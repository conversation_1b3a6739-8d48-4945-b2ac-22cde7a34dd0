# SeekAlpha 股票数据库 API 接口文档

## 概述

本文档描述了 SeekAlpha 股票数据库系统提供的 RESTful API 接口，用于获取股票相关数据。

**基础信息:**
- 服务地址: `http://localhost:40023`
- 协议: HTTP/HTTPS
- 数据格式: JSON
- 编码: UTF-8

---

## 接口列表

### 1. 获取股票合并数据接口

#### 接口信息
- **接口名称**: 获取股票合并数据
- **接口描述**: 获取股票的综合数据，包括行情、筹码分布、资金流向等信息
- **接口地址**: `POST /api/v1/data/combined`
- **支持格式**: JSON

#### 请求参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| stock_list | Array[String] | 是 | - | 股票代码列表，如 ["000001.SZ", "600000.SH"] |
| start_date | String | 是 | - | 开始日期，格式: YYYY-MM-DD |
| end_date | String | 是 | - | 结束日期，格式: YYYY-MM-DD |
| include_quotes | Boolean | 否 | true | 是否包含行情数据 |
| include_chips | Boolean | 否 | true | 是否包含筹码数据 |
| include_money_flow | Boolean | 否 | true | 是否包含资金流数据 |

#### 请求示例

```json
{
    "stock_list": ["000006.SZ", "000008.SZ"],
    "start_date": "2025-05-06",
    "end_date": "2025-05-07",
    "include_quotes": true,
    "include_chips": true,
    "include_money_flow": true
}
```

#### 响应参数

| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | Integer | 响应状态码，0-成功，其他-失败 |
| message | String | 响应消息 |
| data | Object | 股票数据，采用列式存储格式，每个字段名为key，对应所有记录的值组成数组 |

**data 字段说明（行情数据）:**
| 字段名 | 类型 | 说明 |
|--------|------|------|
| instrument_id | Array[String] | 股票代码 |
| trade_date | Array[String] | 交易日期 |
| open | Array[Float] | 开盘价 |
| high | Array[Float] | 最高价 |
| low | Array[Float] | 最低价 |
| close | Array[Float] | 收盘价 |
| volume | Array[Integer] | 成交量（股） |
| amount | Array[Float] | 成交额（元） |
| change | Array[Float] | 涨跌额（元） |
| pct_chg | Array[Float] | 涨跌幅 |
| adj_factor | Array[Float] | 复权因子 |
| adj_open | Array[Float] | 后复权开盘价 |
| adj_high | Array[Float] | 后复权最高价 |
| adj_low | Array[Float] | 后复权最低价 |
| adj_close | Array[Float] | 后复权收盘价 |

**data 字段说明（筹码数据）:**
| 字段名 | 类型 | 说明 |
|--------|------|------|
| his_low | Array[Float] | 历史最低价 |
| his_high | Array[Float] | 历史最高价 |
| cost_5pct | Array[Float] | 5%成本价位 |
| cost_15pct | Array[Float] | 15%成本价位 |
| cost_50pct | Array[Float] | 50%成本价位 |
| cost_85pct | Array[Float] | 85%成本价位 |
| cost_95pct | Array[Float] | 95%成本价位 |
| weight_avg | Array[Float] | 加权平均成本 |
| winner_rate | Array[Float] | 获利盘比例 |
| chip_conct_90 | Array[Float] | 90 筹码集中度% |
| chip_conct_70 | Array[Float] | 70 筹码集中度% |

**data 字段说明（资金流数据）:**
| 字段名 | 类型 | 说明 |
|--------|------|------|
| buy_sm_vol | Array[Integer] | 小单买入量 |
| buy_sm_amount | Array[Float] | 小单买入额 |
| sell_sm_vol | Array[Integer] | 小单卖出量 |
| sell_sm_amount | Array[Float] | 小单卖出额 |
| buy_md_vol | Array[Integer] | 中单买入量 |
| buy_md_amount | Array[Float] | 中单买入额 |
| sell_md_vol | Array[Integer] | 中单卖出量 |
| sell_md_amount | Array[Float] | 中单卖出额 |
| buy_lg_vol | Array[Integer] | 大单买入量 |
| buy_lg_amount | Array[Float] | 大单买入额 |
| sell_lg_vol | Array[Integer] | 大单卖出量 |
| sell_lg_amount | Array[Float] | 大单卖出额 |
| buy_elg_vol | Array[Integer] | 特大单买入量 |
| buy_elg_amount | Array[Float] | 特大单买入额 |
| sell_elg_vol | Array[Integer] | 特大单卖出量 |
| sell_elg_amount | Array[Float] | 特大单卖出额 |
| net_mf_vol | Array[Integer] | 净流入量 |
| net_mf_amount | Array[Float] | 净流入额 |

**流入流出量单位为‘股’，买入卖出额单位为‘元’**
#### 响应示例

**成功响应:**
```json
{
    "code": 0,
    "message": "Success",
    "data": {
        "trade_date": ["2025-05-07", "2025-05-07", "2025-05-06", "2025-05-06"],
        "instrument_id": ["000006.SZ", "000008.SZ", "000006.SZ", "000008.SZ"],
        "open": [6.55, 2.75, 6.32, 2.71],
        "high": [6.66, 2.77, 6.46, 2.74],
        "low": [6.49, 2.72, 6.32, 2.7],
        "close": [6.56, 2.76, 6.46, 2.74],
        "volume": [31805140, 66957099, 23943189, 49111800],
        "amount": [209069577.0, 183846620.0, 153531506.0, 133888208.0],
        "change": [0.1, 0.02, 0.11, 0.05],
        "pct_chg": [0.0155, 0.0073, 0.0173, 0.0186],
        "adj_factor": [39.74, 22.408, 39.74, 22.408],
        "adj_open": [260.297, 61.622, 251.15680000000003, 60.725680000000004],
        "adj_high": [264.6684, 62.07016, 256.7204, 61.397920000000006],
        "adj_low": [257.9126, 60.949760000000005, 251.15680000000003, 60.50160000000001],
        "adj_close": [260.6944, 61.84608, 256.7204, 61.397920000000006],
        "his_low": [3.2, 1.8, 3.2, 1.8],
        "his_high": [10.5, 4.1, 10.5, 4.1],
        "cost_5pct": [5.7, 2.6, 5.7, 2.6],
        "cost_15pct": [6.1, 2.7, 6.1, 2.7],
        "cost_50pct": [6.4, 2.8, 6.4, 2.8],
        "cost_85pct": [7.3, 3.0, 7.3, 3.0],
        "cost_95pct": [7.8, 3.1, 7.8, 3.1],
        "weight_avg": [6.57, 2.82, 6.57, 2.83],
        "winner_rate": [68.0, 43.83, 60.9, 42.21],
        "chip_conct_90": [15.555555555555554, 8.771929824561402, 15.555555555555554, 8.771929824561402],
        "chip_conct_70": [8.955223880597018, 5.263157894736839, 8.955223880597018, 5.263157894736839],
        "buy_sm_vol": [9160700, 18301000, 7821200, 13679300],
        "buy_sm_amount": [60192400.0, 50244400.0, 50179100.0, 37290800.0],
        "sell_sm_vol": [9650500, 19990500, 6882100, 16282700],
        "sell_sm_amount": [63459500.0, 54904700.0, 44173700.0, 44399300.0],
        "buy_md_vol": [12770200, 21758100, 8682900, 18242900],
        "buy_md_amount": [83969200.0, 59720100.0, 55663100.0, 49740000.0],
        "sell_md_vol": [10636400, 24747300, 8479300, 16856400],
        "sell_md_amount": [69976700.0, 67948800.0, 54369600.0, 45955200.0],
        "buy_lg_vol": [8704100, 16083100, 6356200, 13208900],
        "buy_lg_amount": [57231100.0, 44146200.0, 40717400.0, 36015700.0],
        "sell_lg_vol": [9530800, 16628200, 7017900, 9505000],
        "sell_lg_amount": [62631700.0, 45647200.0, 44978000.0, 25900500.0],
        "buy_elg_vol": [1170200, 10814900, 1082900, 3980700],
        "buy_elg_amount": [7676800.0, 29735900.0, 6972000.0, 10841700.0],
        "sell_elg_vol": [1987400, 5591100, 1563900, 6467700],
        "sell_elg_amount": [13001700.0, 15345900.0, 10010300.0, 17633100.0],
        "net_mf_vol": [-268900, 8540100, 1652700, 10151200],
        "net_mf_amount": [-1560100.0, 23786900.0, 10686700.0, 27848700.0]
    }
}
```

**失败响应:**
```json
{
    "code": 1,
    "message": "获取数据失败: 日期格式错误",
    "data": {}
}
```


---

### 2. 获取股票行业信息接口

#### 接口信息
- **接口名称**: 获取股票一级行业信息
- **接口描述**: 根据股票代码列表获取对应的一级行业代码和行业名称
- **接口地址**: `POST /api/v1/data/stock-industry-l1`
- **支持格式**: JSON

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| stock_list | Array[String] | 是 | 股票代码列表，如 ["000001.SZ", "600000.SH"] |

#### 请求示例

```json
{
    "stock_list": ["000006.SZ", "000008.SZ"]
}
```

#### 响应参数

| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | Integer | 响应状态码，0-成功，其他-失败 |
| message | String | 响应消息 |
| data | Array[Object] | 股票行业信息列表 |

**data 字段说明:**
| 字段名 | 类型 | 说明 |
|--------|------|------|
| instrument_id | String | 股票代码 |
| l1_code | String | 一级行业代码 |
| l1_name | String | 一级行业名称 |

#### 响应示例

**成功响应:**
```json
{
  "code": 0,
  "message": "Success",
  "data": [
    {
      "instrument_id": "000006.SZ",
      "l1_code": "801180.SI",
      "l1_name": "房地产"
    },
    {
      "instrument_id": "000008.SZ",
      "l1_code": "801890.SI",
      "l1_name": "机械设备"
    }
  ]
}
```

**失败响应:**
```json
{
    "code": 1,
    "message": "获取数据失败: 股票列表不能为空",
    "data": []
}
```


---

### 3. 获取指数数据接口

#### 接口信息
- **接口名称**: 获取指数数据
- **接口描述**: 根据指数代码列表和日期范围获取指数的行情数据
- **接口地址**: `POST /api/v1/data/index`
- **支持格式**: JSON

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| index_list | Array[String] | 是 | 指数代码列表，如 ["000905.SH", "000300.SH", "399006.SZ"] |
| start_date | String | 是 | 开始日期，格式: YYYY-MM-DD |
| end_date | String | 是 | 结束日期，格式: YYYY-MM-DD |

#### 请求示例

```json
{
    "index_list": ["000905.SH", "000852.SH"],
    "start_date": "2024-12-04",
    "end_date": "2024-12-05"
}
```

#### 响应参数

| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | Integer | 响应状态码，0-成功，其他-失败 |
| message | String | 响应消息 |
| data | Object | 指数数据，采用列式存储格式，每个字段名为key，对应所有记录的值组成数组 |

**data 字段说明（指数数据）:**
| 字段名 | 类型 | 说明 |
|--------|------|------|
| instrument_id | Array[String] | 指数代码 |
| trade_date | Array[String] | 交易日期 |
| open | Array[Float] | 开盘点位 |
| high | Array[Float] | 最高点位 |
| low | Array[Float] | 最低点位 |
| close | Array[Float] | 收盘点位 |
| volume | Array[Integer] | 成交量（股） |
| amount | Array[Float] | 成交额（元） |
| change | Array[Float] | 涨跌额（元） |
| pct_chg | Array[Float] | 涨跌幅 |

#### 响应示例

**成功响应:**
```json
{
    "code": 0,
    "message": "Success",
    "data": {
        "instrument_id": ["000852.SH", "000852.SH", "000905.SH", "000905.SH"],
        "trade_date": ["2024-12-05", "2024-12-04", "2024-12-05", "2024-12-04"],
        "open": [6186.1227, 6301.0479, 5855.7419, 5929.4763],
        "high": [6302.3554, 6303.5046, 5923.6243, 5940.8545],
        "low": [6186.1227, 6175.8385, 5853.527, 5850.9094],
        "close": [6285.6007, 6204.9448, 5901.9859, 5879.2695],
        "volume": [28853561700, 29920209800, 19854223600, 21599163400],
        "amount": [340682850435.0, 351416845598.0, 212251881677.0, 234212942759.0],
        "change": [80.6559, -98.331, 22.7164, -52.8512],
        "pct_chg": [0.013, -0.0156, 0.0039, -0.0089]
    }
}
```

**失败响应:**
```json
{
    "code": 1,
    "message": "获取数据失败: 指数列表不能为空",
    "data": {}
}
```

---

### 4. 获取股票5分钟K线数据接口

#### 接口信息
- **接口名称**: 获取股票5分钟K线数据
- **接口描述**: 根据股票代码和日期范围获取该股票的5分钟K线数据
- **接口地址**: `POST /api/v1/data/stock-5min`
- **支持格式**: JSON

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| stock_code | String | 是 | 股票代码，如 "000001.SZ", "600519.SH" |
| start_date | String | 是 | 开始日期，格式: YYYY-MM-DD |
| end_date | String | 是 | 结束日期，格式: YYYY-MM-DD |

#### 请求示例

```json
{
    "stock_code": "000019.SZ",
    "start_date": "2024-12-04",
    "end_date": "2024-12-04"
}
```

#### 响应参数

| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | Integer | 响应状态码，0-成功，其他-失败 |
| message | String | 响应消息 |
| data | Object | 5分钟K线数据，采用列式存储格式，每个字段名为key，对应所有记录的值组成数组 |

**data 字段说明（5分钟K线数据）:**
| 字段名 | 类型 | 说明 |
|--------|------|------|
| instrument_id | Array[String] | 股票代码 |
| trade_time | Array[String] | 交易时间，格式: YYYY-MM-DD HH:MM:SS |
| open | Array[Float] | 开盘价 |
| high | Array[Float] | 最高价 |
| low | Array[Float] | 最低价 |
| close | Array[Float] | 收盘价 |
| volume | Array[Integer] | 成交量（股） |
| amount | Array[Float] | 成交额（元） |
| trade_date | Array[String] | 交易日期，格式: YYYY-MM-DD |

#### 响应示例

**成功响应:**
```json
{
    "code": 0,
    "message": "Success",
    "data": {
        "instrument_id": ["000019.SZ", "000019.SZ", "000019.SZ"...],
        "trade_time": ["2024-12-04 09:35:00", "2024-12-04 09:40:00", "2024-12-04 09:45:00"...],
        "open": [7.119999885559082, 7.079999923706055, 7.070000171661377...],
        "high": [7.139999866485596, 7.079999923706055, 7.099999904632568...],
        "low": [7.059999942779541, 7.050000190734863, 7.059999942779541...],
        "close": [7.079999923706055, 7.070000171661377, 7.090000152587891...],
        "volume": [836700, 523300, 651100...],
        "amount": [5934861.0, 3698903.0, 4611789.0...],
        "trade_date": ["2024-12-04", "2024-12-04", "2024-12-04"...]
    }
}
```

**失败响应:**
```json
{
    "code": 1,
    "message": "获取数据失败: 股票代码不能为空",
    "data": {}
}
```

#### 特别说明

1. **查询优化**: 接口内部使用 `trade_time` 字段进行查询，将输入的日期范围转换为：
   - 开始时间: `start_date 09:30:00` (开盘时间)
   - 结束时间: `end_date 15:00:00` (收盘时间)
   - 这样设计能充分利用数据库索引 `(instrument_id, trade_time)`，提升查询性能

2. **数据排序**: 返回的数据按交易时间正序排列（从早到晚）

3. **交易时间**: 只返回正常交易时间段（09:30-15:00）的数据，不包括集合竞价等时间段

4. **单股票查询**: 此接口专门用于单个股票的5分钟数据查询，如需查询多个股票请多次调用

---

### 5. 获取板块数据接口

#### 接口信息
- **接口名称**: 获取板块数据
- **接口描述**: 根据板块代码列表和日期范围获取板块的行情数据
- **接口地址**: `POST /api/v1/data/sector`
- **支持格式**: JSON

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| stock_list | Array[String] | 否 | 板块代码列表，如 ["885501", "885502"]，为null表示查询所有板块 |
| start_date | String | 是 | 开始日期，格式: YYYY-MM-DD |
| end_date | String | 是 | 结束日期，格式: YYYY-MM-DD |

#### 请求示例

```json
{
    "stock_list": ["885362.TI", "700003.TI"],
    "start_date": "2025-07-10",
    "end_date": "2025-07-11"
}
```

#### 响应参数

| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | Integer | 响应状态码，0-成功，其他-失败 |
| message | String | 响应消息 |
| data | Object | 板块数据，采用列式存储格式，每个字段名为key，对应所有记录的值组成数组 |

**data 字段说明（板块数据）:**
| 字段名 | 类型 | 说明 |
|--------|------|------|
| instrument_id | Array[String] | 板块代码 |
| trade_date | Array[String] | 交易日期 |
| open | Array[Float] | 开盘点位 |
| high | Array[Float] | 最高点位 |
| low | Array[Float] | 最低点位 |
| close | Array[Float] | 收盘点位 |
| volume | Array[Integer] | 成交量（股） |
| avg_price | Array[Float] | 平均价 |
| change | Array[Float] | 涨跌点位 |
| pct_chg | Array[Float] | 涨跌幅 |
| turnover_rate | Array[Float] | 换手率(%) **注意这里是 '%'** |
| total_mv | Array[Float] | 总市值(元) |
| float_mv | Array[Float] | 流通市值(元) |

#### 响应示例

**成功响应:**
```json
{
    "code": 0,
    "message": "Success",
    "data": {
        "instrument_id": ["700003.TI", "700003.TI", "885362.TI", "885362.TI"],
        "trade_date": ["2025-07-11", "2025-07-10", "2025-07-11", "2025-07-10"],
        "open": [3640.173, 3620.418, 3811.504, 3805.016],
        "high": [3676.662, 3650.771, 3876.662, 3827.63],
        "low": [3632.052, 3617.413, 3782.601, 3781.894],
        "close": [3649.113, 3636.021, 3860.062, 3811.96],
        "volume": [138710975100, 124899300600, 6862599100, 5767438000],
        "avg_price": [11.7116, 11.4006, 16.2632, 15.784],
        "change": [13.092, 15.872, 48.102, -8.175],
        "pct_chg": [0.003601, 0.004384, 0.012619, -0.00214],
        "turnover_rate": [null, 1.799686, null, 2.358915],
        "total_mv": [98554421322911.8, 98554421322911.8, 6254146425727.52, 6254146425727.52],
        "float_mv": [77913485734217.08, 77913485734217.08, 3526524152140.65, 3526524152140.65]
    }
}
```

**失败响应:**
```json
{
    "code": 1,
    "message": "获取数据失败: 日期格式错误",
    "data": {}
}
```

#### 特别说明

1. **板块代码**: 使用同花顺板块代码，如 "700003.TI"、"885362.TI"等
2. **查询范围**: 当 `stock_list` 为 `null` 时，查询所有板块数据
3. **市值单位**: `total_mv` 和 `float_mv` 的单位为元
4. **涨跌幅**: `pct_chg` 为小数形式，如 0.0073 表示 0.73%
5. **换手率(%)**: `turnover_rate` 为百分数形式，1.799686 表示 1.799686%

---

## 交易管理接口

### 6. 插入交易策略接口

#### 接口信息
- **接口名称**: 插入交易策略
- **接口描述**: 新增一个交易策略到系统中
- **接口地址**: `POST /api/v1/trade/strategy`
- **支持格式**: JSON

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| strategy_name | String | 是 | 策略名称，最大200个字符，必须唯一 |
| strategy_desc | String | 否 | 策略描述，最大1000个字符 |
| factor_names | Array[String] | 否 | 因子名称列表 |
| factor_expressions | Array[String] | 否 | 因子表达式列表，需与因子名称数量一致 |
| extra_params | Object | 否 | 额外参数，JSON格式 |

#### 请求示例

```json
{
    "strategy_name": "趋势跟踪策略",
    "strategy_desc": "基于移动平均线的趋势跟踪策略",
    "factor_names": ["MA5", "MA20"],
    "factor_expressions": ["close.rolling(5).mean()", "close.rolling(20).mean()"],
    "extra_params": {
        "start_cash": 1000000,
        "update_freq": 5
    }
}
```

#### 响应参数

| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | Integer | 响应状态码，200-成功，其他-失败 |
| message | String | 响应消息 |


#### 响应示例

**成功响应:**
```json
{
    "code": 200,
    "message": "Success",
    "data": {
        "strategy_id": 1
    }
}
```

**失败响应:**
```json
{
    "code": 500,
    "message": "Strategy name already exists: 趋势跟踪策略"
}
```

---


### 7. 批量插入交易信号接口

#### 接口信息
- **接口名称**: 批量插入交易信号
- **接口描述**: 批量插入多条交易信号记录
- **接口地址**: `POST /api/v1/trade/signals`
- **支持格式**: JSON

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| signals | Array[Object] | 是 | 信号列表，每个对象包含单条信号的所有字段 |

**signals 中每个对象的字段与单条插入接口相同**

#### 请求示例

```json
{
    "signals": [
        {
            "strategy_name": "趋势跟踪策略",
            "timestamp": "2025-01-20 14:30:00",
            "generated_time": "2025-01-20 14:32:15",
            "stock_code": "000001.SZ",
            "order_type": 1,
            "order_volume": 500,
            "last_close_price": 12.45
        },
        {
            "strategy_name": "趋势跟踪策略",
            "timestamp": "2025-01-20 14:35:00",
            "generated_time": "2025-01-20 14:37:20",
            "stock_code": "000002.SZ",
            "order_type": 2,
            "order_volume": 1000,
            "last_close_price": 8.90
        }
    ]
}
```

#### 响应参数

| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | Integer | 响应状态码，200-成功，其他-失败 |
| message | String | 响应消息 |
| data | Object | 响应数据 |

**data 字段说明:**
| 字段名 | 类型 | 说明 |
|--------|------|------|
| total_provided | Integer | 提供的信号总数 |
| successful_inserts | Integer | 成功插入的信号数量 |

#### 响应示例

**成功响应:**
```json
{
    "code": 200,
    "message": "Success",
    "data": {
        "total_provided": 2,
        "successful_inserts": 2
    }
}
```

**部分失败响应:**
```json
{
    "code": 500,
    "message": "Data validation failed\n[\"Signal 1: Strategy not found: 不存在的策略\"]"
}
```

---

### 8. 插入账户概览接口

#### 接口信息
- **接口名称**: 插入账户概览
- **接口描述**: 插入账户资产概览数据
- **接口地址**: `POST /api/v1/trade/account-summary`
- **支持格式**: JSON

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| account_id | String | 是 | 账户ID，最大50个字符 |
| total_asset | Float | 是 | 总资产，非负数 |
| market_value | Float | 是 | 持仓总市值，非负数 |
| cash | Float | 是 | 可用现金，非负数 |
| timestamp | String | 是 | 数据快照时间，格式：YYYY-MM-DD HH:MM:SS |

#### 请求示例

```json
{
    "account_id": "account_001",
    "total_asset": 1000000.50,
    "market_value": 800000.30,
    "cash": 200000.20,
    "timestamp": "2025-01-20 15:30:00"
}
```

#### 响应参数

| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | Integer | 响应状态码，200-成功，其他-失败 |
| message | String | 响应消息 |

#### 响应示例

**成功响应:**
```json
{
    "code": 200,
    "message": "Success"
}
```

---

### 9. 批量插入账户持仓接口

#### 接口信息
- **接口名称**: 批量插入账户持仓
- **接口描述**: 批量插入一个账户的多个股票持仓明细数据
- **接口地址**: `POST /api/v1/trade/account-positions`
- **支持格式**: JSON

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| account_id | String | 是 | 账户ID，最大50个字符 |
| positions | Array[Object] | 是 | 持仓列表，每个对象包含单个持仓的详细信息 |
| timestamp | String | 是 | 数据快照时间，格式：YYYY-MM-DD HH:MM:SS |

**positions 中每个对象的字段:**
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| stock_code | String | 是 | 股票代码，如：000001.SZ |
| volume | Integer | 是 | 持仓股票数量，非负整数 |
| can_use_volume | Integer | 是 | 可用股票数量，非负整数 |
| frozen_volume | Integer | 是 | 冻结股票数量，非负整数 |
| open_price | Float | 是 | 成本价，正数 |
| avg_price | Float | 是 | 平均成本价，正数 |
| market_value | Float | 是 | 持仓市值，非负数 |

#### 请求示例

```json
{
    "account_id": "account_001",
    "positions": [
        {
            "stock_code": "000001.SZ",
            "volume": 1000,
            "can_use_volume": 800,
            "frozen_volume": 200,
            "open_price": 12.50,
            "avg_price": 12.35,
            "market_value": 12350.00
        },
        {
            "stock_code": "000002.SZ",
            "volume": 500,
            "can_use_volume": 500,
            "frozen_volume": 0,
            "open_price": 8.20,
            "avg_price": 8.15,
            "market_value": 4075.00
        }
    ],
    "timestamp": "2025-01-20 15:30:00"
}
```

#### 响应参数

| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | Integer | 响应状态码，200-成功，其他-失败 |
| message | String | 响应消息 |
| data | Object | 响应数据 |

**data 字段说明:**
| 字段名 | 类型 | 说明 |
|--------|------|------|
| total_provided | Integer | 提供的持仓记录总数 |
| successful_inserts | Integer | 成功插入的持仓记录数量 |

#### 响应示例

**成功响应:**
```json
{
    "code": 200,
    "message": "Success",
    "data": {
        "total_provided": 2,
        "successful_inserts": 2
    }
}
```

**失败响应:**
```json
{
    "code": 500,
    "message": "Data validation failed"
}
```

---

### 10. 根据策略名称查询信号接口

#### 接口信息
- **接口名称**: 根据策略名称查询信号
- **接口描述**: 根据策略名称查询该策略产生的交易信号，支持分页查询
- **接口地址**: `GET /api/v1/trade/signals/by-strategy`
- **支持格式**: JSON

#### 请求参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| strategy_name | String | 是 | - | 策略名称 |
| page | Integer | 否 | null | 页码，从1开始。为null时返回全部数据 |
| page_size | Integer | 否 | null | 每页记录数。为null时返回全部数据 |

#### 请求示例

```bash
# 分页查询
GET /api/v1/trade/signals/by-strategy?strategy_name=趋势跟踪策略&page=1&page_size=10

# 查询全部数据（不分页）
GET /api/v1/trade/signals/by-strategy?strategy_name=趋势跟踪策略
```

#### 响应参数

| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | Integer | 响应状态码，0-成功，其他-失败 |
| message | String | 响应消息 |
| data | Object | 响应数据 |

**data 字段说明:**
| 字段名 | 类型 | 说明 |
|--------|------|------|
| items | Array[Object] | 信号列表 |
| pagination | Object | 分页信息 |

**pagination 字段说明:**
| 字段名 | 类型 | 说明 |
|--------|------|------|
| page | Integer/null | 当前页码，无分页时为null |
| page_size | Integer/null | 每页记录数，无分页时为null |
| total | Integer | 总记录数 |
| is_paginated | Boolean | 是否进行了分页 |

**items 中每个对象的字段:**
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | Integer | 信号ID |
| strategy_name | String | 策略名称 |
| timestamp | String | 信号时间戳 |
| generated_time | String | 信号生成时间 |
| stock_code | String | 股票代码 |
| order_type | Integer | 订单类型（1-买入，2-卖出） |
| order_volume | Integer | 订单数量 |
| last_close_price | Float | 上一收盘价 |

#### 响应示例

**成功响应（分页）:**
```json
{
    "code": 0,
    "message": "Success",
    "data": {
        "items": [
            {
                "id": 1,
                "strategy_name": "趋势跟踪策略",
                "timestamp": "2025-01-20 14:30:00",
                "generated_time": "2025-01-20 14:32:15",
                "stock_code": "000001.SZ",
                "order_type": 1,
                "order_volume": 500,
                "last_close_price": 12.45
            }
        ],
        "pagination": {
            "page": 1,
            "page_size": 10,
            "total": 25,
            "is_paginated": true
        }
    }
}
```

**成功响应（无分页）:**
```json
{
    "code": 0,
    "message": "Success",
    "data": {
        "items": [
            {
                "id": 1,
                "strategy_name": "趋势跟踪策略",
                "timestamp": "2025-01-20 14:30:00",
                "generated_time": "2025-01-20 14:32:15",
                "stock_code": "000001.SZ",
                "order_type": 1,
                "order_volume": 500,
                "last_close_price": 12.45
            }
        ],
        "pagination": {
            "page": null,
            "page_size": null,
            "total": 25,
            "is_paginated": false
        }
    }
}
```

---

### 11. 根据策略名称和日期查询信号接口

#### 接口信息
- **接口名称**: 根据策略名称和日期查询信号
- **接口描述**: 根据策略名称和交易日期查询该策略在指定日期产生的交易信号，支持分页查询
- **接口地址**: `GET /api/v1/trade/signals/by-strategy-date`
- **支持格式**: JSON

#### 请求参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| strategy_name | String | 是 | - | 策略名称 |
| trade_date | String | 是 | - | 交易日期，格式：YYYY-MM-DD |
| page | Integer | 否 | null | 页码，从1开始。为null时返回全部数据 |
| page_size | Integer | 否 | null | 每页记录数。为null时返回全部数据 |

#### 请求示例

```bash
# 分页查询
GET /api/v1/trade/signals/by-strategy-date?strategy_name=趋势跟踪策略&trade_date=2025-01-20&page=1&page_size=10

# 查询全部数据（不分页）
GET /api/v1/trade/signals/by-strategy-date?strategy_name=趋势跟踪策略&trade_date=2025-01-20
```

#### 响应参数

响应格式与"根据策略名称查询信号接口"相同。

---

### 12. 查询账户概览接口

#### 接口信息
- **接口名称**: 查询账户概览
- **接口描述**: 根据账户ID查询账户资产概览数据，支持按日期筛选和分页查询
- **接口地址**: `GET /api/v1/trade/account-summary`
- **支持格式**: JSON

#### 请求参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| account_id | String | 是 | - | 账户ID |
| date | String | 否 | null | 查询日期，格式：YYYY-MM-DD。为null时查询所有日期 |
| page | Integer | 否 | null | 页码，从1开始。为null时返回全部数据 |
| page_size | Integer | 否 | null | 每页记录数。为null时返回全部数据 |

#### 请求示例

```bash
# 分页查询指定日期
GET /api/v1/trade/account-summary?account_id=account_001&date=2025-01-20&page=1&page_size=10

# 查询全部数据（不分页）
GET /api/v1/trade/account-summary?account_id=account_001
```

#### 响应参数

| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | Integer | 响应状态码，0-成功，其他-失败 |
| message | String | 响应消息 |
| data | Object | 响应数据 |

**data 字段说明:**
| 字段名 | 类型 | 说明 |
|--------|------|------|
| items | Array[Object] | 账户概览列表 |
| pagination | Object | 分页信息（格式同上） |

**items 中每个对象的字段:**
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | Integer | 记录ID |
| account_id | String | 账户ID |
| total_asset | Float | 总资产 |
| market_value | Float | 持仓总市值 |
| cash | Float | 可用现金 |
| timestamp | String | 数据快照时间 |

---

### 13. 查询账户持仓接口

#### 接口信息
- **接口名称**: 查询账户持仓
- **接口描述**: 根据账户ID查询账户持仓明细数据，支持按日期筛选和分页查询
- **接口地址**: `GET /api/v1/trade/account-positions`
- **支持格式**: JSON

#### 请求参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| account_id | String | 是 | - | 账户ID |
| date | String | 否 | null | 查询日期，格式：YYYY-MM-DD。为null时查询所有日期 |
| page | Integer | 否 | null | 页码，从1开始。为null时返回全部数据 |
| page_size | Integer | 否 | null | 每页记录数。为null时返回全部数据 |

#### 请求示例

```bash
# 分页查询指定日期
GET /api/v1/trade/account-positions?account_id=account_001&date=2025-01-20&page=1&page_size=10

# 查询全部数据（不分页）
GET /api/v1/trade/account-positions?account_id=account_001
```

#### 响应参数

| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | Integer | 响应状态码，0-成功，其他-失败 |
| message | String | 响应消息 |
| data | Object | 响应数据 |

**data 字段说明:**
| 字段名 | 类型 | 说明 |
|--------|------|------|
| items | Array[Object] | 持仓明细列表 |
| pagination | Object | 分页信息（格式同上） |

**items 中每个对象的字段:**
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | Integer | 记录ID |
| account_id | String | 账户ID |
| stock_code | String | 股票代码 |
| volume | Integer | 持仓股票数量 |
| can_use_volume | Integer | 可用股票数量 |
| frozen_volume | Integer | 冻结股票数量 |
| open_price | Float | 成本价 |
| avg_price | Float | 平均成本价 |
| market_value | Float | 持仓市值 |
| timestamp | String | 数据快照时间 |

---

### 14. 根据策略名称查询策略接口

#### 接口信息
- **接口名称**: 根据策略名称查询策略
- **接口描述**: 根据策略名称查询策略详细信息
- **接口地址**: `GET /api/v1/trade/strategy/by-name`
- **支持格式**: JSON

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| strategy_name | String | 是 | 策略名称 |

#### 请求示例

```bash
GET /api/v1/trade/strategy/by-name?strategy_name=趋势跟踪策略
```

#### 响应参数

| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | Integer | 响应状态码，0-成功，其他-失败 |
| message | String | 响应消息 |
| data | Object | 策略详细信息 |

**data 字段说明:**
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | Integer | 策略ID |
| strategy_name | String | 策略名称 |
| strategy_desc | String | 策略描述 |
| factor_names | Array[String] | 因子名称列表 |
| factor_expressions | Array[String] | 因子表达式列表 |
| extra_params | Object | 额外参数 |
| created_at | String | 创建时间 |
| updated_at | String | 更新时间 |

---

### 15. 根据策略ID查询策略接口

#### 接口信息
- **接口名称**: 根据策略ID查询策略
- **接口描述**: 根据策略ID查询策略详细信息
- **接口地址**: `GET /api/v1/trade/strategy/by-id`
- **支持格式**: JSON

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| strategy_id | Integer | 是 | 策略ID |

#### 请求示例

```bash
GET /api/v1/trade/strategy/by-id?strategy_id=1
```

#### 响应参数

响应格式与"根据策略名称查询策略接口"相同。

---

## 分页查询说明

### 分页参数说明

所有支持分页的查询接口都遵循以下规则：

1. **page 参数**：
   - 类型：Integer 或 null
   - 默认值：null
   - 说明：页码，从1开始。为null时返回全部数据，不进行分页

2. **page_size 参数**：
   - 类型：Integer 或 null
   - 默认值：null
   - 说明：每页记录数。为null时返回全部数据，不进行分页

3. **分页逻辑**：
   - 当 `page` 或 `page_size` 任一参数为 `null` 时，返回全部匹配的数据
   - 当两个参数都不为 `null` 时，进行分页查询
   - 不传递参数等同于传递 `null`

### 分页响应格式

所有分页接口的响应都包含 `pagination` 对象：

```json
{
    "pagination": {
        "page": 1,              // 当前页码，无分页时为null
        "page_size": 10,        // 每页记录数，无分页时为null
        "total": 25,            // 总记录数
        "is_paginated": true    // 是否进行了分页
    }
}
```

### 使用示例

```python
# Python SDK 中的查询接口示例
def get_signals_by_strategy(self, strategy_name, page=None, page_size=None):
    """根据策略名称查询信号"""
    url = f"{self.base_url}/api/v1/trade/signals/by-strategy"
    params = {"strategy_name": strategy_name}

    if page is not None:
        params["page"] = page
    if page_size is not None:
        params["page_size"] = page_size

    response = requests.get(url, params=params)
    return response.json()

# 使用示例
api = SeekAlphaDatabaseAPI()

# 分页查询
result1 = api.get_signals_by_strategy("趋势跟踪策略", page=1, page_size=10)

# 查询全部数据
result2 = api.get_signals_by_strategy("趋势跟踪策略")
```

---

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 1 | 业务逻辑错误 |
| 500 | 服务器内部错误 |

## 注意事项

1. **股票代码格式**: 必须包含交易所后缀，如 `.SZ`（深交所）、`.SH`（上交所）
2. **指数代码格式**: 必须包含交易所后缀，如 `.SH`（上交所）、`.SZ`（深交所）
3. **日期格式**: 统一使用 `YYYY-MM-DD` 格式
4. **缓存机制**: 股票合并数据和指数数据均支持缓存机制，频繁请求相同数据会从内存缓存中获取，大幅提升响应速度
5. **数据时效性**: 行业分类数据会定期更新，接口返回最新的分类信息
6. **空值处理**: 部分字段可能为空值（null），请在业务逻辑中做好处理
7. **分页查询**:
   - 查询接口支持灵活的分页机制，当 `page` 或 `page_size` 参数为 `null` 时，返回全部数据
   - 不传递分页参数等同于传递 `null`，即返回全部数据
   - 分页从第1页开始计数
   - 响应中的 `is_paginated` 字段指示是否进行了分页处理

## Python SDK 示例

```python
import requests
import json

class SeekAlphaDatabaseAPI:
    def __init__(self, base_url="http://localhost:40023"):
        self.base_url = base_url
    
    def get_combined_data(self, stock_list, start_date, end_date, 
                         include_quotes=True, include_chips=True, include_money_flow=True):
        """获取股票合并数据"""
        url = f"{self.base_url}/api/v1/data/combined"
        data = {
            "stock_list": stock_list,
            "start_date": start_date,
            "end_date": end_date,
            "include_quotes": include_quotes,
            "include_chips": include_chips,
            "include_money_flow": include_money_flow
        }
        
        response = requests.post(url, json=data)
        return response.json()
    
    def get_stock_industry_l1(self, stock_list):
        """获取股票一级行业信息"""
        url = f"{self.base_url}/api/v1/data/stock-industry-l1"
        data = {"stock_list": stock_list}
        
        response = requests.post(url, json=data)
        return response.json()
    
    def get_index_data(self, index_list, start_date, end_date):
        """获取指数数据"""
        url = f"{self.base_url}/api/v1/data/index"
        data = {
            "index_list": index_list,
            "start_date": start_date,
            "end_date": end_date
        }
        
        response = requests.post(url, json=data)
        return response.json()
    
    def get_stock_5min_data(self, stock_code, start_date, end_date):
        """获取股票5分钟K线数据"""
        url = f"{self.base_url}/api/v1/data/stock-5min"
        data = {
            "stock_code": stock_code,
            "start_date": start_date,
            "end_date": end_date
        }
        
        response = requests.post(url, json=data)
        return response.json()
    
    def get_sector_data(self, stock_list, start_date, end_date):
        """获取板块数据"""
        url = f"{self.base_url}/api/v1/data/sector"
        data = {
            "stock_list": stock_list,
            "start_date": start_date,
            "end_date": end_date
        }
        
        response = requests.post(url, json=data)
        return response.json()
    
    # 交易管理接口
    def insert_trade_strategy(self, strategy_name, strategy_desc=None, 
                             factor_names=None, factor_expressions=None, extra_params=None):
        """插入交易策略"""
        url = f"{self.base_url}/api/v1/trade/strategy"
        data = {
            "strategy_name": strategy_name,
            "strategy_desc": strategy_desc,
            "factor_names": factor_names,
            "factor_expressions": factor_expressions,
            "extra_params": extra_params
        }
        
        response = requests.post(url, json=data)
        return response.json()

    
    def insert_trade_signals(self, signals):
        """批量插入交易信号"""
        url = f"{self.base_url}/api/v1/trade/signals"
        data = {"signals": signals}
        
        response = requests.post(url, json=data)
        return response.json()
    
    def insert_account_summary(self, account_id, total_asset, market_value, cash, timestamp):
        """插入账户概览"""
        url = f"{self.base_url}/api/v1/trade/account-summary"
        data = {
            "account_id": account_id,
            "total_asset": total_asset,
            "market_value": market_value,
            "cash": cash,
            "timestamp": timestamp
        }
        
        response = requests.post(url, json=data)
        return response.json()
    
    def insert_account_positions(self, account_id, positions, timestamp):
        """批量插入账户持仓"""
        url = f"{self.base_url}/api/v1/trade/account-positions"
        data = {
            "account_id": account_id,
            "positions": positions,
            "timestamp": timestamp
        }
        
        response = requests.post(url, json=data)
        return response.json()

    # 查询接口
    def get_signals_by_strategy(self, strategy_name, page=None, page_size=None):
        """根据策略名称查询信号"""
        url = f"{self.base_url}/api/v1/trade/signals/by-strategy"
        params = {"strategy_name": strategy_name}

        if page is not None:
            params["page"] = page
        if page_size is not None:
            params["page_size"] = page_size

        response = requests.get(url, params=params)
        return response.json()

    def get_signals_by_strategy_and_date(self, strategy_name, trade_date, page=None, page_size=None):
        """根据策略名称和日期查询信号"""
        url = f"{self.base_url}/api/v1/trade/signals/by-strategy-date"
        params = {
            "strategy_name": strategy_name,
            "trade_date": trade_date
        }

        if page is not None:
            params["page"] = page
        if page_size is not None:
            params["page_size"] = page_size

        response = requests.get(url, params=params)
        return response.json()

    def get_account_summary(self, account_id, date=None, page=None, page_size=None):
        """查询账户概览"""
        url = f"{self.base_url}/api/v1/trade/account-summary"
        params = {"account_id": account_id}

        if date is not None:
            params["date"] = date
        if page is not None:
            params["page"] = page
        if page_size is not None:
            params["page_size"] = page_size

        response = requests.get(url, params=params)
        return response.json()

    def get_account_positions(self, account_id, date=None, page=None, page_size=None):
        """查询账户持仓"""
        url = f"{self.base_url}/api/v1/trade/account-positions"
        params = {"account_id": account_id}

        if date is not None:
            params["date"] = date
        if page is not None:
            params["page"] = page
        if page_size is not None:
            params["page_size"] = page_size

        response = requests.get(url, params=params)
        return response.json()

    def get_strategy_by_name(self, strategy_name):
        """根据策略名称查询策略"""
        url = f"{self.base_url}/api/v1/trade/strategy/by-name"
        params = {"strategy_name": strategy_name}

        response = requests.get(url, params=params)
        return response.json()

    def get_strategy_by_id(self, strategy_id):
        """根据策略ID查询策略"""
        url = f"{self.base_url}/api/v1/trade/strategy/by-id"
        params = {"strategy_id": strategy_id}

        response = requests.get(url, params=params)
        return response.json()

# 使用示例
api = SeekAlphaDatabaseAPI()

# 获取合并数据
result1 = api.get_combined_data(
    stock_list=["000006.SZ", "000008.SZ"],
    start_date="2025-05-06",
    end_date="2025-05-07"
)

# 获取行业信息
result2 = api.get_stock_industry_l1(["000006.SZ", "000008.SZ"])

# 获取指数数据
result3 = api.get_index_data(
    index_list=["000905.SH", "000852.SH"],
    start_date="2024-12-01",
    end_date="2024-12-05"
)

# 获取5分钟K线数据
result4 = api.get_stock_5min_data(
    stock_code="000019.SZ",
    start_date="2024-12-04",
    end_date="2024-12-04"
)

# 获取板块数据
result5 = api.get_sector_data(
    stock_list=["885362.TI", "700003.TI"],
    start_date="2025-07-10",
    end_date="2025-07-11"
)

# 交易管理接口示例
# 插入策略
strategy_result = api.insert_trade_strategy(
    strategy_name="测试策略",
    strategy_desc="这是一个测试策略",
    factor_names=["MA5", "MA20"],
    factor_expressions=["close.rolling(5).mean()", "close.rolling(20).mean()"],
    extra_params= {
        "start_cash": 1000000,
        "update_freq": 5
    }

)

# 批量插入信号
batch_signals = [
    {
        "strategy_name": "测试策略",
        "timestamp": "2025-01-20 14:30:00",
        "generated_time": "2025-01-20 14:32:15",
        "stock_code": "000001.SZ",
        "order_type": 1,
        "order_volume": 500,
        "last_close_price": 12.45
    }
]
signal_result = api.insert_trade_signals(batch_signals)

# 插入账户概览
summary_result = api.insert_account_summary(
    account_id="account_001",
    total_asset=1000000.50,
    market_value=800000.30,
    cash=200000.20,
    timestamp="2025-01-20 15:30:00"
)

# 批量插入账户持仓
positions = [
    {
        "stock_code": "000001.SZ",
        "volume": 1000,
        "can_use_volume": 800,
        "frozen_volume": 200,
        "open_price": 12.50,
        "avg_price": 12.35,
        "market_value": 12350.00
    },
    {
        "stock_code": "000002.SZ",
        "volume": 500,
        "can_use_volume": 500,
        "frozen_volume": 0,
        "open_price": 8.20,
        "avg_price": 8.15,
        "market_value": 4075.00
    }
]
position_result = api.insert_account_positions(
    account_id="account_001",
    positions=positions,
    timestamp="2025-01-20 15:30:00"
)

# 查询接口示例
# 查询策略信号（分页）
signals_paged = api.get_signals_by_strategy("测试策略", page=1, page_size=10)

# 查询策略信号（全部数据）
signals_all = api.get_signals_by_strategy("测试策略")

# 根据策略和日期查询信号
signals_by_date = api.get_signals_by_strategy_and_date("测试策略", "2025-01-20")

# 查询账户概览
account_summary = api.get_account_summary("account_001", date="2025-01-20")

# 查询账户持仓
account_positions = api.get_account_positions("account_001")

# 查询策略信息
strategy_info = api.get_strategy_by_name("测试策略")

print("合并数据:", json.dumps(result1, indent=2, ensure_ascii=False))
print("行业信息:", json.dumps(result2, indent=2, ensure_ascii=False))
print("指数数据:", json.dumps(result3, indent=2, ensure_ascii=False))
print("5分钟K线数据:", json.dumps(result4, indent=2, ensure_ascii=False))
print("板块数据:", json.dumps(result5, indent=2, ensure_ascii=False))
print("策略插入结果:", json.dumps(strategy_result, indent=2, ensure_ascii=False))
print("信号插入结果:", json.dumps(signal_result, indent=2, ensure_ascii=False))
print("账户概览结果:", json.dumps(summary_result, indent=2, ensure_ascii=False))
print("持仓插入结果:", json.dumps(position_result, indent=2, ensure_ascii=False))
print("信号查询结果:", json.dumps(signals_paged, indent=2, ensure_ascii=False))
print("账户概览查询:", json.dumps(account_summary, indent=2, ensure_ascii=False))
print("策略信息查询:", json.dumps(strategy_info, indent=2, ensure_ascii=False))
``` 