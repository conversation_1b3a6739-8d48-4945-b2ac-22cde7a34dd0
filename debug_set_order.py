import json
from typing import List

def load_stock_codes_from_json_with_set(stock_codes_path: str) -> List[str]:
    """使用set去重的版本（可能导致顺序不一致）"""
    with open(stock_codes_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    stock_codes = list(set(data.get('stock_codes', [])))
    return stock_codes

def load_stock_codes_from_json_preserve_order(stock_codes_path: str) -> List[str]:
    """保持原始顺序去重的版本"""
    with open(stock_codes_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 使用dict.fromkeys()来去重并保持顺序
    stock_codes = list(dict.fromkeys(data.get('stock_codes', [])))
    return stock_codes

# 测试
file_path = '/home/<USER>/SeekAlpha-Stock-Database/.cache/sector_codes.json'

print("测试使用set去重（可能顺序不一致）:")
for i in range(5):
    result = load_stock_codes_from_json_with_set(file_path)
    print(f"第{i+1}次: {result[:3]}")

print("\n测试保持原始顺序去重:")
for i in range(5):
    result = load_stock_codes_from_json_preserve_order(file_path)
    print(f"第{i+1}次: {result[:3]}")

print("\n原始JSON文件中的前3个代码:")
with open(file_path, 'r', encoding='utf-8') as f:
    data = json.load(f)
    print(data['stock_codes'][:3]) 