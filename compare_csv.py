#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSV文件比较脚本
比较两个CSV文件中指定字段值相同的行数
"""

import pandas as pd
import sys
import argparse
from pathlib import Path


def compare_csv_files(file1_path, file2_path, field_name):
    """
    比较两个CSV文件中指定字段值相同的行数（逐行比较）
    
    Args:
        file1_path (str): 第一个CSV文件路径（未复权数据）
        file2_path (str): 第二个CSV文件路径（后复权数据）
        field_name (str): 要比较的字段名
    """
    try:
        # 读取CSV文件
        print(f"正在读取文件: {file1_path}")
        df1 = pd.read_csv(file1_path)
        
        print(f"正在读取文件: {file2_path}")
        df2 = pd.read_csv(file2_path)
        
        # 打印总行数
        print(f"\n文件统计:")
        print(f"文件1 ({Path(file1_path).name}) 总行数: {len(df1)}")
        print(f"文件2 ({Path(file2_path).name}) 总行数: {len(df2)}")
        
        # 检查行数是否相同
        if len(df1) != len(df2):
            print(f"错误: 两个文件的行数不相同！文件1: {len(df1)}行，文件2: {len(df2)}行")
            return
        
        # 检查字段是否存在
        if field_name not in df1.columns:
            print(f"错误: 字段 '{field_name}' 在文件1中不存在")
            print(f"文件1可用字段: {list(df1.columns)}")
            return
            
        if field_name not in df2.columns:
            print(f"错误: 字段 '{field_name}' 在文件2中不存在")
            print(f"文件2可用字段: {list(df2.columns)}")
            return
        
        # 逐行比较指定字段的值
        same_count = 0
        total_rows = len(df1)
        
        for i in range(total_rows):
            value1 = df1.iloc[i][field_name]
            value2 = df2.iloc[i][field_name]
            
            # 处理NaN值的比较
            if pd.isna(value1) and pd.isna(value2):
                same_count += 1
            elif not pd.isna(value1) and not pd.isna(value2) and value1 == value2:
                same_count += 1
        
        print(f"\n比较结果:")
        print(f"字段 '{field_name}' 相同的行数: {same_count}")
        print(f"字段 '{field_name}' 不同的行数: {total_rows - same_count}")
        print(f"相同比例: {same_count/total_rows*100:.2f}%")
                
    except FileNotFoundError as e:
        print(f"错误: 找不到文件 - {e}")
    except pd.errors.EmptyDataError:
        print("错误: CSV文件为空")
    except Exception as e:
        print(f"错误: {e}")


def main():
    parser = argparse.ArgumentParser(
        description="比较两个CSV文件中指定字段值相同的行数",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python compare_csv.py file1.csv file2.csv ts_code
  python compare_csv.py 未复权.csv 后复权.csv trade_date
        """
    )
    
    parser.add_argument('--file1', default='.cache/stock_data/market/no_adj/D/600511_SH.csv', help='第一个CSV文件路径（未复权数据）')
    parser.add_argument('--file2', default='.cache/stock_data/market/hfq/D/600511_SH.csv', help='第二个CSV文件路径（后复权数据）')
    parser.add_argument('--field', default='amount', help='要比较的字段名')
    
    
    args = parser.parse_args()
    
    # 检查文件是否存在
    if not Path(args.file1).exists():
        print(f"错误: 文件不存在 - {args.file1}")
        return
        
    if not Path(args.file2).exists():
        print(f"错误: 文件不存在 - {args.file2}")
        return
    
    print("=" * 50)
    print("CSV文件比较工具")
    print("=" * 50)
    
    compare_csv_files(args.file1, args.file2, args.field)


if __name__ == "__main__":
    main() 