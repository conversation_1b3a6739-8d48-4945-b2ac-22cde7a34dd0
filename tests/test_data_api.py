"""
API测试脚本
"""
import requests
import json
import traceback
import time
# URL = "http://localhost:40042"
URL = "https://*************:7032"

STOCK_LIST = json.load(open(".cache/zz500_all_stocks_2013_2025.json", "r"))


def test_health_api():
    """测试健康检查接口"""
    url = f"{URL}/api/v1/data/health"
    response = requests.get(url, timeout=30)
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.json()}")

def test_combined_data_api():
    """测试合并数据接口"""
    
    url = f"{URL}/api/v1/data/combined"
    
    # stock_codes = STOCK_LIST["stock_codes"][:2]

    stock_codes = ['000869.SZ','603938.SH',]
    # 测试数据
    test_data = {
        "stock_list": stock_codes,
        "start_date": "2025-07-01",
        "end_date": "2025-07-02",
        "include_quotes": True,
        "include_chips": True,
        "include_money_flow": True
    }
    
    try:
        print("🚀 测试合并数据接口...")
        start_time = time.time()
        response = requests.post(url, json=test_data, timeout=120)
        end_time = time.time()
        print(f"请求时间: {end_time - start_time}秒")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"响应码: {data.get('code')}")
            print(f"消息: {data.get('message')}")
            
            # 打印前几条数据
            if data.get('data'):
                data = data['data']
                print(f"len(data): {len(data)}")
                print("\n前3条数据:")
                for col in data.keys():
                    print(f"  {col}: {data[col][:3]}")
                    # print(f"  {col}: {data[col]}")
            
            import pickle
            with open("/home/<USER>/SeekAlphaTrader/.cache/cached_中证1000_combine_df_2021-01-01_2025-07-01.pkl", "wb") as f:
                pickle.dump(data, f)
        else:
            print(f"请求失败: {response.text}")
            
    except Exception as e:
        traceback.print_exc()
        print(f"测试失败: {e}")

def test_stock_industry_l1_api():
    """测试股票一级行业信息接口"""
    url = f"{URL}/api/v1/data/stock-industry-l1"
    
    # 测试数据 - 使用一些常见的股票代码
    test_data = {
        "stock_list": STOCK_LIST["stock_codes"]
    }
    
    try:
        print("🚀 测试股票一级行业信息接口...")
        response = requests.post(url, json=test_data, timeout=30)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"响应码: {data.get('code')}")
            print(f"消息: {data.get('message')}")
            print(f"数据条数: {len(data.get('data', []))}")
            
            # 打印所有数据
            if data.get('data'):
                print("\n股票行业信息:")
                for record in data['data']:
                    stock_id = record.get('instrument_id', 'N/A')
                    l1_code = record.get('l1_code', 'N/A')
                    l1_name = record.get('l1_name', 'N/A')
                    print(f"  股票: {stock_id}, 一级行业代码: {l1_code}, 一级行业名称: {l1_name}")
            else:
                print("  无数据返回")
        else:
            print(f"请求失败: {response.text}")
            
    except Exception as e:
        print(f"测试失败: {e}")


def test_index_data_api():
    """测试指数数据接口"""
    url = f"{URL}/api/v1/data/index"
    
    # 测试数据
    test_data = {
        "index_list": ["000905.SH", "000852.SH"],
        "start_date": "2024-12-04",
        "end_date": "2024-12-05"
    }
    
    try:
        print("🚀 测试指数数据接口...")
        start_time = time.time()
        response = requests.post(url, json=test_data, timeout=120)
        end_time = time.time()
        print(f"请求时间: {end_time - start_time}秒")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"响应码: {data.get('code')}")
            print(f"消息: {data.get('message')}")
            
            # 打印前几条数据
            if data.get('data'):
                data = data['data']
                print(data)
                # print(f"len(data): {len(data)}")
                # print("\n前3条数据:")
                # for col in data.keys():
                #     print(f"  {col}: {data[col][:3] if len(data[col]) > 3 else data[col]}")
        else:
            print(f"请求失败: {response.text}")
            
    except Exception as e:
        traceback.print_exc()
        print(f"测试失败: {e}")

def test_empty_stock_list():
    """测试空股票列表的情况"""
    url = f"{URL}/api/v1/data/stock-industry-l1"
    
    test_data = {
        "stock_list": []
    }
    
    try:
        print("\n🧪 测试空股票列表...")
        response = requests.post(url, json=test_data, timeout=30)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"响应码: {data.get('code')}")
            print(f"消息: {data.get('message')}")
        else:
            print(f"请求失败: {response.text}")
            
    except Exception as e:
        print(f"测试失败: {e}")

def test_empty_index_list():
    """测试空指数列表的情况"""
    url = f"{URL}/api/v1/data/index"
    
    test_data = {
        "index_list": [],
        "start_date": "2024-12-01",
        "end_date": "2024-12-05"
    }
    
    try:
        print("\n🧪 测试空指数列表...")
        response = requests.post(url, json=test_data, timeout=30)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"响应码: {data.get('code')}")
            print(f"消息: {data.get('message')}")
        else:
            print(f"请求失败: {response.text}")
            
    except Exception as e:
        print(f"测试失败: {e}")

def test_stock_5min_data_api():
    """测试股票5分钟K线数据接口"""
    url = f"{URL}/api/v1/data/stock-5min"
    
    # 测试数据
    test_data = {
        "stock_code": "000019.SZ",
        "start_date": "2024-12-04",
        "end_date": "2024-12-04"
    }
    
    try:
        print("🚀 测试股票5分钟K线数据接口...")
        start_time = time.time()
        response = requests.post(url, json=test_data, timeout=120)
        end_time = time.time()
        print(f"请求时间: {end_time - start_time}秒")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"响应码: {data.get('code')}")
            print(f"消息: {data.get('message')}")
            
            # 打印数据统计信息
            if data.get('data'):
                data_content = data['data']
                if data_content:
                    # 获取数据长度
                    first_key = list(data_content.keys())[0]
                    data_length = len(data_content[first_key])
                    print(f"数据条数: {data_length}")
                    
                    print("\n字段列表:")
                    for col in data_content.keys():
                        print(f"  {col}")
                    
                    print("\n前3条数据样例:")
                    for col in ['instrument_id', 'trade_time', 'open', 'high', 'low', 'close', 'volume', 'amount']:
                        if col in data_content:
                            values = data_content[col][:3]
                            print(f"  {col}: {values}")
                else:
                    print("返回数据为空")
        else:
            print(f"请求失败: {response.text}")
            
    except Exception as e:
        traceback.print_exc()
        print(f"测试失败: {e}")

def test_stock_5min_data_invalid_params():
    """测试股票5分钟K线数据接口 - 无效参数"""
    url = f"{URL}/api/v1/data/stock-5min"
    
    # 测试空股票代码
    test_cases = [
        {
            "name": "空股票代码",
            "data": {
                "stock_code": "",
                "start_date": "2024-12-04",
                "end_date": "2024-12-05"
            }
        },
        {
            "name": "缺少开始日期",
            "data": {
                "stock_code": "000001.SZ",
                "end_date": "2024-12-05"
            }
        },
        {
            "name": "缺少结束日期",
            "data": {
                "stock_code": "000001.SZ",
                "start_date": "2024-12-04"
            }
        },
        {
            "name": "无效的股票代码",
            "data": {
                "stock_code": "INVALID.XX",
                "start_date": "2024-12-04",
                "end_date": "2024-12-05"
            }
        }
    ]
    
    for test_case in test_cases:
        try:
            print(f"\n🧪 测试{test_case['name']}...")
            response = requests.post(url, json=test_case['data'], timeout=30)
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"响应码: {data.get('code')}")
                print(f"消息: {data.get('message')}")
                if data.get('data') and data['data']:
                    first_key = list(data['data'].keys())[0]
                    data_length = len(data['data'][first_key])
                    print(f"数据条数: {data_length}")
            else:
                print(f"请求失败: {response.text}")
                
        except Exception as e:
            print(f"测试失败: {e}")



def test_sector_data_api():
    """测试板块数据接口"""
    url = f"{URL}/api/v1/data/sector"
    
    # 测试数据
    test_data = {
        "stock_list": ["885362.TI", "700003.TI"],  # 板块代码
        "start_date": "2025-07-10",
        "end_date": "2025-07-11"
    }
    
    try:
        print("🚀 测试板块数据接口...")
        start_time = time.time()
        response = requests.post(url, json=test_data, timeout=120)
        end_time = time.time()
        print(f"请求时间: {end_time - start_time}秒")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"响应码: {data.get('code')}")
            print(f"消息: {data.get('message')}")
            
            # 打印数据统计信息
            if data.get('data'):
                data_content = data['data']
                json.dump(data_content, open("data_content.json", "w"), indent=2, ensure_ascii=False)
                if data_content:
                    # 获取数据长度
                    first_key = list(data_content.keys())[0]
                    data_length = len(data_content[first_key])
                    print(f"数据条数: {data_length}")
                    
                    print("\n字段列表:")
                    for col in data_content.keys():
                        print(f"  {col}")
                    
                    print("\n前3条数据样例:")
                    for col in ['instrument_id', 'trade_date', 'open', 'high', 'low', 'close', 'volume', 'avg_price', 'change', 'pct_chg', 'turnover_rate', 'total_mv', 'float_mv']:
                        if col in data_content:
                            values = data_content[col][:3]
                            print(f"  {col}: {values}")
                else:
                    print("返回数据为空")
        else:
            print(f"请求失败: {response.text}")
            
    except Exception as e:
        traceback.print_exc()
        print(f"测试失败: {e}")


if __name__ == "__main__":
    test_health_api()
    test_combined_data_api() 
    # test_stock_industry_l1_api()
    # test_empty_stock_list()
    # test_index_data_api()
    # test_empty_index_list()
    # test_stock_5min_data_api()
    # test_stock_5min_data_invalid_params()
    # test_sector_data_api()