import requests
import json

# 测试服务器地址
BASE_URL = "http://192.168.3.165:40042"

def test_insert_strategy():
    """测试插入交易策略"""
    print("=== 测试插入交易策略 ===")
    
    url = f"{BASE_URL}/api/v1/trade/strategy"
    data = {
        "strategy_name": "测试策略1",
        "strategy_desc": "这是一个测试策略",
        "factor_names": ["因子1", "因子2"],
        "factor_expressions": ["close > open", "volume > 1000"],
        "extra_params": {"param1": "value1", "param2": 123}
    }
    
    response = requests.post(url, json=data)
    result = response.json()
    print(f"Request: {json.dumps(data, ensure_ascii=False)}")
    print(f"Response: {json.dumps(result, ensure_ascii=False)}")
    print()
    
    return result

def test_insert_account_summary():
    """测试插入账户概览"""
    print("=== 测试插入账户概览 ===")
    
    url = f"{BASE_URL}/api/v1/trade/account-summary"
    data = {
        "account_id": "test_account_001",
        "total_asset": 1000000.50,
        "market_value": 800000.30,
        "cash": 200000.20,
        "timestamp": "2025-01-20 15:30:00"
    }
    
    response = requests.post(url, json=data)
    result = response.json()
    print(f"Request: {json.dumps(data, ensure_ascii=False)}")
    print(f"Response: {json.dumps(result, ensure_ascii=False)}")
    print()
    
    return result

def test_insert_account_positions():
    """测试批量插入账户持仓"""
    print("=== 测试批量插入账户持仓 ===")
    
    url = f"{BASE_URL}/api/v1/trade/account-positions"
    data = {
        "account_id": "test_account_001",
        "positions": [
            {
                "stock_code": "000001.SZ",
                "volume": 1000,
                "can_use_volume": 800,
                "frozen_volume": 200,
                "open_price": 12.50,
                "avg_price": 12.35,
                "market_value": 12350.00
            },
            {
                "stock_code": "000002.SZ",
                "volume": 500,
                "can_use_volume": 500,
                "frozen_volume": 0,
                "open_price": 8.20,
                "avg_price": 8.15,
                "market_value": 4075.00
            }
        ],
        "timestamp": "2025-01-20 15:30:00"
    }
    
    response = requests.post(url, json=data)
    result = response.json()
    print(f"Request: {json.dumps(data, ensure_ascii=False)}")
    print(f"Response: {json.dumps(result, ensure_ascii=False)}")
    print()
    
    return result

def test_batch_insert_signals():
    """测试批量插入交易信号"""
    print("=== 测试批量插入交易信号 ===")
    
    url = f"{BASE_URL}/api/v1/trade/signals"
    data = {
        "signals": [
            {
                "strategy_name": "测试策略1",
                "timestamp": "2025-01-20 14:30:00",
                "generated_time": "2025-01-20 14:32:15",
                "stock_code": "000001.SZ",
                "order_type": 1,
                "order_volume": 500,
                "last_close_price": 12.45
            },
            {
                "strategy_name": "测试策略1",
                "timestamp": "2025-01-20 14:35:00",
                "generated_time": "2025-01-20 14:37:20",
                "stock_code": "000002.SZ",
                "order_type": 2,
                "order_volume": 1000,
                "last_close_price": 8.90
            }
        ]
    }
    
    response = requests.post(url, json=data)
    result = response.json()
    print(f"Request: {json.dumps(data, ensure_ascii=False)}")
    print(f"Response: {json.dumps(result, ensure_ascii=False)}")
    print()
    
    return result



def test_get_strategy_by_name():
    """测试根据策略名称查询策略"""
    print("=== 测试根据策略名称查询策略 ===")
    
    url = f"{BASE_URL}/api/v1/trade/strategy/by-name"
    params = {
        "strategy_name": "测试策略1"
    }
    
    response = requests.get(url, params=params)
    result = response.json()
    print(f"Request: {url}?{requests.compat.urlencode(params)}")
    print(f"Response: {json.dumps(result, ensure_ascii=False)}")
    print()
    
    return result


def test_get_strategy_by_id():
    """测试根据策略ID查询策略"""
    print("=== 测试根据策略ID查询策略 ===")
    
    strategy_id = 1
    url = f"{BASE_URL}/api/v1/trade/strategy/by-id/{strategy_id}"
    
    response = requests.get(url)
    result = response.json()

    print(f"Request: {url}")
    print(f"Response: {json.dumps(result, ensure_ascii=False)}")
    print()
    
    return result


def test_get_signals_by_strategy():
    """测试根据策略名称查询信号"""
    print("=== 测试根据策略名称查询信号 ===")
    
    url = f"{BASE_URL}/api/v1/trade/signals/by-strategy"
    params = {
        "strategy_name": "测试策略1",
        "page": None,
        "page_size": None
    }
    
    response = requests.get(url, params=params)
    result = response.json()
    print(f"Request: {url}?{requests.compat.urlencode(params)}")
    print(f"Response: {json.dumps(result, ensure_ascii=False)}")
    print()
    
    return result


def test_get_signals_by_strategy_and_date():
    """测试根据策略名称和日期查询信号"""
    print("=== 测试根据策略名称和日期查询信号 ===")
    
    url = f"{BASE_URL}/api/v1/trade/signals/by-strategy-date"
    params = {
        "strategy_name": "测试策略1",
        "trade_date": "2025-01-20"
    }
    
    response = requests.get(url, params=params)
    result = response.json()
    print(f"Request: {url}?{requests.compat.urlencode(params)}")
    print(f"Response: {json.dumps(result, ensure_ascii=False)}")
    print()
    
    return result


def test_get_account_summary():
    """测试查询账户概览"""
    print("=== 测试查询账户概览 ===")
    
    url = f"{BASE_URL}/api/v1/trade/account-summary"
    params = {
        "account_id": "test_account_001",
        "page": 1,
        "page_size": 10
    }
    
    response = requests.get(url, params=params)
    result = response.json()
    print(f"Request: {url}?{requests.compat.urlencode(params)}")
    print(f"Response: {json.dumps(result, ensure_ascii=False)}")
    print()
    
    return result


def test_get_account_summary_with_date():
    """测试按日期查询账户概览"""
    print("=== 测试按日期查询账户概览 ===")
    
    url = f"{BASE_URL}/api/v1/trade/account-summary"
    params = {
        "account_id": "test_account_001",
        "date": "2025-01-20",
        "page": 1,
        "page_size": 10
    }
    
    response = requests.get(url, params=params)
    result = response.json()
    print(f"Request: {url}?{requests.compat.urlencode(params)}")
    print(f"Response: {json.dumps(result, ensure_ascii=False)}")
    print()
    
    return result


def test_get_account_positions():
    """测试查询账户持仓"""
    print("=== 测试查询账户持仓 ===")
    
    url = f"{BASE_URL}/api/v1/trade/account-positions"
    params = {
        "account_id": "test_account_001"
    }
    
    response = requests.get(url, params=params)
    result = response.json()
    print(f"Request: {url}?{requests.compat.urlencode(params)}")
    print(f"Response: {json.dumps(result, ensure_ascii=False)}")
    print()
    
    return result


def test_get_account_positions_with_date():
    """测试按日期查询账户持仓"""
    print("=== 测试按日期查询账户持仓 ===")
    
    url = f"{BASE_URL}/api/v1/trade/account-positions"
    params = {
        "account_id": "test_account_001",
        "date": "2025-01-20"
    }
    
    response = requests.get(url, params=params)
    result = response.json()
    print(f"Request: {url}?{requests.compat.urlencode(params)}")
    print(f"Response: {json.dumps(result, ensure_ascii=False)}")
    print()
    
    return result


def main():
    """主测试函数"""
    print("开始测试 SeekAlpha 交易 API...")
    
    # 按依赖顺序执行测试
    try:
        # # 1. 先插入策略（其他测试依赖此策略）
        # test_insert_strategy()
        
        # 2. 测试账户相关接口
        # test_insert_account_summary()
        # test_insert_account_positions()
        
        # # 3. 测试信号相关接口
        # test_insert_signal()
        # test_batch_insert_signals()
        
        # 4. 测试查询接口
        print("\n" + "="*50)
        print("开始测试查询接口")
        print("="*50)
        
        # 查询策略
        # test_get_strategy_by_name()
        # test_get_strategy_by_id()
        
        # # 查询信号
        # test_get_signals_by_strategy()
        # test_get_signals_by_strategy_and_date()
        
        # # 查询账户概览
        # test_get_account_summary()
        # test_get_account_summary_with_date()
        
        # # 查询账户持仓
        # test_get_account_positions()
        test_get_account_positions_with_date()
        
        print("所有测试完成!")
        

    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")

if __name__ == "__main__":
    main()

