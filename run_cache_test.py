#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
缓存机制测试运行脚本
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

if __name__ == "__main__":
    from test_stock_data_manager import StockDataManagerTester
    
    print("🎯 开始测试 StockDataManager 缓存机制")
    
    # 创建测试实例并运行缓存测试
    tester = StockDataManagerTester()
    tester.test_cache_mechanism()
    
    print("\n🎉 缓存机制测试完成！") 