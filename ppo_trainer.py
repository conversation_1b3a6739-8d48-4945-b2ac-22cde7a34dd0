# Copyright (c) Microsoft Corporation.
# SPDX-License-Identifier: Apache-2.0

# DeepSpeed Team
import torch
import torch.nn.functional as F
import time
import deepspeed
from deepspeed.runtime.zero.partition_parameters import ZeroParamStatus
from deepspeed.accelerator import get_accelerator

from dschat.utils.utils import print_rank_0,load_hf_tokenizer


def print_all_ranks(tag, value, rank):
    world_size = torch.distributed.get_world_size()
    all_tensor = torch.zeros(world_size, dtype=torch.float32).to(
        get_accelerator().current_device_name())
    all_tensor[rank] = value
    torch.distributed.all_reduce(all_tensor, op=torch.distributed.ReduceOp.SUM)
    print_rank_0(f'{tag} {all_tensor}', rank)


def get_model_norm(model):
    with torch.no_grad():
        total = 0.0
        for param in model.parameters():
            should_gather = hasattr(
                param,
                'ds_id') and param.ds_status == ZeroParamStatus.NOT_AVAILABLE
            with deepspeed.zero.GatheredParameters(param,
                                                   enabled=should_gather):
                total += float(param.float().norm())

    return total


def gather_log_probs(logits, labels):
    log_probs = F.log_softmax(logits, dim=-1)
    log_probs_labels = log_probs.gather(dim=-1, index=labels.unsqueeze(-1))
    return log_probs_labels.squeeze(-1)


class DeepSpeedPPOTrainer():

    def __init__(self, rlhf_engine, args):
        self.critic_model_path_name = args.critic_model_name_or_path
        self.rlhf_engine = rlhf_engine
        self.actor_model = self.rlhf_engine.actor
        self.critic_model = self.rlhf_engine.critic
        self.ref_model = self.rlhf_engine.ref
        self.reward_model = self.rlhf_engine.reward
        self.tokenizer = self.rlhf_engine.tokenizer
        self.args = args
        self.max_answer_seq_len = args.max_answer_seq_len
        self.max_prompt_seq_len = args.max_prompt_seq_len
        self.end_of_conversation_token_id = self.tokenizer(
            args.end_of_conversation_token)['input_ids'][-1]
        self.z3_enabled = args.actor_zero_stage == 3
        self.compute_fp32_loss = self.args.compute_fp32_loss

        # In case the generated experience is not valid (too short), we use the last valid
        # generated experience. Alternatively, we can skip the step (on all workers).
        # For now, use the last valid experience which is a simpler solution
        self.last_generated_experience = None

        # Those value can be changed
        self.kl_ctl = 0.1
        self.clip_reward_value = 5
        self.cliprange = 0.2
        self.cliprange_value = 0.2
        self.gamma = 1.0
        self.lam = 0.95
        self.generate_time = 0.0

    def _generate_sequence(self, prompts, mask, step):

        max_min_length = self.max_answer_seq_len + prompts.shape[1]
        print(f'max_min_length is {max_min_length}')
        # This has been added due to a probability/nan error that happens after
        # meta-llama/Llama-2-7b-hf enabled do_sample:
        # https://huggingface.co/meta-llama/Llama-2-7b-hf/commit/6fdf2e60f86ff2481f2241aaee459f85b5b0bbb9
        if self.actor_model.module.config.model_type == "llama" or self.actor_model.module.config.model_type == "baichuan":
            kwargs = dict(do_sample=False)
        else:
            kwargs = dict()

        with torch.no_grad():
            seq = self.actor_model.module.generate(
                prompts,
                attention_mask=mask,
                max_length=max_min_length,
                pad_token_id=self.tokenizer.pad_token_id,
                synced_gpus=self.z3_enabled,
                **kwargs)

        # Filter out seq with no answers (or very short). This happens when users directly use the pre-training ckpt without supervised finetuning
        # NOTE: this will causes each GPU has different number of examples
        batch_size = seq.shape[0]
        prompt_length = prompts.shape[1]
        
        self.prompt_length = prompt_length
        ans = seq[:, prompt_length:]
        valid_ans_len = (ans != self.tokenizer.pad_token_id).sum(dim=-1)

        if self.args.print_answers and (step % self.args.print_answers_interval
                                        == 0):
            print(
                f"--- prompt --> step={step}, rank={torch.distributed.get_rank()}, {self.tokenizer.batch_decode(prompts, skip_special_tokens=True)}"
            )
            print(
                f"--- ans    --> step={step}, rank={torch.distributed.get_rank()}, {self.tokenizer.batch_decode(ans, skip_special_tokens=True)}"
            )

        out_seq = []
        for i in range(batch_size):
            if valid_ans_len[
                    i] <= 1:  # if the answer is shorter than 1 token, drop it
                print(
                    f'Dropping too short generated answer: {step=}: \n'
                    f'prompts: {self.tokenizer.batch_decode(prompts, skip_special_tokens=False)}\n'
                    f'answers: {self.tokenizer.batch_decode(ans, skip_special_tokens=False)}'
                )
                continue
            else:
                out_seq.append(seq[i:i + 1])
                print(f'out_seq seq is {seq[i:i + 1]}')
        if not out_seq:
            print(
                f'All generated results are too short for rank={self.args.local_rank} step={step}\n'
                f'-> prompts: {self.tokenizer.batch_decode(prompts, skip_special_tokens=False)}\n'
                f'-> answers: {self.tokenizer.batch_decode(ans, skip_special_tokens=False)}'
            )
            return None

        out_seq = torch.cat(out_seq, dim=0)  # concat output in the batch dim

        return out_seq
    
    def get_reward_model_seq(self,seq):
        max_prompt_seq_len = self.max_prompt_seq_len
        prompt_content_id = seq[:,:max_prompt_seq_len]
        answer_content_id = seq[:,max_prompt_seq_len:]
        prompt_content = self.tokenizer.batch_decode(prompt_content_id, skip_special_tokens=True,return_tensors="pt") # 解码为文本
        answer_content = self.tokenizer.batch_decode(answer_content_id, skip_special_tokens=True,return_tensors="pt") # 解码为文本
        # print(f"get_reward_model_seq answer_content is {answer_content}")
        model_gpt_path_file = "/home/<USER>/RLHF/DeepSpeed/DeepSpeedExamples/applications/DeepSpeed-Chat/training/step2_reward_model_finetuning/output/reward-models/gpt2_1"
        gpt_tokenizer = load_hf_tokenizer(model_gpt_path_file,fast_tokenizer=True,add_special_tokens=None)
        prompt_content_gpt = gpt_tokenizer.batch_encode_plus(prompt_content,truncation=True,max_length=max_prompt_seq_len,return_tensors="pt")
        prompt_content_gpt_id = prompt_content_gpt["input_ids"]
        prompt_content_gpt_att_mask = prompt_content_gpt["attention_mask"]
        prompt_content_gpt_id_filp = prompt_content_gpt_id.flip(1)
        prompt_content_gpt_att_mask_filp = prompt_content_gpt_att_mask.flip(1)
        length = prompt_content_gpt_id.size()[-1]
        pad_length = max_prompt_seq_len - length
        pad_token_id = 0
        if pad_length > 0 :
            prompt_content_gpt_id_pad = F.pad(prompt_content_gpt_id_filp,
                                    pad=(0, pad_length),
                                    mode='constant',
                                    value=pad_token_id)
            prompt_content_gpt_att_mask_pad = F.pad(prompt_content_gpt_att_mask_filp,
                                    pad=(0, pad_length),
                                    mode='constant',
                                    value=pad_token_id)
        else :
            prompt_content_gpt_id_pad = prompt_content_gpt_id_filp
            prompt_content_gpt_att_mask_pad = prompt_content_gpt_att_mask_filp
        prompt_content_gpt_id_pad_filp = prompt_content_gpt_id_pad.flip(1).to(seq.device)
        prompt_content_gpt_att_mask_pad_filp = prompt_content_gpt_att_mask_pad.flip(1).to(seq.device)

        answer_content_gpt = gpt_tokenizer.batch_encode_plus(
                                        answer_content,
                                        max_length=self.max_answer_seq_len,
                                        truncation=True,
                                        return_tensors="pt")
        len_answer = answer_content_gpt["input_ids"].shape[1]
        if len_answer < self.max_answer_seq_len:
            answer_content_gpt["input_ids"] = answer_content_gpt["input_ids"]
            answer_content_gpt["attention_mask"] = answer_content_gpt["attention_mask"]
        else:
            answer_content_gpt["input_ids"] = answer_content_gpt["input_ids"][:,:self.max_answer_seq_len]
            answer_content_gpt["attention_mask"]= answer_content_gpt["attention_mask"][:,:self.max_answer_seq_len]
        # print(f'answer_content_gpt["input_ids"] is {answer_content_gpt["input_ids"].shape}')
        answer_content_gpt_id = answer_content_gpt["input_ids"].to(seq.device)
        answer_content_gpt_attention_mask = answer_content_gpt["attention_mask"].to(seq.device)
        rw_seq_id =torch.cat((prompt_content_gpt_id_pad_filp, answer_content_gpt_id), dim=1)
        rw_seq_attention_mask =torch.cat((prompt_content_gpt_att_mask_pad_filp, answer_content_gpt_attention_mask), dim=1)

        rw_prompt_length = prompt_content_gpt_id_pad_filp.shape[1]

        return rw_seq_id, rw_seq_attention_mask, rw_prompt_length, prompt_content, answer_content
        # return rw_seq_id,rw_seq_attention_mask,rw_prompt_length


    def generate_experience(self, prompts, mask, step):
        self.eval()
        generate_start = time.time()
        # prompts (baichuan_tokenizer_id) 填充到特定长度 [[2,2,2,....,xxx]]
        # print('seq before prompts is',prompts) 
        # prompt+answer (baichuan_tokenizer_id) prompts是特定长度，answer长度不固定
        seq = self._generate_sequence(prompts, mask, step) # baichuan batch prompt+answer的id
        # print(f'generate_experience seq is {seq}')
        generate_end = time.time()
        if seq is None:
            assert self.last_generated_experience is not None, f'Invalid generated experience at {step=}'
            prompts = self.last_generated_experience['prompts']
            seq = self.last_generated_experience['seq']
        else:
            self.last_generated_experience = {'prompts': prompts, 'seq': seq}
        self.train()
        rw_seq_id,rw_seq_attention_mask,rw_prompt_length,prompt_content, answer_content = self.get_reward_model_seq(seq)
        
        pad_token_id = self.tokenizer.pad_token_id
        attention_mask = seq.not_equal(pad_token_id).long()
        with torch.no_grad():
            output = self.actor_model(seq, attention_mask=attention_mask)
            output_ref = self.ref_model(seq, attention_mask=attention_mask)
            reward_score = self.reward_model.forward_value(
                rw_seq_id, rw_seq_attention_mask,
                prompt_length=rw_prompt_length)['chosen_end_scores'].detach(
                )
            print(f"{prompt_content},{answer_content},rw_seq_id is {rw_seq_id},reward_score is {reward_score}")
            values = self.critic_model.forward_value(
                seq, attention_mask, return_value_only=True).detach()[:, :-1]
            # print(f"values is {values}")

        logits = output.logits
        logits_ref = output_ref.logits
        if self.compute_fp32_loss:
            logits = logits.to(torch.float)
            logits_ref = logits_ref.to(torch.float)

        self.generate_time = generate_end - generate_start

        return {
            'prompts': prompts,
            'logprobs': gather_log_probs(logits[:, :-1, :], seq[:, 1:]),
            'ref_logprobs': gather_log_probs(logits_ref[:, :-1, :], seq[:,
                                                                        1:]),
            'value': values,
            'rewards': reward_score,
            'input_ids': seq,
            "attention_mask": attention_mask,
            "rw_attention_mask": rw_seq_attention_mask,
            # "rw_prompt_length":rw_prompt_length
        }

    def compute_rewards(self, prompts, log_probs, ref_log_probs, reward_score,
                        action_mask):

        kl_divergence_estimate = -self.kl_ctl * (log_probs - ref_log_probs)
        rewards = kl_divergence_estimate
        start = prompts.shape[1] - 1
        ends = start + action_mask[:, start:].sum(1) + 1
        reward_clip = torch.clamp(reward_score, -self.clip_reward_value,
                                  self.clip_reward_value)
        batch_size = log_probs.shape[0]
        for j in range(batch_size):
            rewards[j, start:ends[j]][-1] += reward_clip[j]

        return rewards 

    def train_rlhf(self, inputs):
        # train the rlhf mode here
        ### process the old outputs
        prompts = inputs['prompts']
        log_probs = inputs['logprobs']
        ref_log_probs = inputs['ref_logprobs']
        reward_score = inputs['rewards'] # gpt2给出的分数
        values = inputs['value']
        attention_mask = inputs['attention_mask']
        seq = inputs['input_ids']
        rw_attention_mask = inputs['rw_attention_mask']
        
        start = prompts.size()[-1] - 1
        action_mask = attention_mask[:, 1:]

        old_values = values
        # print(f"old_values.shape is {old_values.shape}")
        with torch.no_grad():
            old_rewards = self.compute_rewards(prompts, log_probs,
                                               ref_log_probs, reward_score,
                                               action_mask) 
            ends = start + action_mask[:, start:].sum(1) + 1
            for i in range(old_rewards.shape[0]):
                old_rewards[i, ends[i]:] = 0
                old_values[i, ends[i]:] = 0
            advantages, returns = self.get_advantages_and_returns(
                old_values, old_rewards, start)

        ### process the new outputs
        batch = {'input_ids': seq, "attention_mask": attention_mask}
        actor_prob = self.actor_model(**batch, use_cache=False).logits
        actor_log_prob = gather_log_probs(actor_prob[:, :-1, :], seq[:, 1:])
        actor_loss = self.actor_loss_fn(actor_log_prob[:, start:],
                                        log_probs[:, start:], advantages,
                                        action_mask[:, start:])
        self.actor_model.backward(actor_loss)

        if not self.args.align_overflow:
            self.actor_model.step()

        value = self.critic_model.forward_value(**batch,
                                                return_value_only=True,
                                                use_cache=False)[:, :-1]
        critic_loss = self.critic_loss_fn(value[:, start:], old_values[:,
                                                                       start:],
                                          returns, action_mask[:, start:])
        self.critic_model.backward(critic_loss)

        if self.args.align_overflow:
            actor_overflow = self.actor_model.optimizer.check_overflow(
                external=True)
            critic_overflow = self.critic_model.optimizer.check_overflow(
                external=True)

            rank = torch.distributed.get_rank()
            if actor_overflow and not critic_overflow:
                self.critic_model.optimizer.skip_step = True
                print_rank_0(
                    "OVERFLOW: actor overflow, skipping both actor and critic steps",
                    rank)
            elif not actor_overflow and critic_overflow:
                self.actor_model.optimizer.skip_step = True
                print_rank_0(
                    "OVERFLOW: critic overflow, skipping both actor and critic steps",
                    rank)
            elif actor_overflow and critic_overflow:
                print_rank_0(
                    "OVERFLOW: actor and critic overflow, skipping both actor and critic steps",
                    rank)
            self.actor_model.step()

        self.critic_model.step()

        return actor_loss, critic_loss

    def get_overflow(self):
        # Overflow is not expected when using bf16
        # Therefore, DeepSpeed's BF16_Optimizer does not maintain an overflow indication
        if self.args.dtype == "bf16":
            return False, False

        actor_overflow = self.actor_model.optimizer.overflow
        critic_overflow = self.critic_model.optimizer.overflow

        return actor_overflow, critic_overflow

    def actor_loss_fn(self, logprobs, old_logprobs, advantages, mask):
        ## policy gradient loss
        log_ratio = (logprobs - old_logprobs) * mask
        ratio = torch.exp(log_ratio)
        pg_loss1 = -advantages * ratio
        pg_loss2 = -advantages * torch.clamp(ratio, 1.0 - self.cliprange,
                                             1.0 + self.cliprange)
        pg_loss = torch.sum(torch.max(pg_loss1, pg_loss2) * mask) / mask.sum()
        return pg_loss

    def critic_loss_fn(self, values, old_values, returns, mask):
        ## value loss
        values_clipped = torch.clamp(
            values,
            old_values - self.cliprange_value,
            old_values + self.cliprange_value,
        )
        if self.compute_fp32_loss:
            values = values.float()
            values_clipped = values_clipped.float()
        vf_loss1 = (values - returns)**2
        vf_loss2 = (values_clipped - returns)**2
        vf_loss = 0.5 * torch.sum(
            torch.max(vf_loss1, vf_loss2) * mask) / mask.sum()
        return vf_loss
    
    # values gpt2维度 rewards baichuan维度
    def get_advantages_and_returns(self, values, rewards, start):
        # Adopted from https://github.com/CarperAI/trlx/blob/main/trlx/models/modeling_ppo.py#L134
        lastgaelam = 0
        advantages_reversed = []
        length = rewards.size()[-1]
        for t in reversed(range(start, length)):
            nextvalues = values[:, t + 1] if t < length - 1 else 0.0
            delta = rewards[:, t] + self.gamma * nextvalues - values[:, t]
            lastgaelam = delta + self.gamma * self.lam * lastgaelam
            advantages_reversed.append(lastgaelam)
        advantages = torch.stack(advantages_reversed[::-1], dim=1)
        returns = advantages + values[:, start:]
        return advantages.detach(), returns

    def _validate_training_mode(self):
        assert self.actor_model.module.training
        assert self.critic_model.module.training

    def _validate_evaluation_mode(self):
        assert not self.actor_model.module.training
        assert not self.critic_model.module.training
        assert not self.ref_model.module.training
        assert not self.reward_model.module.training

    def train(self):
        self.actor_model.train()
        self.critic_model.train()

    def eval(self):
        self.actor_model.eval()
        self.critic_model.eval()
        self.reward_model.eval()
        self.ref_model.eval()

    def dump_model_norms(self, tag):
        actor_model_norm = get_model_norm(self.actor_model)
        ref_model_norm = get_model_norm(self.ref_model)
        critic_model_norm = get_model_norm(self.critic_model)
        reward_model_norm = get_model_norm(self.reward_model)
        print_all_ranks(f'{tag} global_actor_model_norm', actor_model_norm,
                        self.args.local_rank)
        print_all_ranks(f'{tag} global_ref_model_norm', ref_model_norm,
                        self.args.local_rank)
        print_all_ranks(f'{tag} global_critic_model_norm', critic_model_norm,
                        self.args.local_rank)
        print_all_ranks(f'{tag} global_reward_model_norm', reward_model_norm,
                        self.args.local_rank)


class DeepSpeedPPOTrainerUnsupervised(DeepSpeedPPOTrainer):

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def train_unsupervised(self, inputs, unsup_coef):
        # Train the unsupervised model here
        self._validate_training_mode()

        outputs = self.actor_model(**inputs, use_cache=False)
        loss = outputs.loss
        self.actor_model.backward(unsup_coef * loss)
        self.actor_model.step()

        return loss
