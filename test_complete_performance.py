import time
import json
import clickhouse_connect
import pandas as pd

# ClickHouse 连接配置
CLICKHOUSE_CONFIG = {
    'host': '127.0.0.1',
    'port': 8714,
    'username': 'root',
    'password': 'Seue2vnILYi4F6',
    'database': 'seekalpha'
}

# 加载股票数据
stock_info = json.load(open('.cache/zz500_all_stocks_2013_2025.json'))
ALL_POSSIBLE_STOCKS = stock_info['stock_codes']

# 测试参数 - 使用全部股票！
START_DATE = '2021-01-01'
END_DATE = '2024-12-31'
stock_pool = ALL_POSSIBLE_STOCKS[:2]  # 使用全部股票

print(f"测试环境: ClickHouse @ {CLICKHOUSE_CONFIG['host']}:{CLICKHOUSE_CONFIG['port']}")
print(f"查询范围: {START_DATE} to {END_DATE}, 共 {len(stock_pool)} 支股票")
print("⚠️  警告：这是全量数据测试，预计耗时较长...")
print("=" * 70)

try:
    client = clickhouse_connect.get_client(**CLICKHOUSE_CONFIG)
    
    # 基础查询
    base_query = """
    SELECT instrument_id, trade_date, open, high, low, close, volume, amount, change, pct_chg, adj_factor 
    FROM daily_quotes
    WHERE trade_date BETWEEN toDate(%(start_date)s) AND toDate(%(end_date)s)
      AND instrument_id IN %(stock_pool)s
    """
    
    # SQL层面类型转换查询 - 修复语法
    sql_cast_query = """
    SELECT 
        instrument_id,
        trade_date,
        CAST(open AS Float64) as open,
        CAST(high AS Float64) as high,
        CAST(low AS Float64) as low,
        CAST(close AS Float64) as close,
        volume,
        CAST(amount AS Float64) as amount,
        CAST(change AS Nullable(Float64)) as change,
        CAST(pct_chg AS Nullable(Float64)) as pct_chg,
        CAST(adj_factor AS Nullable(Float64)) as adj_factor
    FROM daily_quotes
    WHERE trade_date BETWEEN toDate(%(start_date)s) AND toDate(%(end_date)s)
      AND instrument_id IN %(stock_pool)s
    """
    
    params = {
        'start_date': START_DATE,
        'end_date': END_DATE,
        'stock_pool': tuple(stock_pool)
    }

    print("🧪 开始性能测试...")
    print()

    # 方法1: 原始方法 (query + 手动转换 + 完整类型转换)
    print("方法1: 原始方法 (query + 手动转换 + 完整类型转换)")
    start_time = time.time()
    result = client.query(base_query, parameters=params)
    query_time = time.time() - start_time
    print(f"  - 查询耗时: {query_time:.4f} 秒")
    
    df_start = time.time()
    df1 = pd.DataFrame(result.result_rows, columns=result.column_names)
    df_create_time = time.time() - df_start
    print(f"  - 创建DataFrame耗时: {df_create_time:.4f} 秒")
    
    convert_start = time.time()
    # 完整类型转换
    df1['instrument_id'] = df1['instrument_id'].astype('string')
    df1['trade_date'] = pd.to_datetime(df1['trade_date'])
    for col in ['open', 'high', 'low', 'close', 'amount', 'change', 'pct_chg', 'adj_factor']:
        if col in df1.columns:
            df1[col] = pd.to_numeric(df1[col], errors='coerce').astype('float64')
    df1['volume'] = pd.to_numeric(df1['volume'], errors='coerce').astype('Int64')
    convert_time = time.time() - convert_start
    print(f"  - 类型转换耗时: {convert_time:.4f} 秒")
    
    time1 = time.time() - start_time
    print(f"  💡 总耗时: {time1:.4f} 秒 | 数据行数: {len(df1)}")
    print()

    # 方法2: 优化方法 (query + 最小化类型转换)
    print("方法2: 优化方法 (query + 最小化类型转换)")
    start_time = time.time()
    result = client.query(base_query, parameters=params)
    df2 = pd.DataFrame(result.result_rows, columns=result.column_names)
    
    # 只转换必要的类型（日期）
    df2['trade_date'] = pd.to_datetime(df2['trade_date'])
    
    time2 = time.time() - start_time
    print(f"  💡 总耗时: {time2:.4f} 秒 | 数据行数: {len(df2)}")
    print()

    # 方法3: SQL层面类型转换
    print("方法3: SQL层面类型转换 (在数据库端转换)")
    start_time = time.time()
    result = client.query(sql_cast_query, parameters=params)
    df3 = pd.DataFrame(result.result_rows, columns=result.column_names)
    # df3['trade_date'] = pd.to_datetime(df3['trade_date'])
    
    time3 = time.time() - start_time
    print(f"  💡 总耗时: {time3:.4f} 秒 | 数据行数: {len(df3)}")
    print()

    # 方法4: query_df方法
    print("方法4: 直接使用query_df")
    start_time = time.time()
    df4 = client.query_df(base_query, parameters=params)
    
    time4 = time.time() - start_time
    data = df4.to_dict('records')
    print(f"data: {data[:2]}")
    print(f"  💡 总耗时: {time4:.4f} 秒 | 数据行数: {len(df4)}")
    print()

    # 方法5: 完全不转换类型
    print("方法5: 完全不转换类型 (基准测试)")
    start_time = time.time()
    result = client.query(base_query, parameters=params)
    df5 = pd.DataFrame(result.result_rows, columns=result.column_names)
    # 完全不做类型转换
    
    time5 = time.time() - start_time
    print(f"  💡 总耗时: {time5:.4f} 秒 | 数据行数: {len(df5)}")
    print()

    # 方法6: query_df + SQL类型转换
    print("方法6: query_df + SQL层面类型转换")
    start_time = time.time()
    df6 = client.query_df(sql_cast_query, parameters=params)
    
    time6 = time.time() - start_time
    print(f"  💡 总耗时: {time6:.4f} 秒 | 数据行数: {len(df6)}")
    print()

    print("=" * 70)
    print("🏆 性能对比总结:")
    print("-" * 70)
    times = [time1, time2, time3, time4, time5, time6]
    methods = [
        "原始方法(完整转换)", 
        "最小化转换", 
        "SQL层面转换", 
        "query_df", 
        "完全不转换",
        "query_df+SQL转换"
    ]
    
    for i, (method, t) in enumerate(zip(methods, times), 1):
        print(f"方法{i}: {method:20} | {t:8.4f} 秒")
    
    print("-" * 70)
    
    # 找出最快的方法
    best_idx = times.index(min(times))
    worst_idx = times.index(max(times))
    
    print(f"🥇 最快方法: {methods[best_idx]} ({times[best_idx]:.4f} 秒)")
    print(f"🐌 最慢方法: {methods[worst_idx]} ({times[worst_idx]:.4f} 秒)")
    print(f"📊 性能差异: {times[worst_idx]/times[best_idx]:.2f} 倍")
    
    print()
    print("💡 加速比分析 (相对于原始方法):")
    for i, (method, t) in enumerate(zip(methods[1:], times[1:]), 2):
        speedup = time1 / t
        if speedup > 1:
            print(f"方法{i}: {method} - 加速 {speedup:.2f} 倍")
        else:
            print(f"方法{i}: {method} - 减速 {1/speedup:.2f} 倍")

    print()
    print("🔍 数据类型对比 (前5列):")
    print("-" * 50)
    for i, (method, df) in enumerate(zip(methods, [df1, df2, df3, df4, df5, df6]), 1):
        print(f"方法{i} ({method}):")
        for col in list(df.columns)[:5]:
            print(f"  {col:15}: {str(df[col].dtype):15}")
        print()

except Exception as e:
    print(f"❌ 执行出错: {e}")
    import traceback
    traceback.print_exc() 