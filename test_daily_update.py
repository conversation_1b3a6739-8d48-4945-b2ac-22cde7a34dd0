#!/usr/bin/env python3
"""
测试每日数据更新功能
"""

import os
import sys
from datetime import datetime
from dotenv import load_dotenv

# 加载环境变量
load_dotenv(".env_sh")


from stock_collector import StockDataCollector, DailyUpdateManager

def format_date(date_str: str) -> str:
    """格式化日期字符串"""
    if '-' in date_str:
        return datetime.strptime(date_str, '%Y-%m-%d').strftime('%Y-%m-%d')

    return datetime.strptime(date_str, '%Y%m%d').strftime('%Y-%m-%d')

def test_daily_update_market():
    """测试行情数据每日更新功能"""
    
    # 检查必需的环境变量
    token = os.getenv("TUSHARE_TOKEN")
    stock_codes_path = ".cache/zz500_all_stocks_2013_2025.json"
    
    if not token:
        print("错误: 请设置TUSHARE_TOKEN环境变量")
        return False
    
    if not os.path.exists(stock_codes_path):
        print(f"错误: 股票代码文件不存在: {stock_codes_path}")
        return False
    
    # 确保缓存目录存在
    os.makedirs('.cache', exist_ok=True)
    
    try:
        # 配置数据库连接
        db_config = {
            "host": os.getenv("CLICKHOUSE_HOST"),
            "port": os.getenv("CLICKHOUSE_PORT"),
            "user": os.getenv("CLICKHOUSE_USER"), 
            "password": os.getenv("CLICKHOUSE_PASSWORD"),
            "database": os.getenv("CLICKHOUSE_DATABASE")
        }
        
        # 创建收集器实例
        collector = StockDataCollector(
            token=token, 
            db_config=db_config
        )
        
        # 创建每日更新管理器
        daily_manager = DailyUpdateManager(collector)
        
        
        print("=" * 80)
        print("测试行情数据每日更新功能")
        print("=" * 80)
        
        # 测试获取北京时间前一天
        yesterday = daily_manager.get_beijing_yesterday()
        print(f"北京时间前一天: {yesterday}")
        

        latest_date = daily_manager.get_latest_date_from_db('daily_quotes')

        if format_date(yesterday) == format_date(latest_date):
            print("无需更新")
            return True
        
        # 执行每日行情数据更新
        result = daily_manager.daily_update_market_data(stock_codes_path, yesterday, latest_date)
        
        print("\n行情数据更新结果:")
        for key, value in result.items():
            print(f"  {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"行情数据测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_daily_update_cyq():
    """测试筹码数据每日更新功能"""
    
    # 检查必需的环境变量
    token = os.getenv("TUSHARE_TOKEN")
    stock_codes_path = ".cache/zz500_all_stocks_2013_2025.json"
    
    if not token:
        print("错误: 请设置TUSHARE_TOKEN环境变量")
        return False
    
    if not os.path.exists(stock_codes_path):
        print(f"错误: 股票代码文件不存在: {stock_codes_path}")
        return False
    
    try:
        # 配置数据库连接
        db_config = {
            "host": os.getenv("CLICKHOUSE_HOST"),
            "port": os.getenv("CLICKHOUSE_PORT"),
            "user": os.getenv("CLICKHOUSE_USER"), 
            "password": os.getenv("CLICKHOUSE_PASSWORD"),
            "database": os.getenv("CLICKHOUSE_DATABASE")
        }
        
        # 创建收集器实例
        collector = StockDataCollector(
            token=token, 
            db_config=db_config
        )
        
        # 创建每日更新管理器
        daily_manager = DailyUpdateManager(collector)
        
        print("=" * 80)
        print("测试筹码数据每日更新功能")
        print("=" * 80)
        
        # 测试获取北京时间前一天
        yesterday = daily_manager.get_beijing_yesterday()
        print(f"北京时间前一天: {yesterday}")
        
        latest_date = daily_manager.get_latest_date_from_db('daily_chips')

        if format_date(yesterday) == format_date(latest_date):
            print("无需更新")
            return True
        
        # 执行每日筹码数据更新
        result = daily_manager.daily_update_cyq_data(stock_codes_path, yesterday, latest_date)
        
        print("\n筹码数据更新结果:")
        for key, value in result.items():
            print(f"  {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"筹码数据测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_daily_update_moneyflow():
    """测试资金流数据每日更新功能"""
    
    # 检查必需的环境变量
    token = os.getenv("TUSHARE_TOKEN")
    stock_codes_path = ".cache/zz500_all_stocks_2013_2025.json"
    
    if not token:
        print("错误: 请设置TUSHARE_TOKEN环境变量")
        return False
    
    if not os.path.exists(stock_codes_path):
        print(f"错误: 股票代码文件不存在: {stock_codes_path}")
        return False
    
    try:
        # 配置数据库连接
        db_config = {
            "host": os.getenv("CLICKHOUSE_HOST"),
            "port": os.getenv("CLICKHOUSE_PORT"),
            "user": os.getenv("CLICKHOUSE_USER"), 
            "password": os.getenv("CLICKHOUSE_PASSWORD"),
            "database": os.getenv("CLICKHOUSE_DATABASE")
        }
        
        # 创建收集器实例
        collector = StockDataCollector(
            token=token, 
            db_config=db_config
        )
        
        # 创建每日更新管理器
        daily_manager = DailyUpdateManager(collector)
        
        print("=" * 80)
        print("测试资金流数据每日更新功能")
        print("=" * 80)
        
        # 测试获取北京时间前一天
        yesterday = daily_manager.get_beijing_yesterday()
        print(f"北京时间前一天: {yesterday}")
        
        latest_date = daily_manager.get_latest_date_from_db('daily_money_flow')

        print(f"format_date(yesterday): {format_date(yesterday)}")
        print(f"format_date(latest_date): {format_date(latest_date)}")

        if format_date(yesterday) == format_date(latest_date):
            print("无需更新")
            return True
        
        # 执行每日资金流数据更新
        result = daily_manager.daily_update_moneyflow_data(stock_codes_path, yesterday, latest_date)
        
        print("\n资金流数据更新结果:")
        for key, value in result.items():
            print(f"  {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"资金流数据测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_all_daily_updates():
    """测试所有类型的每日更新功能"""
    
    # 检查必需的环境变量
    token = os.getenv("TUSHARE_TOKEN")
    stock_codes_path = ".cache/zz500_all_stocks_2013_2025.json"
    
    if not token:
        print("错误: 请设置TUSHARE_TOKEN环境变量")
        return False
    
    if not os.path.exists(stock_codes_path):
        print(f"错误: 股票代码文件不存在: {stock_codes_path}")
        return False
    
    try:
        # 配置数据库连接
        db_config = {
            "host": os.getenv("CLICKHOUSE_HOST"),
            "port": os.getenv("CLICKHOUSE_PORT"),
            "user": os.getenv("CLICKHOUSE_USER"), 
            "password": os.getenv("CLICKHOUSE_PASSWORD"),
            "database": os.getenv("CLICKHOUSE_DATABASE")
        }
        
        # 创建收集器实例
        collector = StockDataCollector(
            token=token, 
            db_config=db_config
        )
        
        # 创建每日更新管理器
        daily_manager = DailyUpdateManager(collector)
        
        print("=" * 80)
        print("测试所有类型的每日更新功能")
        print("=" * 80)
        
        # 测试获取北京时间前一天
        yesterday = daily_manager.get_beijing_yesterday()
        print(f"北京时间前一天: {yesterday}")
        
        latest_date = daily_manager.get_latest_date_from_db('daily_quotes')

        if format_date(yesterday) == format_date(latest_date):
            print("无需更新")
            return True

        # 1. 测试行情数据更新
        print("\n1. 测试行情数据每日更新")
        print("-" * 40)
        market_result = daily_manager.daily_update_market_data(stock_codes_path, yesterday, latest_date)
        print("行情数据更新结果:")
        for key, value in market_result.items():
            print(f"  {key}: {value}")
        
        # 2. 测试筹码数据更新
        print("\n2. 测试筹码数据每日更新")
        print("-" * 40)
        cyq_result = daily_manager.daily_update_cyq_data(stock_codes_path, yesterday, latest_date)
        print("筹码数据更新结果:")
        for key, value in cyq_result.items():
            print(f"  {key}: {value}")
        
        # 3. 测试资金流数据更新
        print("\n3. 测试资金流数据每日更新")
        print("-" * 40)
        moneyflow_result = daily_manager.daily_update_moneyflow_data(stock_codes_path, yesterday, latest_date)
        print("资金流数据更新结果:")
        for key, value in moneyflow_result.items():
            print(f"  {key}: {value}")
        
        # 汇总结果
        print("\n" + "=" * 80)
        print("测试结果汇总")
        print("=" * 80)
        
        results = [
            ("行情数据", market_result),
            ("筹码数据", cyq_result),
            ("资金流数据", moneyflow_result)
        ]
        
        total_imported = 0
        for data_type, result in results:
            print(f"\n【{data_type}】")
            if "error" in result:
                print(f"❌ 更新失败: {result['error']}")
            else:
                if result.get('message') == '无需更新':
                    print("✅ 数据已是最新")
                else:
                    imported_count = result.get('imported_count', 0)
                    print(f"✅ 成功导入 {imported_count} 条记录")
                    total_imported += imported_count
        
        print(f"\n总计导入记录数: {total_imported}")
        print("✅ 所有测试完成！")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_database_status():
    """测试数据库状态查询功能"""
    
    # 检查必需的环境变量
    token = os.getenv("TUSHARE_TOKEN")
    
    if not token:
        print("错误: 请设置TUSHARE_TOKEN环境变量")
        return False
    
    try:
        # 配置数据库连接
        db_config = {
            "host": os.getenv("CLICKHOUSE_HOST"),
            "port": os.getenv("CLICKHOUSE_PORT"),
            "user": os.getenv("CLICKHOUSE_USER"), 
            "password": os.getenv("CLICKHOUSE_PASSWORD"),
            "database": os.getenv("CLICKHOUSE_DATABASE")
        }
        
        # 创建收集器实例
        collector = StockDataCollector(token=token, db_config=db_config)
        daily_manager = DailyUpdateManager(collector)
        
        print("=" * 80)
        print("测试数据库状态查询功能")
        print("=" * 80)
        
        # 测试数据库查询功能
        print("查询各表最新数据日期:")
        market_latest = daily_manager.get_latest_date_from_db('daily_quotes')
        cyq_latest = daily_manager.get_latest_date_from_db('daily_chips')
        moneyflow_latest = daily_manager.get_latest_date_from_db('daily_money_flow')
        
        print(f"  行情数据最新日期: {market_latest if market_latest else '无数据'}")
        print(f"  筹码数据最新日期: {cyq_latest if cyq_latest else '无数据'}")
        print(f"  资金流数据最新日期: {moneyflow_latest if moneyflow_latest else '无数据'}")
        
        # 测试交易日历功能
        print("\n测试交易日历功能:")
        yesterday = daily_manager.get_beijing_yesterday()
        calendar_df = daily_manager._get_or_update_trade_calendar(end_date=yesterday)
        print(f"  交易日历记录数: {len(calendar_df)}")
        print(f"  北京时间前一天: {yesterday}")
        
        # 测试缺失日期计算
        print("\n测试缺失日期计算:")
        
        missing_market = daily_manager.get_missing_trade_dates(market_latest, yesterday, calendar_df)
        missing_cyq = daily_manager.get_missing_trade_dates(cyq_latest, yesterday, calendar_df)
        missing_moneyflow = daily_manager.get_missing_trade_dates(moneyflow_latest, yesterday, calendar_df)
        
        print(f"  行情数据缺失日期: {missing_market[:5]}{'...' if len(missing_market) > 5 else ''} (共{len(missing_market)}个)")
        print(f"  筹码数据缺失日期: {missing_cyq[:5]}{'...' if len(missing_cyq) > 5 else ''} (共{len(missing_cyq)}个)")
        print(f"  资金流数据缺失日期: {missing_moneyflow[:5]}{'...' if len(missing_moneyflow) > 5 else ''} (共{len(missing_moneyflow)}个)")
        
        return True
        
    except Exception as e:
        print(f"数据库状态测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("选择测试选项:")
    print("1. 测试行情数据每日更新")
    print("2. 测试筹码数据每日更新")  
    print("3. 测试资金流数据每日更新")
    print("4. 测试所有类型的每日更新")
    print("5. 测试数据库状态查询")
    print("6. 运行所有测试")
    
    choice = input("请输入选择 (1-6): ").strip()


    if choice == "1":
        print("\n开始测试行情数据每日更新...")
        success = test_daily_update_market()
    elif choice == "2":
        print("\n开始测试筹码数据每日更新...")
        success = test_daily_update_cyq()
    elif choice == "3":
        print("\n开始测试资金流数据每日更新...")
        success = test_daily_update_moneyflow()
    elif choice == "4":
        print("\n开始测试所有类型的每日更新...")
        success = test_all_daily_updates()
    elif choice == "5":
        print("\n开始测试数据库状态查询...")
        success = test_database_status()
    elif choice == "6":
        print("\n开始运行所有测试...")
        print("\n" + "=" * 60)
        print("第1步: 数据库状态查询测试")
        print("=" * 60)
        success1 = test_database_status()
        
        print("\n" + "=" * 60)  
        print("第2步: 行情数据更新测试")
        print("=" * 60)
        success2 = test_daily_update_market()
        
        print("\n" + "=" * 60)
        print("第3步: 筹码数据更新测试") 
        print("=" * 60)
        success3 = test_daily_update_cyq()
        
        print("\n" + "=" * 60)
        print("第4步: 资金流数据更新测试")
        print("=" * 60)
        success4 = test_daily_update_moneyflow()
        
        print("\n" + "=" * 60)
        print("第5步: 综合更新测试")
        print("=" * 60)
        success5 = test_all_daily_updates()
        
        success = success1 and success2 and success3 and success4 and success5
        
        print("\n" + "=" * 80)
        print("所有测试完成汇总")
        print("=" * 80)
        print(f"数据库状态测试: {'✅ 通过' if success1 else '❌ 失败'}")
        print(f"行情数据测试: {'✅ 通过' if success2 else '❌ 失败'}")
        print(f"筹码数据测试: {'✅ 通过' if success3 else '❌ 失败'}")
        print(f"资金流数据测试: {'✅ 通过' if success4 else '❌ 失败'}")
        print(f"综合更新测试: {'✅ 通过' if success5 else '❌ 失败'}")
        print(f"总体结果: {'🎉 全部通过' if success else '⚠️ 部分失败'}")
    else:
        print("无效选择")
        success = False
    
    sys.exit(0 if success else 1) 