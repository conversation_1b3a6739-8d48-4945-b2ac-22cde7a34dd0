#!/usr/bin/env python3
"""
测试StockDataManager的DataFrame返回功能
"""

import time
import pandas as pd
from app.src.manager.stock_data_manager import StockDataManager

def main():
    # 初始化manager
    manager = StockDataManager.get_instance()
    
    # 测试参数
    stock_list = ['000001.SZ', '000002.SZ', '600000.SH']
    start_date = '2024-01-01'
    end_date = '2024-01-31'
    
    print("=" * 60)
    print("测试 StockDataManager DataFrame 返回功能")
    print("=" * 60)
    
    # 测试1: get_daily_quotes 返回字典格式
    print("\n1. 测试 get_daily_quotes 返回字典格式")
    start_time = time.time()
    result_dict = manager.get_daily_quotes(
        stock_list=stock_list,
        start_date=start_date,
        end_date=end_date,
        return_df=False
    )
    dict_time = time.time() - start_time
    print(f"   时间: {dict_time:.4f} 秒")
    print(f"   类型: {type(result_dict)}")
    print(f"   数据条数: {result_dict.get('total', 0)}")
    if result_dict.get('data'):
        print(f"   第一条数据类型: {type(result_dict['data'][0])}")
    
    # 测试2: get_daily_quotes 返回DataFrame格式
    print("\n2. 测试 get_daily_quotes 返回DataFrame格式")
    start_time = time.time()
    result_df = manager.get_daily_quotes(
        stock_list=stock_list,
        start_date=start_date,
        end_date=end_date,
        return_df=True
    )
    df_time = time.time() - start_time
    print(f"   时间: {df_time:.4f} 秒")
    print(f"   类型: {type(result_df)}")
    print(f"   形状: {result_df.shape}")
    print(f"   列: {list(result_df.columns)}")
    
    # 显示数据类型
    if not result_df.empty:
        print(f"   数据类型:")
        for col, dtype in result_df.dtypes.items():
            print(f"     {col}: {dtype}")
    
    # 测试3: 验证数据一致性
    print("\n3. 验证数据一致性")
    if result_dict.get('data') and not result_df.empty:
        dict_count = len(result_dict['data'])
        df_count = len(result_df)
        print(f"   字典格式数据条数: {dict_count}")
        print(f"   DataFrame数据条数: {df_count}")
        print(f"   数据一致性: {'✓' if dict_count == df_count else '✗'}")
    
    # 测试4: 性能对比
    print(f"\n4. 性能对比")
    print(f"   字典格式耗时: {dict_time:.4f} 秒")
    print(f"   DataFrame耗时: {df_time:.4f} 秒")
    if dict_time > 0:
        speedup = dict_time / df_time if df_time > 0 else float('inf')
        print(f"   DataFrame加速比: {speedup:.2f}x")
    
    # 测试5: 测试筹码数据
    print("\n5. 测试筹码数据 DataFrame 返回")
    start_time = time.time()
    chips_df = manager.get_daily_chips(
        stock_list=stock_list,
        start_date=start_date,
        end_date=end_date,
        return_df=True
    )
    print(f"   时间: {time.time() - start_time:.4f} 秒")
    print(f"   类型: {type(chips_df)}")
    print(f"   形状: {chips_df.shape}")
    
    # 测试6: 测试资金流数据
    print("\n6. 测试资金流数据 DataFrame 返回")
    start_time = time.time()
    money_flow_df = manager.get_daily_money_flow(
        stock_list=stock_list,
        start_date=start_date,
        end_date=end_date,
        return_df=True
    )
    print(f"   时间: {time.time() - start_time:.4f} 秒")
    print(f"   类型: {type(money_flow_df)}")
    print(f"   形状: {money_flow_df.shape}")
    
    print("\n" + "=" * 60)
    print("测试完成!")
    print("=" * 60)

if __name__ == "__main__":
    main() 