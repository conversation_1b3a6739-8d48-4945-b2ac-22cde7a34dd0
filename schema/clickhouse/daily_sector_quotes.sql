CREATE TABLE daily_sector_quotes
(
    -- 核心维度列
    instrument_id     LowCardinality(String)      COMMENT '同花顺板块代码',
    trade_date        Date          COMMENT '交易日期' CODEC(DoubleDelta, ZSTD),
    
    -- 行情指标列
    open              Float64       COMMENT '开盘点位' CODEC(Gorilla, ZSTD),
    high              Float64       COMMENT '最高点位' CODEC(Gorilla, ZSTD),
    low               Float64       COMMENT '最低点位' CODEC(Gorilla, ZSTD),
    close             Float64       COMMENT '收盘点位' CODEC(Gorilla, ZSTD),
    volume            UInt64        COMMENT '成交量(股)' CODEC(T64, LZ4),

    avg_price         Nullable(Float64)       COMMENT '平均价' CODEC(Gorilla, ZSTD),
    change            Nullable(Float64)       COMMENT '涨跌点位' CODEC(Gorilla, ZSTD),
    pct_chg        Nullable(Float64)       COMMENT '涨跌幅' CODEC(Gorilla, ZSTD),
    turnover_rate     Nullable(Float64)       COMMENT '换手率(%)' CODEC(Gorilla, ZSTD),

    -- 市值列 (通常数值较大)
    total_mv          Nullable(Float64)       COMMENT '总市值(元)' CODEC(Gorilla, ZSTD),
    float_mv          Nullable(Float64)       COMMENT '流通市值(元)' CODEC(Gorilla, ZSTD),

    ingest_time       DateTime      COMMENT '数据入库时间' CODEC(DoubleDelta, ZSTD)

)
ENGINE = MergeTree()
PARTITION BY toYYYYMM(trade_date)
ORDER BY (instrument_id, trade_date)
SETTINGS index_granularity = 8192;