CREATE TABLE daily_money_flow (

    instrument_id FixedString(9) COMMENT '股票代码, e.g., 600004.SH' CODEC(LZ4),
    trade_date Date COMMENT '交易日期' CODEC(DoubleDelta, LZ4),

    buy_sm_vol Nullable(Int64) COMMENT '小单买入量(股)' CODEC(T64, LZ4),
    buy_sm_amount Nullable(Decimal64(2)) COMMENT '小单买入金额(元)' CODEC(T64, LZ4),
    sell_sm_vol Nullable(Int64) COMMENT '小单卖出量(股)' CODEC(T64, LZ4),
    sell_sm_amount Nullable(Decimal64(2)) COMMENT '小单卖出金额(元)' CODEC(T64, LZ4),

    buy_md_vol Nullable(Int64) COMMENT '中单买入量(股)' CODEC(T64, LZ4),
    buy_md_amount Nullable(Decimal64(2)) COMMENT '中单买入金额(元)' CODEC(T64, LZ4),
    sell_md_vol Nullable(Int64) COMMENT '中单卖出量(股)' CODEC(T64, LZ4),
    sell_md_amount Nullable(Decimal64(2)) COMMENT '中单卖出金额(元)' CODEC(T64, LZ4),

    buy_lg_vol Nullable(Int64) COMMENT '大单买入量(股)' CODEC(T64, LZ4),
    buy_lg_amount Nullable(Decimal64(2)) COMMENT '大单买入金额(元)' CODEC(T64, LZ4),
    sell_lg_vol Nullable(Int64) COMMENT '大单卖出量(股)' CODEC(T64, LZ4),
    sell_lg_amount Nullable(Decimal64(2)) COMMENT '大单卖出金额(元)' CODEC(T64, LZ4),

    buy_elg_vol Nullable(Int64) COMMENT '特大单买入量(股)' CODEC(T64, LZ4),
    buy_elg_amount Nullable(Decimal64(2)) COMMENT '特大单买入金额(元)' CODEC(T64, LZ4),
    sell_elg_vol Nullable(Int64) COMMENT '特大单卖出量(股)' CODEC(T64, LZ4),
    sell_elg_amount Nullable(Decimal64(2)) COMMENT '特大单卖出金额(元)' CODEC(T64, LZ4),

    net_mf_vol Nullable(Int64) COMMENT '净流入量(股)' CODEC(T64, LZ4),
    net_mf_amount Nullable(Decimal64(2)) COMMENT '净流入额(元)' CODEC(T64, LZ4),

    ingest_time DateTime COMMENT '数据入库时间'

) ENGINE = MergeTree()
PARTITION BY toYYYYMM(trade_date)
ORDER BY (instrument_id, trade_date)
SETTINGS index_granularity = 8192;