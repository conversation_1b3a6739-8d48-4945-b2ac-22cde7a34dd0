CREATE TABLE stock_5_min
(
    -- 核心字段
    `instrument_id` LowCardinality(String) COMMENT '股票代码, e.g., 600519.SH',
    `trade_time` DateTime   COMMENT '交易时间, e.g., 2023-08-25 15:00:00' CODEC(DoubleDelta, ZSTD),
    `open` Nullable(Float32)                  COMMENT '开盘价' CODEC(Gorilla, ZSTD),
    `high` Nullable(Float32)                  COMMENT '最高价' CODEC(Gorilla, ZSTD),
    `low` Nullable(Float32)                   COMMENT '最低价' CODEC(Gorilla, ZSTD),
    `close` Nullable(Float32)                 COMMENT '收盘价' CODEC(Gorilla, ZSTD),
    `volume` Nullable(UInt64)                 COMMENT '成交量 (股)' CODEC(T64, LZ4),
    `amount` Nullable(Float64)                COMMENT '成交额 (元)' CODEC(Gorilla, ZSTD),

    -- 建议增加的冗余字段，便于查询
    `trade_date` Date               COMMENT '交易日期' CODEC(DoubleDelta, ZSTD)

)
ENGINE = MergeTree()
PARTITION BY toYYYYMM(trade_time)
ORDER BY (instrument_id, trade_time)
SETTINGS index_granularity = 512;