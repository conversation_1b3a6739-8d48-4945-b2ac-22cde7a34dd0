CREATE TABLE stock_industry (
    -- 股票维度
    instrument_id FixedString(9) COMMENT '成分股票代码' CODEC(LZ4),
    name String COMMENT '成分股票名称' CODEC(ZSTD),

    -- 行业分类维度
    l1_code Nullable(FixedString(9)) COMMENT '一级行业代码' CODEC(LZ4),
    l1_name Nullable(String) COMMENT '一级行业名称' CODEC(ZSTD),
    l2_code Nullable(FixedString(9)) COMMENT '二级行业代码' CODEC(LZ4),
    l2_name Nullable(String) COMMENT '二级行业名称' CODEC(ZSTD),
    l3_code Nullable(FixedString(9)) COMMENT '三级行业代码' CODEC(LZ4),
    l3_name Nullable(String) COMMENT '三级行业名称' CODEC(ZSTD),

    -- 关系生命周期
    in_date Date COMMENT '纳入日期' CODEC(DoubleDelta, LZ4),
    out_date Nullable(Date) COMMENT '剔除日期' CODEC(DoubleDelta, LZ4),

    -- 状态标志
    is_new Nullable(UInt8) COMMENT '是否最新: 1是, 0否',
    
    -- 元数据
    ingest_time DateTime COMMENT '数据入库时间'

) ENGINE = MergeTree()
-- 分区键：由于这不是严格的时间序列数据，可以不分区，或使用一个伪分区
PARTITION BY tuple()
-- 排序键：核心优化，满足双向查询
ORDER BY (instrument_id, in_date);