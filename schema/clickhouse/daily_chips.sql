CREATE TABLE daily_chips (

    instrument_id FixedString(9) COMMENT '股票代码, e.g., 600004.SH' CODEC(LZ4),
    trade_date Date COMMENT '交易日期' CODEC(DoubleDelta, LZ4),

    his_low Nullable(Decimal32(4)) COMMENT '历史最低价' CODEC(T64, LZ4),
    his_high Nullable(Decimal32(4)) COMMENT '历史最高价' CODEC(T64, LZ4),

    cost_5pct Nullable(Decimal32(4)) COMMENT '5%成本位价格' CODEC(T64, LZ4),
    cost_15pct Nullable(Decimal32(4)) COMMENT '15%成本位价格' CODEC(T64, LZ4),
    cost_50pct Nullable(Decimal32(4)) COMMENT '50%成本位价格' CODEC(T64, LZ4),
    cost_85pct Nullable(Decimal32(4)) COMMENT '85%成本位价格' CODEC(T64, LZ4),
    cost_95pct Nullable(Decimal32(4)) COMMENT '95%成本位价格' CODEC(T64, LZ4),

    weight_avg Nullable(Decimal32(4)) COMMENT '加权平均成本' CODEC(T64, LZ4),

    winner_rate Nullable(Decimal32(2)) COMMENT '胜率(%)' CODEC(T64, LZ4),
    
    ingest_time DateTime COMMENT '数据入库时间'

) ENGINE = MergeTree()
PARTITION BY toYYYYMM(trade_date)   -- 按年月分区
ORDER BY (instrument_id, trade_date) -- 按股票代码和日期排序，最核心的优化
SETTINGS index_granularity = 8192;