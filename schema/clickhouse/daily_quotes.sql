CREATE TABLE daily_quotes (

    instrument_id FixedString(9) COMMENT '股票代码, e.g., 000001.SZ' CODEC(LZ4),
    trade_date Date COMMENT '交易日期' CODEC(DoubleDelta, LZ4),

    open Decimal32(4) COMMENT '开盘价' CODEC(T64, LZ4),
    high Decimal32(4) COMMENT '最高价' CODEC(T64, LZ4),
    low Decimal32(4) COMMENT '最低价' CODEC(T64, LZ4),
    close Decimal32(4) COMMENT '收盘价' CODEC(T64, LZ4),

    volume UInt64 COMMENT '成交量(股)' CODEC(T64, LZ4),

    amount Decimal64(2) COMMENT '成交额(元)' CODEC(T64, LZ4),

    change Nullable(Decimal64(8)) COMMENT '涨跌额' CODEC(T64, LZ4),
    pct_chg Nullable(Decimal32(4)) COMMENT '涨跌幅' CODEC(T64, LZ4),

    adj_factor Nullable(Decimal64(6)) COMMENT '复权因子' CODEC(T64, LZ4),

    ingest_time DateTime DEFAULT now() COMMENT '数据入库时间'

) ENGINE = MergeTree()
PARTITION BY toYYYYMM(trade_date)
ORDER BY (instrument_id, trade_date)
SETTINGS index_granularity = 8192;