CREATE DATABASE IF NOT EXISTS seekalpha;
USE seekalpha;

CREATE TABLE `trade_strategies` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '策略唯一ID，主键',
  `strategy_name` VARCHAR(200) NOT NULL COMMENT '策略名称',
  `strategy_desc` TEXT NULL COMMENT '策略的详细描述',
  `factor_names` TEXT NULL COMMENT '因子名称列表，JSON格式储存',
  `factor_expressions` TEXT NULL COMMENT '因子表达式列表，JSON格式储存',
  `extra_params` TEXT NULL COMMENT '策略所需的额外参数，以JSON格式存储',
  `created_at` DATETIME NOT NULL COMMENT '记录创建时间',
  `updated_at` DATETIME NULL COMMENT '记录更新时间'

) COMMENT='交易策略表';

CREATE UNIQUE INDEX `uidx_strategy_name` ON `trade_strategies` (`strategy_name`);


 
 