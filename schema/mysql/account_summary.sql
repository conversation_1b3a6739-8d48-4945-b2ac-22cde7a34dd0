CREATE DATABASE IF NOT EXISTS seekalpha;
USE seekalpha;
CREATE TABLE `account_summary` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
  `account_id` VARCHAR(50) NOT NULL COMMENT '账户ID',
  `total_asset` DECIMAL(20, 2) NOT NULL COMMENT '总资产 (cash + market_value)',
  `market_value` DECIMAL(20, 2) NOT NULL COMMENT '持仓总市值',
  `cash` DECIMAL(20, 2) NOT NULL COMMENT '可用现金',
  `date` DATE NOT NULL COMMENT '数据快照日期',
  `datetime` DATETIME NOT NULL COMMENT '数据快照时间'

) COMMENT='账户概览表';

CREATE UNIQUE INDEX `uidx_account_datetime` ON `account_summary` (`account_id`, `datetime`);
CREATE INDEX `idx_account_date` ON `account_summary` (`account_id`, `date`);
