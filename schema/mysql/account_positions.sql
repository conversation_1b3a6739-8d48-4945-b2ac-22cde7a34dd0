CREATE DATABASE IF NOT EXISTS seekalpha;
USE seekalpha;
CREATE TABLE `account_positions` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
  `account_id` VARCHAR(50) NOT NULL COMMENT '账户ID',
  `stock_code` VARCHAR(20) NOT NULL COMMENT '股票代码 (如 600377.SH)',
  `volume` INT UNSIGNED NOT NULL COMMENT '持仓股票数量',
  `can_use_volume` INT UNSIGNED NOT NULL COMMENT '可用股票数量',
  `frozen_volume` INT UNSIGNED NOT NULL COMMENT '冻结股票数量',
  `open_price` DECIMAL(12, 2) NOT NULL COMMENT '成本价 (含手续费)',
  `avg_price` DECIMAL(12, 2) NOT NULL COMMENT '平均成本价',
  `market_value` DECIMAL(20, 2) NOT NULL COMMENT '持仓市值 (当前价格 * 数量)',
  `date` DATE NOT NULL COMMENT '数据快照日期',
  `datetime` DATETIME NOT NULL COMMENT '数据快照时间'

) COMMENT='账户持仓详情表';

CREATE UNIQUE INDEX `uidx_account_stock_datetime` ON `account_positions` (`account_id`, `stock_code`, `datetime`);
CREATE INDEX `idx_account_date` ON `account_positions` (`account_id`, `date`);


