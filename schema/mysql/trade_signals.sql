CREATE DATABASE IF NOT EXISTS seekalpha;
USE seekalpha;

CREATE TABLE `trade_signals` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '信号唯一ID，主键',
  `strategy_id` BIGINT UNSIGNED NOT NULL COMMENT '关联的策略ID (外键)',
  `trade_date` DATE NOT NULL COMMENT '交易日期',
  `generated_time` DATETIME NOT NULL COMMENT '信号生成的确切时间',
  `stock_code` VARCHAR(20) NOT NULL COMMENT '股票代码',
  `order_type` INT NOT NULL COMMENT '订单类型 (例如：1-买入, 2-卖出)',
  `order_volume` INT UNSIGNED NOT NULL COMMENT '订单数量',
  `last_close_price` DECIMAL(10, 2) NOT NULL COMMENT '上一交易日收盘价',
  

  CONSTRAINT `fk_signal_strategy`
    FOREIGN KEY (`strategy_id`)
    REFERENCES `trade_strategies` (`id`)
    ON DELETE RESTRICT
    ON UPDATE CASCADE
) COMMENT='交易信号表';

CREATE INDEX `idx_trade_date` ON `trade_signals` (`trade_date`);
CREATE INDEX `idx_strategy_date` ON `trade_signals` (`strategy_id`, `trade_date`);