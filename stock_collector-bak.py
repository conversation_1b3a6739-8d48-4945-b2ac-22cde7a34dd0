import sys
import time
import os
import json
from datetime import datetime, timedelta, UTC
import pytz
from typing import Optional, Literal, List, Dict, Any, Callable

from dotenv import load_dotenv
import tushare as ts
import pandas as pd
from utils.clickhouse_client import ClickHouseClient
from app.server.core.log import logger


class StockDataCollector:
    """股票复权数据收集器"""
    
    def __init__(
        self, 
        token: str, 
        data_dir: str = "./stock_data", 
        clickhouse_client: ClickHouseClient = None,
        db_config: Dict[str, Any] = None
    ):
        """
        初始化股票数据收集器
        
        Args:
            token: Tushare Pro的token
            data_dir: 数据保存目录，默认为当前目录下的stock_data文件夹
            db_config: ClickHouse数据库配置
        """
        if not token:
            raise ValueError("Tushare token is required")
        
        self.token = token
        self.data_dir = data_dir
        
        # 设置tushare token
        ts.set_token(token)
        self.pro = ts.pro_api(token)
        
        # 确保数据目录存在
        os.makedirs(data_dir, exist_ok=True)
        
        # 初始化ClickHouse客户端
        if clickhouse_client:
            self.db_client = clickhouse_client
        else:
            if db_config:
                self.db_client = ClickHouseClient(**db_config)
            else:
                self.db_client = None
    
    def _download_market_data(
        self, 
        ts_code: str = '', 
        start_date: str = '', 
        end_date: str = '', 
        adj: Literal['qfq', 'hfq', None] = None, 
        freq: str = 'D', 
        ma: list = []
    ) -> pd.DataFrame:
        """下载行情数据的具体实现"""
        return ts.pro_bar(
            ts_code=ts_code,
            start_date=start_date,
            end_date=end_date,
            asset='E',  # 股票
            adj=adj,
            freq=freq,
            ma=ma
        )
    
    def _download_adj_factor_data(self, 
        ts_code: str = '', 
        start_date: str = '', 
        end_date: str = '', 
        trade_date: str = '', 
        **kwargs
    ) -> pd.DataFrame:
        """下载复权因子数据的具体实现"""
        return self.pro.adj_factor(
            ts_code=ts_code,
            start_date=start_date,
            end_date=end_date,
            trade_date=trade_date
        )
    
    def _download_cyq_perf_data(
        self, 
        ts_code: str = '', 
        start_date: str = '', 
        end_date: str = '', 
        trade_date: str = '', 
        **kwargs
    ) -> pd.DataFrame:
        """下载筹码数据的具体实现"""
        return self.pro.cyq_perf(
            ts_code=ts_code,
            start_date=start_date,
            end_date=end_date,
            trade_date=trade_date
        )
    
    def _download_moneyflow_data(
        self, 
        ts_code: str = '', 
        start_date: str = '', 
        end_date: str = '', 
        trade_date: str = '', 
        **kwargs
    ) -> pd.DataFrame:
        """下载资金流向数据的具体实现"""
        return self.pro.moneyflow(
            ts_code=ts_code,
            start_date=start_date,
            end_date=end_date,
            trade_date=trade_date
        )
    
    def _download_industry_data(self, ts_code: str, **kwargs) -> pd.DataFrame:
        """下载申万行业成分数据的具体实现"""
        return self.pro.index_member_all(
            ts_code=ts_code,
            is_new='Y'  # 默认获取最新数据
        )
    
    def _download_index_daily_data(
        self, 
        ts_code: str = '', 
        start_date: str = '', 
        end_date: str = '', 
        trade_date: str = '', 
        **kwargs
    ) -> pd.DataFrame:
        """下载指数行情数据的具体实现"""
        return self.pro.index_daily(
            ts_code=ts_code,
            start_date=start_date,
            end_date=end_date,
            trade_date=trade_date
        )
    
    def _get_data_download_config(self, data_type: str, **kwargs) -> Dict[str, Any]:
        """获取不同数据类型的下载配置"""
        configs = {
            'market': {
                'download_func': self._download_market_data,
                'dir_path': lambda **kwargs: os.path.join(self.data_dir, "market", kwargs.get('adj') if kwargs.get('adj') else "no_adj", kwargs.get('freq', 'D')),
                'description': '行情数据',
                'extra_params': ['adj', 'freq', 'ma']
            },
            'adj_factor': {
                'download_func': self._download_adj_factor_data,
                'dir_path': lambda **_: os.path.join(self.data_dir, "market", "adj_factor"),
                'description': '复权因子数据',
                'extra_params': []
            },
            'cyq_perf': {
                'download_func': self._download_cyq_perf_data,
                'dir_path': lambda **_: os.path.join(self.data_dir, "special", "cyq_perf"),
                'description': '筹码成本及其胜率数据',
                'extra_params': []
            },
            'moneyflow': {
                'download_func': self._download_moneyflow_data,
                'dir_path': lambda **_: os.path.join(self.data_dir, "moneyflow", "moneyflow"),
                'description': '资金流向数据',
                'extra_params': []
            },
            'industry': {
                'download_func': self._download_industry_data,
                'dir_path': lambda **_: os.path.join(self.data_dir, "index", "index_member_all"),
                'description': '申万行业成分数据',
                'extra_params': []
            },
            'index_daily': {
                'download_func': self._download_index_daily_data,
                'dir_path': lambda **_: os.path.join(self.data_dir, "index", "index_daily"),
                'description': '指数行情数据',
                'extra_params': []
            }
        }
        return configs.get(data_type)
    
    def _collect_single_stock_data(self, 
                                  ts_code: str,
                                  data_type: str,
                                  start_date: str = '',
                                  end_date: str = '',
                                  overwrite: bool = False,
                                  **kwargs) -> bool:
        """
        通用的单个股票数据收集方法
        
        Args:
            ts_code: 股票代码
            data_type: 数据类型 ('market' ,'adj_factor', 'cyq_perf', 'moneyflow', 'industry')
            start_date: 开始日期
            end_date: 结束日期
            overwrite: 是否覆盖已存在的文件
            **kwargs: 额外参数
            
        Returns:
            bool: 成功返回True，失败返回False
        """
        try:
            config = self._get_data_download_config(data_type, **kwargs)
            if not config:
                logger.error(f"不支持的数据类型: {data_type}")
                return False
            
            # 构建文件目录路径
            target_dir = config['dir_path'](**kwargs)
            os.makedirs(target_dir, exist_ok=True)
            
            # 统一的文件命名
            filename = f"{ts_code.replace('.', '_')}.csv"
            filepath = os.path.join(target_dir, filename)
            
            # 检查文件是否已存在
            if os.path.exists(filepath) and not overwrite:
                logger.info(f"文件 {filename} 已存在，跳过下载。如需覆盖请设置 overwrite=True")
                return True
            
            logger.info(f"正在获取股票 {ts_code} 的{config['description']}...")
            logger.info(f"日期范围: {start_date} 到 {end_date}")
            
            # 构建下载参数
            download_params = {
                'ts_code': ts_code,
                'start_date': start_date,
                'end_date': end_date
            }
            
            # 添加额外参数
            for param in config['extra_params']:
                if param in kwargs:
                    download_params[param] = kwargs[param]
            
            # 调用具体的下载函数
            df = config['download_func'](**download_params)
            
            if df is None or df.empty:
                logger.warning(f"未获取到股票 {ts_code} 的{config['description']}")
                return False
            
            # 保存到CSV文件
            df.to_csv(filepath, index=False, encoding='utf-8')
            logger.info(f"{config['description']}已保存到: {filepath}")
            logger.info(f"共获取 {len(df)} 条记录")
            
            # # 显示数据预览
            # logger.info(f"\n{config['description']}预览:")
            # logger.info(df.head())
            
            return True
            
        except Exception as e:
            logger.error(f"获取股票 {ts_code} {config['description']}时发生错误: {str(e)}")
            return False
    
    def collect_stock_data(self, 
                          ts_code: str,
                          start_date: Optional[str] = None,
                          end_date: Optional[str] = None,
                          adj: Literal['qfq', 'hfq', None] = None,
                          freq: str = 'D',
                          ma: list = [],
                          overwrite: bool = False) -> bool:
        """
        收集指定股票的复权数据并保存为CSV文件
        
        Args:
            ts_code: 股票代码，如 '000001.SZ'
            start_date: 开始日期，格式YYYYMMDD，默认为一年前
            end_date: 结束日期，格式YYYYMMDD，默认为今天
            adj: 复权类型，'qfq'前复权，'hfq'后复权，None不复权，默认不复权
            freq: 数据频度，'D'日线，'1MIN'/'5MIN'等分钟线，默认日线
            ma: 均线周期列表，如[5, 10, 20]
            overwrite: 是否覆盖已存在的文件，默认False
            
        Returns:
            bool: 成功返回True，失败返回False
        """
        return self._collect_single_stock_data(
            ts_code=ts_code,
            data_type='market',
            start_date=start_date or '',
            end_date=end_date or '',
            overwrite=overwrite,
            adj=adj,
            freq=freq,
            ma=ma
        )
    
    def collect_adj_factor(self,
                          ts_code: str,
                          start_date: str,
                          end_date: str,
                          overwrite: bool = False) -> bool:
        """
        收集股票复权因子数据并保存为CSV文件
        
        Args:
            ts_code: 股票代码，如 '000001.SZ'
            start_date: 开始日期，格式YYYYMMDD
            end_date: 结束日期，格式YYYYMMDD
            overwrite: 是否覆盖已存在的文件，默认False
            
        Returns:
            bool: 成功返回True，失败返回False
        """
        return self._collect_single_stock_data(
            ts_code=ts_code,
            data_type='adj_factor',
            start_date=start_date,
            end_date=end_date,
            overwrite=overwrite
        )
    
    def collect_cyq_perf(self,
                          ts_code: str,
                          start_date: str,
                          end_date: str,
                          overwrite: bool = False) -> bool:
        """
        收集股票筹码数据并保存为CSV文件
        
        Args:
            ts_code: 股票代码，如 '000001.SZ'
            start_date: 开始日期，格式YYYYMMDD
            end_date: 结束日期，格式YYYYMMDD
            overwrite: 是否覆盖已存在的文件，默认False
            
        Returns:
            bool: 成功返回True，失败返回False
        """
        return self._collect_single_stock_data(
            ts_code=ts_code,
            data_type='cyq_perf',
            start_date=start_date,
            end_date=end_date,
            overwrite=overwrite
        )
    
    def collect_moneyflow(self,
                          ts_code: str,
                          start_date: str,
                          end_date: str,
                          overwrite: bool = False) -> bool:
        """
        收集股票资金流向数据并保存为CSV文件
        
        Args:
            ts_code: 股票代码，如 '000001.SZ'
            start_date: 开始日期，格式YYYYMMDD
            end_date: 结束日期，格式YYYYMMDD
            overwrite: 是否覆盖已存在的文件，默认False
            
        Returns:
            bool: 成功返回True，失败返回False
        """
        return self._collect_single_stock_data(
            ts_code=ts_code,
            data_type='moneyflow',
            start_date=start_date,
            end_date=end_date,
            overwrite=overwrite
        )
    
    def collect_industry(self,
                        ts_code: str,
                        overwrite: bool = False) -> bool:
        """
        收集股票申万行业成分数据并保存为CSV文件
        
        Args:
            ts_code: 股票代码，如 '000001.SZ'
            overwrite: 是否覆盖已存在的文件，默认False
            
        Returns:
            bool: 成功返回True，失败返回False
        """
        return self._collect_single_stock_data(
            ts_code=ts_code,
            data_type='industry',
            start_date='',  # 行业数据不需要日期范围
            end_date='',
            overwrite=overwrite
        )
    
    def load_stock_codes_from_json(self, stock_codes_path: str) -> List[str]:
        """
        从JSON文件中加载股票代码列表
        
        Args:
            stock_codes_path: JSON文件路径
            
        Returns:
            股票代码列表
        """
        try:
            with open(stock_codes_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            stock_codes = data.get('stock_codes', [])
            logger.info(f"从 {stock_codes_path} 加载了 {len(stock_codes)} 个股票代码")
            return stock_codes
        except Exception as e:
            logger.error(f"加载股票代码JSON文件时发生错误: {str(e)}")
            return []
    
    def _batch_collect_data(self,
                           collect_func: Callable,
                           func_name: str,
                           stock_codes_path: str,
                           max_retries: int = 3,
                           **kwargs) -> Dict[str, bool]:
        """
        通用的批量数据收集方法
        
        Args:
            collect_func: 具体的数据收集函数
            func_name: 函数名称，用于日志显示
            stock_codes_path: 股票代码列表文件路径
            max_retries: 最大重试次数
            **kwargs: 传递给collect_func的参数
            
        Returns:
            每个股票代码的收集结果
        """
        stock_codes = self.load_stock_codes_from_json(stock_codes_path)
        results = {}
        total_count = len(stock_codes)
        
        logger.info(f"开始批量收集 {total_count} 个股票的{func_name}...")
        
        for i, ts_code in enumerate(stock_codes, 1):
            logger.info(f"\n进度: {i}/{total_count} - 正在处理股票: {ts_code}")
            
            retry_count = 0
            success = False
            
            while retry_count < max_retries and not success:
                try:
                    success = collect_func(ts_code=ts_code, **kwargs)
                    
                    if success:
                        results[ts_code] = True
                        logger.info(f"✓ {ts_code} {func_name}收集成功")
                    else:
                        retry_count += 1
                        if retry_count < max_retries:
                            logger.warning(f"✗ {ts_code} {func_name}收集失败，正在重试 ({retry_count}/{max_retries})")
                        else:
                            results[ts_code] = False
                            logger.error(f"✗ {ts_code} {func_name}收集失败，已达到最大重试次数")
                
                except Exception as e:
                    retry_count += 1
                    if retry_count < max_retries:
                        logger.warning(f"✗ {ts_code} {func_name}收集发生异常，正在重试 ({retry_count}/{max_retries}): {str(e)}")
                    else:
                        results[ts_code] = False
                        logger.error(f"✗ {ts_code} {func_name}收集发生异常，已达到最大重试次数: {str(e)}")
                
                time.sleep(0.2)
            
        
        # 统计结果
        success_count = sum(1 for success in results.values() if success)
        failed_count = total_count - success_count
        
        logger.info(f"\n批量{func_name}收集完成:")
        logger.info(f"成功: {success_count} 个股票")
        logger.info(f"失败: {failed_count} 个股票")
        if failed_count > 0:
            failed_codes = [code for code, success in results.items() if not success]
            logger.warning(f"失败的股票代码: {failed_codes}")
        
        return results
    
    def batch_collect_stock_data(self, 
                                stock_codes_path: str,
                                start_date: str,
                                end_date: str,
                                adj: Literal['qfq', 'hfq', None] = None,
                                freq: str = 'D',
                                overwrite: bool = False,
                                max_retries: int = 3) -> Dict[str, bool]:
        """
        批量收集股票数据
        
        Args:
            stock_codes_path: 股票代码列表文件路径
            start_date: 开始日期，格式YYYYMMDD
            end_date: 结束日期，格式YYYYMMDD  
            adj: 复权类型，'qfq'前复权，'hfq'后复权，None不复权
            freq: 数据频度，默认日线
            overwrite: 是否覆盖已存在的文件
            max_retries: 最大重试次数
            
        Returns:
            每个股票代码的收集结果
        """
        logger.info(f"日期范围: {start_date} 到 {end_date}")
        logger.info(f"复权类型: {adj if adj else '不复权'}")
        
        return self._batch_collect_data(
            collect_func=self.collect_stock_data,
            func_name="行情数据",
            stock_codes_path=stock_codes_path,
            max_retries=max_retries,
            start_date=start_date,
            end_date=end_date,
            adj=adj,
            freq=freq,
            overwrite=overwrite
        )
    
    def batch_collect_adj_factors(self,
                                 stock_codes_path: str, 
                                 start_date: str,
                                 end_date: str,
                                 overwrite: bool = False,
                                 max_retries: int = 3) -> Dict[str, bool]:
        """
        批量收集复权因子数据
        
        Args:
            stock_codes_path: 股票代码列表文件路径
            start_date: 开始日期，格式YYYYMMDD
            end_date: 结束日期，格式YYYYMMDD
            overwrite: 是否覆盖已存在的文件
            max_retries: 最大重试次数
            
        Returns:
            每个股票代码的收集结果
        """
        logger.info(f"日期范围: {start_date} 到 {end_date}")
        
        return self._batch_collect_data(
            collect_func=self.collect_adj_factor,
            func_name="复权因子数据",
            stock_codes_path=stock_codes_path,
            max_retries=max_retries,
            start_date=start_date,
            end_date=end_date,
            overwrite=overwrite
        )
    
    def batch_collect_cyq_perf(self,
                                 stock_codes_path: str, 
                                 start_date: str,
                                 end_date: str,
                                 overwrite: bool = False,
                                 max_retries: int = 3) -> Dict[str, bool]:
        """
        批量收集筹码数据
        
        Args:
            stock_codes_path: 股票代码列表文件路径
            start_date: 开始日期，格式YYYYMMDD
            end_date: 结束日期，格式YYYYMMDD
            overwrite: 是否覆盖已存在的文件
            max_retries: 最大重试次数
            
        Returns:
            每个股票代码的收集结果
        """
        logger.info(f"日期范围: {start_date} 到 {end_date}")
        
        return self._batch_collect_data(
            collect_func=self.collect_cyq_perf,
            func_name="筹码数据",
            stock_codes_path=stock_codes_path,
            max_retries=max_retries,
            start_date=start_date,
            end_date=end_date,
            overwrite=overwrite
        )
    
    def batch_collect_moneyflow(self,
                                stock_codes_path: str, 
                                start_date: str,
                                end_date: str,
                                overwrite: bool = False,
                                max_retries: int = 3) -> Dict[str, bool]:
        """
        批量收集资金流向数据
        
        Args:
            stock_codes_path: 股票代码列表文件路径
            start_date: 开始日期，格式YYYYMMDD
            end_date: 结束日期，格式YYYYMMDD
            overwrite: 是否覆盖已存在的文件
            max_retries: 最大重试次数
            
        Returns:
            每个股票代码的收集结果
        """
        logger.info(f"日期范围: {start_date} 到 {end_date}")
        
        return self._batch_collect_data(
            collect_func=self.collect_moneyflow,
            func_name="资金流向数据",
            stock_codes_path=stock_codes_path,
            max_retries=max_retries,
            start_date=start_date,
            end_date=end_date,
            overwrite=overwrite
        )
    
    def batch_collect_industry(self,
                              stock_codes_path: str, 
                              overwrite: bool = False,
                              max_retries: int = 3) -> Dict[str, bool]:
        """
        批量收集申万行业成分数据
        
        Args:
            stock_codes_path: 股票代码列表文件路径
            overwrite: 是否覆盖已存在的文件
            max_retries: 最大重试次数
            
        Returns:
            每个股票代码的收集结果
        """
        return self._batch_collect_data(
            collect_func=self.collect_industry,
            func_name="申万行业成分数据",
            stock_codes_path=stock_codes_path,
            max_retries=max_retries,
            overwrite=overwrite
        )
    
    def process_and_import_market_data_to_clickhouse(
        self, 
        stock_codes_path: str,
        trade_calendar_path: str = None,
        table_name: str = "daily_quotes",
        batch_size: int = 1000, 
        is_index: bool = False
    ) -> Dict[str, int]:
        """
        处理CSV文件并导入到ClickHouse数据库
        
        Args:
            stock_codes_path: 股票代码列表文件路径
            trade_calendar_path: 交易日历文件路径
            table_name: ClickHouse表名
            batch_size: 批量插入的大小
            
        Returns:
            每个股票代码导入的记录数
        """
        stock_codes = self.load_stock_codes_from_json(stock_codes_path)
        if not self.db_client:
            logger.error("错误: 未配置ClickHouse客户端")
            return {}
        
        # 加载交易日历
        if not trade_calendar_path or not os.path.exists(trade_calendar_path):
            logger.error("错误: 必须提供有效的交易日历文件路径")
            raise ValueError("计算change和return必须提供交易日历")
            
        try:
            logger.info(f"正在加载交易日历: {trade_calendar_path}")
            trade_calendar = pd.read_csv(trade_calendar_path)
            logger.info(f"成功加载交易日历，共 {len(trade_calendar)} 条记录")
        except Exception as e:
            logger.error(f"加载交易日历失败: {str(e)}")
            raise
        
        results = {}
        total_count = len(stock_codes)

        if is_index:
            no_adj_dir = os.path.join(self.data_dir, "index", "index_daily")
        else:
            no_adj_dir = os.path.join(self.data_dir, "market", "no_adj", "D")
            adj_factor_dir = os.path.join(self.data_dir, "market", "adj_factor")
            
        logger.info(f"开始处理并导入 {total_count} 个{'指数' if is_index else '股票'}的数据到ClickHouse...")
        
        for i, ts_code in enumerate(stock_codes):
            logger.info(f"\n进度: {i+1}/{total_count} - 正在处理{'指数' if is_index else '股票'}: {ts_code}")
            
            try:
                # 读取未复权行情数据
                no_adj_file = os.path.join(no_adj_dir, f"{ts_code.replace('.', '_')}.csv")
                if not os.path.exists(no_adj_file):
                    logger.error(f"✗ 未找到{'指数' if is_index else '股票'} {ts_code} 的{'行情' if is_index else '未复权'}数据文件: {no_adj_file}")
                    results[ts_code] = 0
                    continue
                
                if not is_index:
                    # 读取复权因子数据
                    adj_factor_file = os.path.join(adj_factor_dir, f"{ts_code.replace('.', '_')}.csv")
                    if not os.path.exists(adj_factor_file):
                        logger.error(f"✗ 未找到股票 {ts_code} 的复权因子文件: {adj_factor_file}")
                        results[ts_code] = 0
                        continue
                    # 读取复权因子数据
                    adj_factor_df = pd.read_csv(adj_factor_file)
                
                # 读取未复权数据
                quotes_df = pd.read_csv(no_adj_file)
                
                if not is_index:
                    # 合并数据
                    merged_df = pd.merge(
                        quotes_df, 
                        adj_factor_df[['ts_code', 'trade_date', 'adj_factor']], 
                        on=['ts_code', 'trade_date'], 
                        how='left'
                    )
                    
                else:
                    # merged_df = quotes_df
                    # 做成跟merged_df一行格式的df
                    merged_df = quotes_df

                if merged_df.empty:
                    logger.warning(f"✗ {'指数' if is_index else '股票'} {ts_code} 合并后数据为空")
                    results[ts_code] = 0
                    continue
                # 处理数据格式
                processed_data = self._process_market_data_for_clickhouse(
                    merged_df, 
                    trade_calendar, 
                    is_index=is_index
                )
                
                if not processed_data:
                    logger.warning(f"✗ {'指数' if is_index else '股票'} {ts_code} 处理后数据为空")
                    results[ts_code] = 0
                    continue
                
                # 批量插入数据
                imported_count = self._batch_insert_to_clickhouse(processed_data, table_name, batch_size)
                results[ts_code] = imported_count
                
                logger.info(f"✓ {ts_code} 成功导入 {imported_count} 条记录")
                
            except Exception as e:
                logger.error(f"✗ 处理股票 {ts_code} 时发生错误: {str(e)}")
                results[ts_code] = 0
        
        # 统计结果
        total_imported = sum(results.values())
        success_count = sum(1 for count in results.values() if count > 0)
        failed_count = total_count - success_count
        
        logger.info(f"\n数据导入完成:")
        logger.info(f"成功导入{'指数' if is_index else '股票'}数: {success_count}")
        logger.info(f"失败{'指数' if is_index else '股票'}数: {failed_count}")
        logger.info(f"总导入记录数: {total_imported}")
        
        return results
    
    def _process_market_data_for_clickhouse(self, df: pd.DataFrame, trade_calendar: pd.DataFrame, is_index: bool = False) -> List[Dict[str, Any]]:
        """
        处理数据格式以适配ClickHouse表结构
        
        Args:
            df: 原始数据DataFrame
            trade_calendar: 交易日历DataFrame，必须提供
            
        Returns:
            处理后的数据列表
        """
        try:
            processed_data = []
            skipped_rows = 0
            
            # 定义必需的行情数据字段
            required_price_fields = ['open', 'high', 'low', 'close']
            required_volume_fields = ['vol', 'amount']
            
            # 数据按交易日期排序
            df = df.sort_values('trade_date').reset_index(drop=True)
            
            # 准备交易日历映射（cal_date -> pretrade_date）
            calendar_map = {}
            try:
                for _, row in trade_calendar.iterrows():
                    calendar_map[str(int(row['cal_date']))] = str(int(row['pretrade_date']))
                    
                if not calendar_map:
                    logger.error("交易日历映射为空")
                    raise ValueError("交易日历映射为空")
            except Exception as e:
                logger.error(f"处理交易日历映射时出错: {str(e)}")
                raise
            
            # 计算change和return
            df['calculated_change'] = None
            df['calculated_return'] = None
            
            for i in range(len(df)):
                current_date = str(int(df.iloc[i]['trade_date']))
                current_close = df.iloc[i]['close']
                
                # 获取前一个交易日
                prev_trade_date = None
                if current_date in calendar_map:
                    # 使用交易日历获取前一个交易日
                    prev_trade_date = calendar_map[current_date]
                else:
                    logger.warning(f"交易日 {current_date} 在交易日历中未找到对应的前一交易日")
                
                # 查找前一个交易日的收盘价
                prev_close = None
                if prev_trade_date:
                    prev_row = df[df['trade_date'] == int(prev_trade_date)]
                    if prev_row is not None and not prev_row.empty:
                        prev_close = prev_row.iloc[0]['close']
                
                # 计算change和return
                if prev_close is not None and not pd.isna(prev_close) and not pd.isna(current_close):
                    df.at[i, 'calculated_change'] = current_close - prev_close
                    if prev_close != 0:
                        df.at[i, 'calculated_return'] = (current_close - prev_close) / prev_close
                    else:
                        df.at[i, 'calculated_return'] = None
                else:
                    df.at[i, 'calculated_change'] = None
                    df.at[i, 'calculated_return'] = None
            
            for _, row in df.iterrows():
                # 跳过缺失关键数据的行
                if pd.isna(row['ts_code']) or pd.isna(row['trade_date']):
                    skipped_rows += 1
                    continue
                
                # 检查价格数据完整性 - 开盘价、最高价、最低价、收盘价都不能为空
                price_missing = any(pd.isna(row[field]) for field in required_price_fields)
                if price_missing:
                    missing_fields = [field for field in required_price_fields if pd.isna(row[field])]
                    logger.warning(f"  跳过 {row['ts_code']} {row['trade_date']}: 价格数据缺失 {missing_fields}")
                    skipped_rows += 1
                    continue
                
                # 检查成交量和成交额数据完整性
                volume_missing = any(pd.isna(row[field]) for field in required_volume_fields)
                if volume_missing:
                    missing_fields = [field for field in required_volume_fields if pd.isna(row[field])]
                    logger.warning(f"  跳过 {row['ts_code']} {row['trade_date']}: 成交数据缺失 {missing_fields}")
                    skipped_rows += 1
                    continue
                
                # 检查复权因子精度范围 - Decimal64(6)，合理范围应该在0.000001到999999.999999之间
                if not is_index and not pd.isna(row['adj_factor']):
                    adj_factor_val = float(row['adj_factor'])
                    if adj_factor_val <= 0 or adj_factor_val > 999999.999999:
                        logger.warning(f"  跳过 {row['ts_code']} {row['trade_date']}: 复权因子超出合理范围 {adj_factor_val}")
                        skipped_rows += 1
                        continue
                
                # 检查数据合理性
                if row['vol'] < 0 or row['amount'] < 0:
                    logger.warning(f"  跳过 {row['ts_code']} {row['trade_date']}: 成交数据异常 (vol={row['vol']}, amount={row['amount']})")
                    skipped_rows += 1
                    continue
                
                # 检查价格数据合理性 - 价格应该大于0
                if any(row[field] <= 0 for field in required_price_fields):
                    invalid_prices = {field: row[field] for field in required_price_fields if row[field] <= 0}
                    logger.warning(f"  跳过 {row['ts_code']} {row['trade_date']}: 价格数据异常 {invalid_prices}")
                    skipped_rows += 1
                    continue
                
                # 检查价格数据精度范围 - Decimal32(4)的最大值约为99999999.9999
                max_price = 99999999.9999
                if any(row[field] > max_price for field in required_price_fields):
                    invalid_prices = {field: row[field] for field in required_price_fields if row[field] > max_price}
                    logger.warning(f"  跳过 {row['ts_code']} {row['trade_date']}: 价格数据超出精度范围 {invalid_prices}")
                    skipped_rows += 1
                    continue
                
                # 处理交易日期格式
                trade_date_str = str(int(row['trade_date']))
                trade_date = datetime.strptime(trade_date_str, '%Y%m%d').date()
                
                # 构建数据记录，精确控制小数位数以匹配ClickHouse表结构
                record = {
                    'instrument_id': row['ts_code'],
                    'trade_date': trade_date,
                    # 价格数据：Decimal32(4) - 保留4位小数
                    'open': self._to_decimal(row['open'], precision=4),
                    'high': self._to_decimal(row['high'], precision=4),
                    'low': self._to_decimal(row['low'], precision=4),
                    'close': self._to_decimal(row['close'], precision=4),
                    'volume': self._to_int(row['vol'], multiplier=100), # 成交量单位：手 -> 股 (1手 = 100股)
                    'amount': self._to_decimal(row['amount'], multiplier=1000, precision=2),  # tushare原始单位是千元，转换为元
                    # 涨跌额：Decimal64(8) - 保留8位小数，涨跌幅：Decimal32(4) - 保留4位小数
                    'change': self._to_decimal(row['calculated_change'], precision=8),
                    'pct_chg': self._to_decimal(row['calculated_return'], precision=4),
                    'ingest_time': datetime.now(UTC)
                }

                if not is_index:
                    # 复权因子：Decimal64(6) - 保留6位小数
                    record['adj_factor'] = self._to_decimal(row['adj_factor'], precision=6)
                
                processed_data.append(record)
            
            if skipped_rows > 0:
                logger.warning(f"  数据质量检查完成: 跳过了 {skipped_rows} 行不完整或异常的数据")
            
            return processed_data
            
        except Exception as e:
            logger.error(f"处理数据时发生错误: {str(e)}")
            return []
    
    def _batch_insert_to_clickhouse(self, data: List[Dict[str, Any]], table_name: str, batch_size: int) -> int:
        """
        批量插入数据到ClickHouse
        
        Args:
            data: 要插入的数据列表
            table_name: 表名
            batch_size: 批量大小
            
        Returns:
            成功插入的记录数
        """
        try:

            total_inserted = 0
            total_batches = len(data) // batch_size + (1 if len(data) % batch_size > 0 else 0)
            
            for i in range(0, len(data), batch_size):
                batch_data = data[i:i + batch_size]
                batch_num = i // batch_size + 1
                
                success = self.db_client.insert_many(table_name, batch_data)
                if success:
                    total_inserted += len(batch_data)
                    logger.info(f"  批次 {batch_num}/{total_batches}: 成功插入 {len(batch_data)} 条记录")
                else:
                    logger.error(f"  批次 {batch_num}/{total_batches}: 插入失败")
            
            return total_inserted
            
        except Exception as e:
            logger.error(f"批量插入数据时发生错误: {str(e)}")
            return 0
    
    def process_and_import_cyq_to_clickhouse(self, 
                                           stock_codes_path: str,
                                           table_name: str = "daily_chips",
                                           batch_size: int = 1000) -> Dict[str, int]:
        """
        处理筹码数据CSV文件并导入到ClickHouse数据库
        
        Args:
            stock_codes_path: 股票代码列表文件路径
            table_name: ClickHouse表名
            batch_size: 批量插入的大小
            
        Returns:
            每个股票代码导入的记录数
        """
        stock_codes = self.load_stock_codes_from_json(stock_codes_path)
        if not self.db_client:
            logger.error("错误: 未配置ClickHouse客户端")
            return {}
        
        results = {}
        total_count = len(stock_codes)

        cyq_perf_dir = os.path.join(self.data_dir, "special", "cyq_perf")
        
            
        logger.info(f"开始处理并导入 {total_count} 个股票的筹码数据到ClickHouse...")
        
        for i, ts_code in enumerate(stock_codes):
            logger.info(f"\n进度: {i}/{total_count} - 正在处理股票: {ts_code}")
            
            try:
                # 读取筹码数据
                cyq_perf_file = os.path.join(cyq_perf_dir, f"{ts_code.replace('.', '_')}.csv")
                if not os.path.exists(cyq_perf_file):
                    logger.error(f"✗ 未找到股票 {ts_code} 的筹码数据文件: {cyq_perf_file}")
                    results[ts_code] = 0
                    continue
                
                # 读取数据
                cyq_df = pd.read_csv(cyq_perf_file)
                
                if cyq_df.empty:
                    logger.warning(f"✗ 股票 {ts_code} 筹码数据为空")
                    results[ts_code] = 0
                    continue
                
                # 处理数据格式
                processed_data = self._process_cyq_data_for_clickhouse(cyq_df)
                
                if not processed_data:
                    logger.warning(f"✗ 股票 {ts_code} 处理后筹码数据为空")
                    results[ts_code] = 0
                    continue
                
                # 批量插入数据
                imported_count = self._batch_insert_to_clickhouse(processed_data, table_name, batch_size)
                results[ts_code] = imported_count
                
                logger.info(f"✓ {ts_code} 成功导入 {imported_count} 条筹码记录")
                
            except Exception as e:
                logger.error(f"✗ 处理股票 {ts_code} 筹码数据时发生错误: {str(e)}")
                results[ts_code] = 0
        
        # 统计结果
        total_imported = sum(results.values())
        success_count = sum(1 for count in results.values() if count > 0)
        failed_count = total_count - success_count
        
        logger.info(f"\n筹码数据导入完成:")
        logger.info(f"成功导入股票数: {success_count}")
        logger.info(f"失败股票数: {failed_count}")
        logger.info(f"总导入记录数: {total_imported}")
        
        return results
    
    def _to_int(self, value: float, multiplier: int = None) -> Optional[int]:
        """
        将浮点数转换为整数，支持单位转换
        """
        if multiplier is None:
            return int(value) if not pd.isna(value) else None
        else:
            return int(value * multiplier) if not pd.isna(value) else None
    
    def _to_decimal(self, value: float, multiplier: float = None, precision: int = 2) -> Optional[float]:
        """
        将浮点数转换为指定精度的小数，支持单位转换
        """
        if pd.isna(value):
            return None
        if multiplier is None:
            return round(float(value), precision)
        else:
            return round(float(value * multiplier), precision)
    
    def _process_cyq_data_for_clickhouse(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        处理筹码数据格式以适配ClickHouse表结构
        
        Args:
            df: 筹码数据DataFrame
            
        Returns:
            处理后的数据列表
        """
        try:
            processed_data = []
            skipped_rows = 0
            
            
            for _, row in df.iterrows():
                # 跳过缺失关键数据的行
                if pd.isna(row['ts_code']) or pd.isna(row['trade_date']):
                    skipped_rows += 1
                    continue
                
                # 检查价格数据合理性 - 价格应该大于0
                price_fields = ['his_low', 'his_high', 'cost_5pct', 'cost_15pct', 'cost_50pct', 
                               'cost_85pct', 'cost_95pct', 'weight_avg']
                invalid_prices = {}
                for field in price_fields:
                    if not pd.isna(row[field]) and row[field] <= 0:
                        invalid_prices[field] = row[field]
                
                if invalid_prices:
                    logger.warning(f"  跳过 {row['ts_code']} {row['trade_date']}: 价格数据异常 {invalid_prices}")
                    skipped_rows += 1
                    continue
                
                # 检查价格数据精度范围 - Decimal32(4)的最大值约为99999999.9999
                max_price = 99999999.9999
                oversized_prices = {}
                for field in price_fields:
                    if not pd.isna(row[field]) and row[field] > max_price:
                        oversized_prices[field] = row[field]
                
                if oversized_prices:
                    logger.warning(f"  跳过 {row['ts_code']} {row['trade_date']}: 价格数据超出精度范围 {oversized_prices}")
                    skipped_rows += 1
                    continue
                
                # 检查胜率范围 (0-100)
                if not pd.isna(row['winner_rate']) and (row['winner_rate'] < 0 or row['winner_rate'] > 100):
                    logger.warning(f"  跳过 {row['ts_code']} {row['trade_date']}: 胜率超出合理范围 {row['winner_rate']}")
                    skipped_rows += 1
                    continue
                
                # 检查价格逻辑：his_low <= his_high, cost分位数应该递增
                if not pd.isna(row['his_low']) and not pd.isna(row['his_high']) and row['his_low'] > row['his_high']:
                    logger.warning(f"  跳过 {row['ts_code']} {row['trade_date']}: 历史最低价大于最高价")
                    skipped_rows += 1
                    continue
                
                
                # 处理交易日期格式
                trade_date_str = str(int(row['trade_date']))
                trade_date = datetime.strptime(trade_date_str, '%Y%m%d').date()
                
                # 构建数据记录，精确控制小数位数以匹配ClickHouse表结构
                record = {
                    'instrument_id': row['ts_code'],
                    'trade_date': trade_date,
                    # 历史价格区间：Decimal32(4) - 保留4位小数
                    'his_low': self._to_decimal(row['his_low'], precision=4),
                    'his_high': self._to_decimal(row['his_high'], precision=4),
                    # 筹码成本价：Decimal32(4) - 保留4位小数
                    'cost_5pct': self._to_decimal(row['cost_5pct'], precision=4),
                    'cost_15pct': self._to_decimal(row['cost_15pct'], precision=4),
                    'cost_50pct': self._to_decimal(row['cost_50pct'], precision=4),
                    'cost_85pct': self._to_decimal(row['cost_85pct'], precision=4),
                    'cost_95pct': self._to_decimal(row['cost_95pct'], precision=4),
                    # 平均成本：Decimal32(4) - 保留4位小数
                    'weight_avg': self._to_decimal(row['weight_avg'], precision=4),
                    # 胜率：Decimal32(2) - 保留2位小数
                    'winner_rate': self._to_decimal(row['winner_rate'], precision=2),
                    'ingest_time': datetime.now(UTC)
                }
                
                processed_data.append(record)
            
            if skipped_rows > 0:
                logger.warning(f"  筹码数据质量检查完成: 跳过了 {skipped_rows} 行不完整或异常的数据")
            
            return processed_data
            
        except Exception as e:
            logger.error(f"处理筹码数据时发生错误: {str(e)}")
            return []
    
    def process_and_import_moneyflow_to_clickhouse(self, 
                                                 stock_codes_path: str,
                                                 table_name: str = "daily_money_flow",
                                                 batch_size: int = 1000) -> Dict[str, int]:
        """
        处理资金流向数据CSV文件并导入到ClickHouse数据库
        
        Args:
            stock_codes_path: 股票代码列表文件路径
            table_name: ClickHouse表名
            batch_size: 批量插入的大小
            
        Returns:
            每个股票代码导入的记录数
        """
        stock_codes = self.load_stock_codes_from_json(stock_codes_path)
        if not self.db_client:
            logger.error("错误: 未配置ClickHouse客户端")
            return {}
        
        results = {}
        total_count = len(stock_codes)

        moneyflow_dir = os.path.join(self.data_dir, "moneyflow", "moneyflow")
        
        logger.info(f"开始处理并导入 {total_count} 个股票的资金流向数据到ClickHouse...")
        
        for i, ts_code in enumerate(stock_codes):
            logger.info(f"\n进度: {i}/{total_count} - 正在处理股票: {ts_code}")
            
            try:
                # 读取资金流向数据
                moneyflow_file = os.path.join(moneyflow_dir, f"{ts_code.replace('.', '_')}.csv")
                if not os.path.exists(moneyflow_file):
                    logger.error(f"✗ 未找到股票 {ts_code} 的资金流向数据文件: {moneyflow_file}")
                    results[ts_code] = 0
                    continue
                
                # 读取数据
                moneyflow_df = pd.read_csv(moneyflow_file)
                
                if moneyflow_df.empty:
                    logger.warning(f"✗ 股票 {ts_code} 资金流向数据为空")
                    results[ts_code] = 0
                    continue
                
                # 处理数据格式
                processed_data = self._process_moneyflow_data_for_clickhouse(moneyflow_df)
                
                if not processed_data:
                    logger.warning(f"✗ 股票 {ts_code} 处理后资金流向数据为空")
                    results[ts_code] = 0
                    continue
                
                # 批量插入数据
                imported_count = self._batch_insert_to_clickhouse(processed_data, table_name, batch_size)
                results[ts_code] = imported_count
                
                logger.info(f"✓ {ts_code} 成功导入 {imported_count} 条资金流向记录")
                
            except Exception as e:
                logger.error(f"✗ 处理股票 {ts_code} 资金流向数据时发生错误: {str(e)}")
                results[ts_code] = 0
        
        # 统计结果
        total_imported = sum(results.values())
        success_count = sum(1 for count in results.values() if count > 0)
        failed_count = total_count - success_count
        
        logger.info(f"\n资金流向数据导入完成:")
        logger.info(f"成功导入股票数: {success_count}")
        logger.info(f"失败股票数: {failed_count}")
        logger.info(f"总导入记录数: {total_imported}")
        
        return results
    
    def _process_moneyflow_data_for_clickhouse(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        处理资金流向数据格式以适配ClickHouse表结构
        
        Args:
            df: 资金流向数据DataFrame
            
        Returns:
            处理后的数据列表
        """
        try:
            processed_data = []
            skipped_rows = 0
            
            for _, row in df.iterrows():
                # 跳过缺失关键数据的行
                if pd.isna(row['ts_code']) or pd.isna(row['trade_date']):
                    skipped_rows += 1
                    continue
                
                # 检查成交量数据合理性 - 成交量应该大于等于0
                volume_fields = ['buy_sm_vol', 'sell_sm_vol', 'buy_md_vol', 'sell_md_vol', 
                               'buy_lg_vol', 'sell_lg_vol', 'buy_elg_vol', 'sell_elg_vol', 'net_mf_vol']
                
                invalid_volumes = {}
                for field in volume_fields:
                    if not pd.isna(row[field]) and row[field] < 0:
                        # net_mf_vol 允许为负数，其他字段不允许
                        if field != 'net_mf_vol':
                            invalid_volumes[field] = row[field]
                
                if invalid_volumes:
                    logger.warning(f"  跳过 {row['ts_code']} {row['trade_date']}: 成交量数据异常 {invalid_volumes}")
                    skipped_rows += 1
                    continue
                
                # 检查成交金额数据合理性 - 成交金额应该大于等于0
                amount_fields = ['buy_sm_amount', 'sell_sm_amount', 'buy_md_amount', 'sell_md_amount', 
                               'buy_lg_amount', 'sell_lg_amount', 'buy_elg_amount', 'sell_elg_amount', 'net_mf_amount']
                
                invalid_amounts = {}
                for field in amount_fields:
                    if not pd.isna(row[field]) and row[field] < 0:
                        # net_mf_amount 允许为负数，其他字段不允许
                        if field != 'net_mf_amount':
                            invalid_amounts[field] = row[field]
                
                if invalid_amounts:
                    logger.warning(f"  跳过 {row['ts_code']} {row['trade_date']}: 成交金额数据异常 {invalid_amounts}")
                    skipped_rows += 1
                    continue
                
                # 处理交易日期格式
                trade_date_str = str(int(row['trade_date']))
                trade_date = datetime.strptime(trade_date_str, '%Y%m%d').date()
                
                # 构建数据记录，进行单位转换：手→股(*100)，万元→元(*10000)
                record = {
                    'instrument_id': row['ts_code'],
                    'trade_date': trade_date,
                    # 小单数据转换：手→股，万元→元
                    'buy_sm_vol': self._to_int(row['buy_sm_vol'], 100),
                    'buy_sm_amount': self._to_decimal(row['buy_sm_amount'], 10000),
                    'sell_sm_vol': self._to_int(row['sell_sm_vol'], 100),
                    'sell_sm_amount': self._to_decimal(row['sell_sm_amount'], 10000),
                    # 中单数据转换：手→股，万元→元
                    'buy_md_vol': self._to_int(row['buy_md_vol'], 100),
                    'buy_md_amount': self._to_decimal(row['buy_md_amount'], 10000),
                    'sell_md_vol': self._to_int(row['sell_md_vol'], 100),
                    'sell_md_amount': self._to_decimal(row['sell_md_amount'], 10000),
                    # 大单数据转换：手→股，万元→元
                    'buy_lg_vol': self._to_int(row['buy_lg_vol'], 100),
                    'buy_lg_amount': self._to_decimal(row['buy_lg_amount'], 10000),
                    'sell_lg_vol': self._to_int(row['sell_lg_vol'], 100),
                    'sell_lg_amount': self._to_decimal(row['sell_lg_amount'], 10000),
                    # 特大单数据转换：手→股，万元→元
                    'buy_elg_vol': self._to_int(row['buy_elg_vol'], 100),
                    'buy_elg_amount': self._to_decimal(row['buy_elg_amount'], 10000),
                    'sell_elg_vol': self._to_int(row['sell_elg_vol'], 100),
                    'sell_elg_amount': self._to_decimal(row['sell_elg_amount'], 10000),
                    # 净流入数据转换：手→股，万元→元（允许为负数）
                    'net_mf_vol': self._to_int(row['net_mf_vol'], 100),
                    'net_mf_amount': self._to_decimal(row['net_mf_amount'], 10000),
                    'ingest_time': datetime.now(UTC)
                }
                
                processed_data.append(record)
            
            if skipped_rows > 0:
                logger.warning(f"  资金流向数据质量检查完成: 跳过了 {skipped_rows} 行不完整或异常的数据")
            
            return processed_data
            
        except Exception as e:
            logger.error(f"处理资金流向数据时发生错误: {str(e)}")
            return []
    
    def process_and_import_industry_to_clickhouse(self, 
                                                stock_codes_path: str,
                                                table_name: str = "stock_industry",
                                                batch_size: int = 1000) -> Dict[str, int]:
        """
        处理申万行业成分数据CSV文件并导入到ClickHouse数据库
        
        Args:
            stock_codes_path: 股票代码列表文件路径
            table_name: ClickHouse表名
            batch_size: 批量插入的大小
            
        Returns:
            每个股票代码导入的记录数
        """
        stock_codes = self.load_stock_codes_from_json(stock_codes_path)
        if not self.db_client:
            logger.error("错误: 未配置ClickHouse客户端")
            return {}
        
        results = {}
        total_count = len(stock_codes)

        industry_dir = os.path.join(self.data_dir, "index", "index_member_all")
        
        logger.info(f"开始处理并导入 {total_count} 个股票的申万行业成分数据到ClickHouse...")
        
        for i, ts_code in enumerate(stock_codes):
            logger.info(f"\n进度: {i}/{total_count} - 正在处理股票: {ts_code}")
            
            try:
                # 读取行业成分数据
                industry_file = os.path.join(industry_dir, f"{ts_code.replace('.', '_')}.csv")
                if not os.path.exists(industry_file):
                    logger.error(f"✗ 未找到股票 {ts_code} 的行业成分数据文件: {industry_file}")
                    results[ts_code] = 0
                    continue
                
                # 读取数据
                industry_df = pd.read_csv(industry_file)
                
                if industry_df.empty:
                    logger.warning(f"✗ 股票 {ts_code} 行业成分数据为空")
                    results[ts_code] = 0
                    continue
                
                # 处理数据格式
                processed_data = self._process_industry_data_for_clickhouse(industry_df)
                
                if not processed_data:
                    logger.warning(f"✗ 股票 {ts_code} 处理后行业成分数据为空")
                    results[ts_code] = 0
                    continue
                
                # 批量插入数据
                imported_count = self._batch_insert_to_clickhouse(processed_data, table_name, batch_size)
                results[ts_code] = imported_count
                
                logger.info(f"✓ {ts_code} 成功导入 {imported_count} 条行业成分记录")
                
            except Exception as e:
                logger.error(f"✗ 处理股票 {ts_code} 行业成分数据时发生错误: {str(e)}")
                results[ts_code] = 0
            time.sleep(0.1)
        
        # 统计结果
        total_imported = sum(results.values())
        success_count = sum(1 for count in results.values() if count > 0)
        failed_count = total_count - success_count
        
        logger.info(f"\n申万行业成分数据导入完成:")
        logger.info(f"成功导入股票数: {success_count}")
        logger.info(f"失败股票数: {failed_count}")
        logger.info(f"总导入记录数: {total_imported}")
        
        return results
    
    def _process_industry_data_for_clickhouse(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        处理申万行业成分数据格式以适配ClickHouse表结构
        
        Args:
            df: 行业成分数据DataFrame
            
        Returns:
            处理后的数据列表
        """
        try:
            processed_data = []
            skipped_rows = 0
            
            for _, row in df.iterrows():
                # 跳过缺失关键数据的行
                if pd.isna(row['ts_code']) or pd.isna(row['name']):
                    skipped_rows += 1
                    continue
                
                # 检查纳入日期格式
                if pd.isna(row['in_date']):
                    logger.warning(f"  跳过 {row['ts_code']}: 纳入日期缺失")
                    skipped_rows += 1
                    continue
                
                # 处理日期格式
                in_date_str = str(int(row['in_date']))
                in_date = datetime.strptime(in_date_str, '%Y%m%d').date()

                
                out_date = None
                if not pd.isna(row['out_date']):
                    out_date_str = str(int(row['out_date']))
                    out_date = datetime.strptime(out_date_str, '%Y%m%d').date()

                # 处理是否最新标志
                is_new = None
                if not pd.isna(row['is_new']):
                    is_new = 1 if str(row['is_new']).upper() == 'Y' else 0
                
                # 构建数据记录
                record = {
                    'instrument_id': row['ts_code'],
                    'name': str(row['name']),
                    # 行业分类维度（可为空）
                    'l1_code': str(row['l1_code']) if not pd.isna(row['l1_code']) else None,
                    'l1_name': str(row['l1_name']) if not pd.isna(row['l1_name']) else None,
                    'l2_code': str(row['l2_code']) if not pd.isna(row['l2_code']) else None,
                    'l2_name': str(row['l2_name']) if not pd.isna(row['l2_name']) else None,
                    'l3_code': str(row['l3_code']) if not pd.isna(row['l3_code']) else None,
                    'l3_name': str(row['l3_name']) if not pd.isna(row['l3_name']) else None,
                    # 关系生命周期
                    'in_date': in_date,
                    'out_date': out_date,
                    # 状态标志
                    'is_new': is_new,
                    'ingest_time': datetime.now(UTC)
                }
                
                processed_data.append(record)
            
            if skipped_rows > 0:
                logger.warning(f"  行业成分数据质量检查完成: 跳过了 {skipped_rows} 行不完整或异常的数据")
            
            return processed_data
            
        except Exception as e:
            logger.error(f"处理行业成分数据时发生错误: {str(e)}")
            return []
    
    def collect_index_daily(self,
                           ts_code: str,
                           start_date: str = '',
                           end_date: str = '',
                           overwrite: bool = False) -> bool:
        """
        收集指数行情数据并保存为CSV文件
        
        Args:
            ts_code: 指数代码，如 '000852.SH'
            start_date: 开始日期，格式YYYYMMDD
            end_date: 结束日期，格式YYYYMMDD
            overwrite: 是否覆盖已存在的文件，默认False
            
        Returns:
            bool: 成功返回True，失败返回False
        """
        return self._collect_single_stock_data(
            ts_code=ts_code,
            data_type='index_daily',
            start_date=start_date,
            end_date=end_date,
            overwrite=overwrite
        )
    
    def batch_collect_index_daily(self,
                                 stock_codes_path: str,
                                 start_date: str,
                                 end_date: str,
                                 overwrite: bool = False,
                                 max_retries: int = 3) -> Dict[str, bool]:
        """
        批量收集指数行情数据
        
        Args:
            stock_codes_path: 指数代码列表文件路径
            start_date: 开始日期，格式YYYYMMDD
            end_date: 结束日期，格式YYYYMMDD
            overwrite: 是否覆盖已存在的文件
            max_retries: 最大重试次数
            
        Returns:
            每个指数代码的收集结果
        """
        logger.info(f"日期范围: {start_date} 到 {end_date}")
        
        return self._batch_collect_data(
            collect_func=self.collect_index_daily,
            func_name="指数行情数据",
            stock_codes_path=stock_codes_path,
            max_retries=max_retries,
            start_date=start_date,
            end_date=end_date,
            overwrite=overwrite
        )





# 使用示例
if __name__ == "__main__":
    import argparse
    import sys

    parser = argparse.ArgumentParser(description='股票数据收集器')
    
    # 必需参数
    parser.add_argument('--stage', type=str, required=True, 
                       choices=['download_market', 'download_cyq_perf', 'download_moneyflow', 'download_industry', 'download_index', 'import', 'daily_update'], 
                       help='功能阶段: download_market(下载行情数据), download_cyq_perf(下载筹码数据), download_moneyflow(下载资金流向数据), download_industry(下载申万行业成分数据), download_index(下载指数行情数据), import(导入数据) 或 daily_update(每日更新)')
    
    # 基础参数
    parser.add_argument('--stock_codes_path', type=str, required=True, 
                       help='股票代码列表文件路径')
    parser.add_argument('--data_dir', type=str, required=True, 
                       help='数据保存目录')
    parser.add_argument('--start_date', type=str, 
                       help='开始日期，格式YYYYMMDD')
    parser.add_argument('--end_date', type=str, 
                       help='结束日期，格式YYYYMMDD')

    
    # 下载阶段参数
    parser.add_argument('--overwrite_csv', action='store_true', 
                       help='是否覆盖已存在的CSV文件')
    parser.add_argument('--max_retries', type=int, default=3, 
                       help='最大重试次数，默认3次')
    
    # 导入阶段参数
    parser.add_argument('--import_type', type=str, 
                       choices=['market', 'cyq_perf', 'moneyflow', 'industry', 'index'], 
                       help='导入类型: market(行情数据), cyq_perf(筹码数据), moneyflow(资金流向数据), industry(申万行业成分数据), index(指数行情数据)')
    parser.add_argument('--clickhouse_host', type=str, default='localhost', 
                       help='ClickHouse数据库地址，默认localhost')
    parser.add_argument('--clickhouse_port', type=int, default=8123, 
                       help='ClickHouse数据库端口，默认8123')
    parser.add_argument('--clickhouse_user', type=str, default='default', 
                       help='ClickHouse数据库用户名，默认default')
    parser.add_argument('--clickhouse_password', type=str, default='', 
                       help='ClickHouse数据库密码，默认为空')
    parser.add_argument('--clickhouse_database', type=str, default='default', 
                       help='ClickHouse数据库名称，默认default')
    parser.add_argument('--table_name', type=str, default='daily_quotes', 
                       help='ClickHouse表名，默认daily_quotes(行情数据), daily_money_flow(资金流向数据), daily_chips(筹码数据), stock_industry(申万行业成分数据), daily_index(指数行情数据)')
    parser.add_argument('--batch_size', type=int, default=500, 
                       help='批量插入大小，默认500')
    parser.add_argument('--trade_calendar_path', type=str, default=None, 
                       help='交易日历文件路径，默认None')

    args = parser.parse_args()

    # 打印所有参数
    logger.info(args)

    # 验证参数
    if args.stage == 'import' and not args.import_type:
        logger.error("错误: 当stage=import时，必须指定import_type参数")
        sys.exit(1)


    # 从环境变量中获取Tushare Token
    load_dotenv(".env_sh")  # 加载.env_sh文件中的环境变量
    TOKEN = os.getenv("TUSHARE_TOKEN")

    # 创建收集器实例
    if args.stage in ['import', 'daily_update']:
        # 导入阶段和每日更新阶段需要数据库配置
        db_config = {
            "host": args.clickhouse_host,
            "port": args.clickhouse_port,
            "user": args.clickhouse_user,
            "password": args.clickhouse_password,
            "database": args.clickhouse_database
        }
        collector = StockDataCollector(token=TOKEN, data_dir=args.data_dir, db_config=db_config)
    else:
        # 下载阶段不需要数据库配置
        collector = StockDataCollector(token=TOKEN, data_dir=args.data_dir)

    logger.info("=" * 80)
    logger.info(f"股票数据收集器 - 阶段: {args.stage}")
    logger.info("=" * 80)

    try:
        if args.stage == 'download_market':
            if not args.start_date or not args.end_date:
                logger.error("错误: 当stage=download_market时，必须指定start_date和end_date参数")
                sys.exit(1)
            
            # 下载行情数据阶段
            logger.info("开始下载行情数据...")
            
            # 1. 下载未复权行情数据
            logger.info("\n步骤1: 下载未复权行情数据...")
            quotes_results = collector.batch_collect_stock_data(
                stock_codes_path=args.stock_codes_path,
                start_date=args.start_date,
                end_date=args.end_date,
                adj=None,  # 不复权
                overwrite=args.overwrite_csv,
                max_retries=args.max_retries
            )
            
            # 2. 下载复权因子数据
            logger.info("\n步骤2: 下载复权因子数据...")
            adj_factor_results = collector.batch_collect_adj_factors(
                stock_codes_path=args.stock_codes_path,
                start_date=args.start_date,
                end_date=args.end_date,
                overwrite=args.overwrite_csv,
                max_retries=args.max_retries
            )
            
            # 统计结果
            total_stocks = len(collector.load_stock_codes_from_json(args.stock_codes_path))
            quotes_success = sum(1 for success in quotes_results.values() if success)
            adj_factor_success = sum(1 for success in adj_factor_results.values() if success)
            
            logger.info("\n" + "=" * 80)
            logger.info("行情数据下载阶段完成")
            logger.info("=" * 80)
            logger.info(f"总股票数: {total_stocks}")
            logger.info(f"行情数据下载成功: {quotes_success}")
            logger.info(f"复权因子下载成功: {adj_factor_success}")
            
            if quotes_success < total_stocks or adj_factor_success < total_stocks:
                logger.warning("\n⚠️  部分股票下载失败，请检查失败原因并重新运行")
                failed_quotes = [code for code, success in quotes_results.items() if not success]
                failed_adj = [code for code, success in adj_factor_results.items() if not success]
                if failed_quotes:
                    logger.warning(f"行情数据下载失败的股票: {failed_quotes[:10]}{'...' if len(failed_quotes) > 10 else ''}")
                if failed_adj:
                    logger.warning(f"复权因子下载失败的股票: {failed_adj[:10]}{'...' if len(failed_adj) > 10 else ''}")
            else:
                logger.info("✅ 所有行情数据下载成功！")
                
        elif args.stage == 'download_cyq_perf':
            if not args.start_date or not args.end_date:
                logger.error("错误: 当stage=download_cyq_perf时，必须指定start_date和end_date参数")
                sys.exit(1)
            
            # 下载筹码数据阶段
            logger.info("开始下载筹码数据...")
            
            cyq_perf_results = collector.batch_collect_cyq_perf(
                stock_codes_path=args.stock_codes_path,
                start_date=args.start_date,
                end_date=args.end_date,
                overwrite=args.overwrite_csv,
                max_retries=args.max_retries
            )
            
            # 统计结果
            total_stocks = len(collector.load_stock_codes_from_json(args.stock_codes_path))
            cyq_perf_success = sum(1 for success in cyq_perf_results.values() if success)
            
            logger.info("\n" + "=" * 80)
            logger.info("筹码数据下载阶段完成")
            logger.info("=" * 80)
            logger.info(f"总股票数: {total_stocks}")
            logger.info(f"筹码数据下载成功: {cyq_perf_success}")
            
            if cyq_perf_success < total_stocks:
                logger.warning("\n⚠️  部分股票筹码数据下载失败，请检查失败原因并重新运行")
                failed_cyq_perf = [code for code, success in cyq_perf_results.items() if not success]
                if failed_cyq_perf:
                    logger.warning(f"筹码数据下载失败的股票: {failed_cyq_perf[:10]}{'...' if len(failed_cyq_perf) > 10 else ''}")
            else:
                logger.info("✅ 所有筹码数据下载成功！")
                
        elif args.stage == 'download_moneyflow':
            if not args.start_date or not args.end_date:
                logger.error("错误: 当stage=download_moneyflow时，必须指定start_date和end_date参数")
                sys.exit(1)
            
            # 下载资金流向数据阶段
            logger.info("开始下载资金流向数据...")
            
            moneyflow_results = collector.batch_collect_moneyflow(
                stock_codes_path=args.stock_codes_path,
                start_date=args.start_date,
                end_date=args.end_date,
                overwrite=args.overwrite_csv,
                max_retries=args.max_retries
            )
            
            # 统计结果
            total_stocks = len(collector.load_stock_codes_from_json(args.stock_codes_path))
            moneyflow_success = sum(1 for success in moneyflow_results.values() if success)
            
            logger.info("\n" + "=" * 80)
            logger.info("资金流向数据下载阶段完成")
            logger.info("=" * 80)
            logger.info(f"总股票数: {total_stocks}")
            logger.info(f"资金流向数据下载成功: {moneyflow_success}")
            
            if moneyflow_success < total_stocks:
                logger.warning("\n⚠️  部分股票资金流向数据下载失败，请检查失败原因并重新运行")
                failed_moneyflow = [code for code, success in moneyflow_results.items() if not success]
                if failed_moneyflow:
                    logger.warning(f"资金流向数据下载失败的股票: {failed_moneyflow[:10]}{'...' if len(failed_moneyflow) > 10 else ''}")
            else:
                logger.info("✅ 所有资金流向数据下载成功！")
                
        elif args.stage == 'download_industry':
            # 下载申万行业成分数据阶段
            logger.info("开始下载申万行业成分数据...")
            
            industry_results = collector.batch_collect_industry(
                stock_codes_path=args.stock_codes_path,
                overwrite=args.overwrite_csv,
                max_retries=args.max_retries
            )
            
            # 统计结果
            total_stocks = len(collector.load_stock_codes_from_json(args.stock_codes_path))
            industry_success = sum(1 for success in industry_results.values() if success)
            
            logger.info("\n" + "=" * 80)
            logger.info("申万行业成分数据下载阶段完成")
            logger.info("=" * 80)
            logger.info(f"总股票数: {total_stocks}")
            logger.info(f"申万行业成分数据下载成功: {industry_success}")
            
            if industry_success < total_stocks:
                logger.warning("\n⚠️  部分股票申万行业成分数据下载失败，请检查失败原因并重新运行")
                failed_industry = [code for code, success in industry_results.items() if not success]
                if failed_industry:
                    logger.warning(f"申万行业成分数据下载失败的股票: {failed_industry[:10]}{'...' if len(failed_industry) > 10 else ''}")
            else:
                logger.info("✅ 所有申万行业成分数据下载成功！")
                
        elif args.stage == 'download_index':
            if not args.start_date or not args.end_date:
                logger.error("错误: 当stage=download_index时，必须指定start_date和end_date参数")
                sys.exit(1)
            
            # 下载指数行情数据阶段
            logger.info("开始下载指数行情数据...")
            
            index_results = collector.batch_collect_index_daily(
                stock_codes_path=args.stock_codes_path,
                start_date=args.start_date,
                end_date=args.end_date,
                overwrite=args.overwrite_csv,
                max_retries=args.max_retries
            )
            
            # 统计结果
            total_codes = len(collector.load_stock_codes_from_json(args.stock_codes_path))
            index_success = sum(1 for success in index_results.values() if success)
            
            logger.info("\n" + "=" * 80)
            logger.info("指数行情数据下载阶段完成")
            logger.info("=" * 80)
            logger.info(f"总指数数: {total_codes}")
            logger.info(f"指数行情数据下载成功: {index_success}")
            
            if index_success < total_codes:
                logger.warning("\n⚠️  部分指数行情数据下载失败，请检查失败原因并重新运行")
                failed_index = [code for code, success in index_results.items() if not success]
                if failed_index:
                    logger.warning(f"指数行情数据下载失败的代码: {failed_index[:10]}{'...' if len(failed_index) > 10 else ''}")
            else:
                logger.info("✅ 所有指数行情数据下载成功！")
                
        elif args.stage == 'import' and args.import_type == 'market':
            # 导入行情数据阶段
            logger.info("开始导入行情数据到ClickHouse...")
            
            import_results = collector.process_and_import_market_data_to_clickhouse(
                stock_codes_path=args.stock_codes_path,
                table_name=args.table_name,
                batch_size=args.batch_size,
                trade_calendar_path=args.trade_calendar_path
            )
            
            # 统计结果
            total_stocks = len(collector.load_stock_codes_from_json(args.stock_codes_path))
            success_count = sum(1 for count in import_results.values() if count > 0)
            total_imported = sum(import_results.values())
            
            logger.info("\n" + "=" * 80)
            logger.info("导入阶段完成")
            logger.info("=" * 80)
            logger.info(f"总股票数: {total_stocks}")
            logger.info(f"成功导入股票数: {success_count}")
            logger.info(f"总导入记录数: {total_imported}")
            
            if success_count < total_stocks:
                failed_imports = [code for code, count in import_results.items() if count == 0]
                logger.warning(f"\n⚠️  部分股票导入失败: {failed_imports[:10]}{'...' if len(failed_imports) > 10 else ''}")
            else:
                logger.info("✅ 所有数据导入成功！")
                
        elif args.stage == 'import' and args.import_type == 'cyq_perf':
            # 导入筹码数据阶段
            logger.info("开始导入筹码数据到ClickHouse...")
            
            import_results = collector.process_and_import_cyq_to_clickhouse(
                stock_codes_path=args.stock_codes_path,
                table_name=args.table_name,
                batch_size=args.batch_size
            )
            
            # 统计结果
            total_stocks = len(collector.load_stock_codes_from_json(args.stock_codes_path))
            success_count = sum(1 for count in import_results.values() if count > 0)
            total_imported = sum(import_results.values())
            
            logger.info("\n" + "=" * 80)
            logger.info("导入阶段完成")
            logger.info("=" * 80)
            logger.info(f"总股票数: {total_stocks}")
            logger.info(f"成功导入股票数: {success_count}")
            logger.info(f"总导入记录数: {total_imported}")
            
            if success_count < total_stocks:
                failed_imports = [code for code, count in import_results.items() if count == 0]
                logger.warning(f"\n⚠️  部分股票导入失败: {failed_imports[:10]}{'...' if len(failed_imports) > 10 else ''}")
            else:
                logger.info("✅ 所有数据导入成功！")
                
        elif args.stage == 'import' and args.import_type == 'moneyflow':
            # 导入资金流向数据阶段
            logger.info("开始导入资金流向数据到ClickHouse...")
            
            import_results = collector.process_and_import_moneyflow_to_clickhouse(
                stock_codes_path=args.stock_codes_path,
                table_name=args.table_name,
                batch_size=args.batch_size
            )
            
            # 统计结果
            total_stocks = len(collector.load_stock_codes_from_json(args.stock_codes_path))
            success_count = sum(1 for count in import_results.values() if count > 0)
            total_imported = sum(import_results.values())
            
            logger.info("\n" + "=" * 80)
            logger.info("资金流向数据导入阶段完成")
            logger.info("=" * 80)
            logger.info(f"总股票数: {total_stocks}")
            logger.info(f"成功导入股票数: {success_count}")
            logger.info(f"总导入记录数: {total_imported}")
            
            if success_count < total_stocks:
                failed_imports = [code for code, count in import_results.items() if count == 0]
                logger.warning(f"\n⚠️  部分股票导入失败: {failed_imports[:10]}{'...' if len(failed_imports) > 10 else ''}")
            else:
                logger.info("✅ 所有资金流向数据导入成功！")
                
        elif args.stage == 'import' and args.import_type == 'industry':
            # 导入申万行业成分数据阶段
            logger.info("开始导入申万行业成分数据到ClickHouse...")
            
            import_results = collector.process_and_import_industry_to_clickhouse(
                stock_codes_path=args.stock_codes_path,
                table_name=args.table_name,
                batch_size=args.batch_size
            )
            
            # 统计结果
            total_stocks = len(collector.load_stock_codes_from_json(args.stock_codes_path))
            success_count = sum(1 for count in import_results.values() if count > 0)
            total_imported = sum(import_results.values())
            
            logger.info("\n" + "=" * 80)
            logger.info("申万行业成分数据导入阶段完成")
            logger.info("=" * 80)
            logger.info(f"总股票数: {total_stocks}")
            logger.info(f"成功导入股票数: {success_count}")
            logger.info(f"总导入记录数: {total_imported}")
            
            if success_count < total_stocks:
                failed_imports = [code for code, count in import_results.items() if count == 0]
                logger.warning(f"\n⚠️  部分股票导入失败: {failed_imports[:10]}{'...' if len(failed_imports) > 10 else ''}")
            else:
                logger.info("✅ 所有申万行业成分数据导入成功！")
        
        elif args.stage == 'import' and args.import_type == 'index':
            # 导入指数行情数据阶段
            logger.info("开始导入指数行情数据到ClickHouse...")
            
            import_results = collector.process_and_import_market_data_to_clickhouse(
                stock_codes_path=args.stock_codes_path,
                table_name=args.table_name,
                batch_size=args.batch_size,
                trade_calendar_path=args.trade_calendar_path,
                is_index=True  # 标记为指数数据
            )
            
            # 统计结果
            total_codes = len(collector.load_stock_codes_from_json(args.stock_codes_path))
            success_count = sum(1 for count in import_results.values() if count > 0)
            total_imported = sum(import_results.values())
            
            logger.info("\n" + "=" * 80)
            logger.info("指数行情数据导入阶段完成")
            logger.info("=" * 80)
            logger.info(f"总指数数: {total_codes}")
            logger.info(f"成功导入指数数: {success_count}")
            logger.info(f"总导入记录数: {total_imported}")
            
            if success_count < total_codes:
                failed_imports = [code for code, count in import_results.items() if count == 0]
                logger.warning(f"\n⚠️  部分指数导入失败: {failed_imports[:10]}{'...' if len(failed_imports) > 10 else ''}")
            else:
                logger.info("✅ 所有指数行情数据导入成功！")
        
        elif args.stage == 'daily_update':
            # 每日更新阶段
            logger.info("开始每日数据更新...")
            
            # 创建每日更新管理器
            daily_manager = DailyUpdateManager(collector)
            
            # 执行每日行情数据更新
            update_result = daily_manager.daily_update_market_data()
            
            # 输出结果
            if "error" in update_result:
                logger.error(f"❌ 每日更新失败: {update_result['error']}")
                sys.exit(1)
            else:
                logger.info("\n" + "=" * 80)
                logger.info("每日更新完成")
                logger.info("=" * 80)
                
                if update_result.get('message') == '无需更新':
                    logger.info("✅ 数据已是最新，无需更新")
                else:
                    missing_dates = update_result.get('missing_dates', [])
                    imported_count = update_result.get('imported_count', 0)
                    processed_stocks = update_result.get('processed_stocks', 0)
                    exclude_date = update_result.get('exclude_date')
                    
                    logger.info(f"更新的交易日: {missing_dates}")
                    logger.info(f"成功导入记录数: {imported_count}")
                    logger.info(f"涉及股票数: {processed_stocks}")
                    if exclude_date:
                        logger.info(f"用于计算但未入库的日期: {exclude_date}")
                    
                    if imported_count > 0:
                        logger.info("✅ 每日数据更新成功！")
                    else:
                        logger.warning("⚠️  没有数据被导入，请检查数据获取情况")
        
        else:
            logger.error(f"错误: 不支持的阶段和类型组合 - stage: {args.stage}, import_type: {args.import_type}")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"❌ 执行过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    
