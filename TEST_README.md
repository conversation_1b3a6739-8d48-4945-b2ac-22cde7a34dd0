# StockDataManager 测试脚本使用说明

本目录包含了用于测试 `StockDataManager` 各个功能的测试脚本。

## 测试脚本说明

### 1. `test_stock_data_manager.py` - 完整测试脚本

这是一个全面的测试脚本，包含了所有 `StockDataManager` 方法的测试。

**功能特点：**
- 🔍 测试所有获取函数
- 📊 支持分页和非分页测试
- 🚨 包含异常情况测试
- 📝 详细的测试结果输出
- 🎯 自动生成测试数据

**测试的方法：**
- `get_daily_quotes()` - 获取日线行情数据
- `get_daily_chips()` - 获取日线筹码数据
- `get_daily_money_flow()` - 获取日线资金流数据
- `get_stock_industry()` - 获取股票行业分类数据
- `get_stock_quote()` - 获取单只股票行情数据
- `get_latest_quotes()` - 获取最新行情数据

### 2. `quick_test.py` - 快速测试脚本

这是一个轻量级的快速测试脚本，用于验证核心功能。

**功能特点：**
- ⚡ 快速执行
- 🎯 测试核心功能
- 📋 简洁的输出格式

## 使用方法

### 运行完整测试

```bash
# 运行完整测试套件
python test_stock_data_manager.py
```

### 运行快速测试

```bash
# 快速验证核心功能
python quick_test.py
```

## 配置说明

测试脚本会自动设置以下 ClickHouse 连接配置：

```python
settings.clickhouse_host = '127.0.0.1'
settings.clickhouse_port = 8714
settings.clickhouse_user = 'root'
settings.clickhouse_password = 'Seue2vnILYi4F6'
settings.clickhouse_database = 'seekalpha'
```

如需修改配置，请编辑脚本中的 `setup_config()` 函数或 `__init__()` 方法。

## 测试数据

### 默认测试股票代码
- 000001.SZ (平安银行)
- 000002.SZ (万科A)
- 600000.SH (浦发银行)
- 600036.SH (招商银行)

### 测试日期范围
- 默认测试最近30天的数据
- 快速测试使用最近7天的数据

## 输出说明

### 成功输出示例
```
🚀 开始测试 get_daily_quotes
============================================================
测试: 查询指定股票行情数据（不分页）
============================================================
总记录数: 42
前3条数据:
  1. {"instrument_id": "000001.SZ", "trade_date": "2024-01-15", "close": 12.34, ...}
  2. {"instrument_id": "000001.SZ", "trade_date": "2024-01-16", "close": 12.45, ...}
  3. {"instrument_id": "000002.SZ", "trade_date": "2024-01-15", "close": 8.76, ...}
```

### 错误输出示例
```
❌ 测试失败: 连接数据库失败
Traceback (most recent call last):
  ...
```

## 故障排除

### 常见问题

1. **连接数据库失败**
   - 检查 ClickHouse 服务是否运行
   - 验证网络连接和防火墙设置
   - 确认用户名和密码正确

2. **导入模块失败**
   - 确保在项目根目录下运行脚本
   - 检查 Python 路径设置

3. **无数据返回**
   - 检查数据库中是否有对应的股票数据
   - 验证日期范围是否合理
   - 确认股票代码格式正确

### 调试建议

1. **增加日志级别**
   ```python
   logging.basicConfig(level=logging.DEBUG)
   ```

2. **单独测试某个方法**
   ```python
   # 在脚本中注释掉其他测试方法
   tester.test_get_daily_quotes()  # 只测试这一个方法
   ```

3. **检查数据库连接**
   ```python
   # 添加连接测试
   try:
       result = manager.clickhouse_client.execute("SELECT 1")
       print(f"数据库连接正常: {result}")
   except Exception as e:
       print(f"数据库连接失败: {e}")
   ```

## 扩展测试

如需添加新的测试用例，可以：

1. **添加新的测试方法**
   ```python
   def test_custom_scenario(self):
       """自定义测试场景"""
       # 你的测试代码
   ```

2. **修改测试数据**
   ```python
   # 使用不同的股票代码或日期范围
   self.test_stocks = ['your_stocks_here']
   ```

3. **调整输出格式**
   ```python
   # 修改 print_result 方法来自定义输出格式
   ``` 