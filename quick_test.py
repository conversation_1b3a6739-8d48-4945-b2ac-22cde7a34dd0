#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本 - 简单测试StockDataManager的核心功能
"""

import os
import sys
import json
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.src.manager.stock_data_manager import StockDataManager
from app.server.settings import settings

def setup_config():
    """设置配置"""
    settings.clickhouse_host = '127.0.0.1'
    settings.clickhouse_port = 8714
    settings.clickhouse_user = 'root'
    settings.clickhouse_password = 'Seue2vnILYi4F6'
    settings.clickhouse_database = 'seekalpha'

def test_basic_functionality():
    """测试基本功能"""
    print("🚀 开始快速测试...")
    
    # 设置配置
    setup_config()
    
    # 初始化数据管理器
    manager = StockDataManager.get_instance()
    
    # 测试数据
    test_stocks = ['000001.SZ', '600000.SH']
    end_date = datetime.now()
    start_date = end_date - timedelta(days=7)
    start_date_str = start_date.strftime('%Y-%m-%d')
    end_date_str = end_date.strftime('%Y-%m-%d')
    
    print(f"测试股票: {test_stocks}")
    print(f"测试日期范围: {start_date_str} 到 {end_date_str}")
    
    # 测试1: 获取日线行情数据
    print("\n1️⃣ 测试获取日线行情数据...")
    try:
        quotes = manager.get_daily_quotes(
            stock_list=test_stocks,
            start_date=start_date_str,
            end_date=end_date_str,
            page=1,
            page_size=5
        )
        print(f"✅ 成功获取行情数据，总记录数: {quotes.get('total', len(quotes))}")
        if quotes.get('data'):
            print(f"   第一条数据: {json.dumps(quotes['data'][0], ensure_ascii=False, default=str)}")
    except Exception as e:
        print(f"❌ 获取行情数据失败: {e}")
    
    # 测试2: 获取最新行情
    print("\n2️⃣ 测试获取最新行情...")
    try:
        latest = manager.get_latest_quotes(test_stocks)
        print(f"✅ 成功获取最新行情，记录数: {len(latest)}")
        if latest:
            print(f"   第一条数据: {json.dumps(latest[0], ensure_ascii=False, default=str)}")
    except Exception as e:
        print(f"❌ 获取最新行情失败: {e}")
    
    # 测试3: 获取行业分类
    print("\n3️⃣ 测试获取行业分类...")
    try:
        industry = manager.get_stock_industry(
            stock_list=test_stocks,
            is_latest=True
        )
        print(f"✅ 成功获取行业分类，记录数: {len(industry)}")
        if industry:
            print(f"   第一条数据: {json.dumps(industry[0], ensure_ascii=False, default=str)}")
    except Exception as e:
        print(f"❌ 获取行业分类失败: {e}")
    
    # 测试4: 获取单只股票行情
    print("\n4️⃣ 测试获取单只股票行情...")
    try:
        single_quote = manager.get_stock_quote(
            stock_code=test_stocks[0],
            page=1,
            page_size=3
        )
        print(f"✅ 成功获取单只股票行情，总记录数: {single_quote.get('total', len(single_quote))}")
        if single_quote.get('data'):
            print(f"   第一条数据: {json.dumps(single_quote['data'][0], ensure_ascii=False, default=str)}")
    except Exception as e:
        print(f"❌ 获取单只股票行情失败: {e}")
    
    print("\n🎉 快速测试完成！")

if __name__ == "__main__":
    test_basic_functionality() 