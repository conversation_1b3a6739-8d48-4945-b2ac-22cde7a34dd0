"""
重构后的股票数据收集器主入口文件
使用模块化架构，支持多种数据类型的收集和导入
"""

import sys
import os
import argparse
import json
from typing import Dict, Any
from pathlib import Path


from dotenv import load_dotenv
from stock_collector.config.collector_factory import CollectorFactory
from app.server.core.log import logger


def create_config(args) -> Dict[str, Any]:
    """
    创建配置字典
    
    Args:
        args: 命令行参数
        
    Returns:
        配置字典
    """
    # 从环境变量中获取Tushare Token
    load_dotenv(".env_sh")
    token = os.getenv("TUSHARE_TOKEN")
    
    if not token:
        raise ValueError("未找到TUSHARE_TOKEN环境变量")
    
    config = {
        'token': token,
        'data_dir': args.data_dir
    }
    
    # 如果是分钟线相关阶段，添加频率配置
    if args.stage in ['download_minutes'] or (args.stage == 'import' and args.import_type == 'minutes'):
        config['frequency'] = args.frequency
    
    # 如果是导入阶段，添加数据库配置
    if args.stage in ['import', 'daily_update']:
        config['db_config'] = {
            "host": args.clickhouse_host,
            "port": args.clickhouse_port,
            "user": args.clickhouse_user,
            "password": args.clickhouse_password,
            "database": args.clickhouse_database
        }
    
    return config


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='重构后的股票数据收集器')
    
    # 必需参数
    parser.add_argument('--stage', type=str, required=True, 
                       choices=['download_market', 'download_cyq_perf', 'download_moneyflow', 'download_industry', 'download_index', 'download_minutes', 'download_sector', 'import', 'daily_update'], 
                       help='功能阶段')
    
    # 基础参数
    parser.add_argument('--data_dir', type=str, required=True, 
                       help='数据保存目录')
    parser.add_argument('--stock_codes_path', type=str, 
                       help='股票代码列表文件路径')
    parser.add_argument('--start_date', type=str, 
                       help='开始日期，格式YYYYMMDD')
    parser.add_argument('--end_date', type=str, 
                       help='结束日期，格式YYYYMMDD')
    
    # 下载阶段参数
    parser.add_argument('--overwrite_csv', action='store_true', 
                       help='是否覆盖已存在的CSV文件')
    parser.add_argument('--max_retries', type=int, default=3, 
                       help='最大重试次数，默认3次')
    parser.add_argument('--frequency', type=str, default='5',
                       help='分钟线频率，支持5、15、30、60分钟，默认5分钟')
    
    # 导入阶段参数
    parser.add_argument('--import_type', type=str, 
                       choices=['market', 'chip', 'moneyflow', 'industry', 'index', 'minutes', 'sector'], 
                       help='导入类型')
    parser.add_argument('--clickhouse_host', type=str, default='localhost', 
                       help='ClickHouse数据库地址，默认localhost')
    parser.add_argument('--clickhouse_port', type=int, default=8123, 
                       help='ClickHouse数据库端口，默认8123')
    parser.add_argument('--clickhouse_user', type=str, default='default', 
                       help='ClickHouse数据库用户名，默认default')
    parser.add_argument('--clickhouse_password', type=str, default='', 
                       help='ClickHouse数据库密码，默认为空')
    parser.add_argument('--clickhouse_database', type=str, default='default', 
                       help='ClickHouse数据库名称，默认default')
    parser.add_argument('--table_name', type=str, default='daily_quotes', 
                       help='ClickHouse表名')
    parser.add_argument('--batch_size', type=int, default=500, 
                       help='批量插入大小，默认500')
    parser.add_argument('--trade_calendar_path', type=str, default=None, 
                       help='交易日历文件路径')

    args = parser.parse_args()

    # 验证参数
    if args.stage == 'import' and not args.import_type:
        logger.error("错误: 当stage=import时，必须指定import_type参数")
        sys.exit(1)

    # 打印参数
    logger.info(args)

    logger.info("=" * 80)
    logger.info(f"重构后的股票数据收集器 - 阶段: {args.stage}")
    logger.info("=" * 80)

    try:
        # 创建配置
        config = create_config(args)
        
        if args.stage == 'download_market':
            # 下载市场数据
            if not args.start_date or not args.end_date:
                logger.error("错误: 当stage=download_market时，必须指定start_date和end_date参数")
                sys.exit(1)
            
            logger.info("开始下载市场数据...")
            
            # 创建市场数据收集器
            market_collector = CollectorFactory.create_collector('market', config)
            
            # 1. 下载未复权行情数据
            logger.info("\n步骤1: 下载未复权行情数据...")
            quotes_results = market_collector.batch_collect_stock_data(
                stock_codes_path=args.stock_codes_path,
                start_date=args.start_date,
                end_date=args.end_date,
                adj=None,  # 不复权
                overwrite=args.overwrite_csv,
                max_retries=args.max_retries
            )
            
            # 2. 下载复权因子数据
            logger.info("\n步骤2: 下载复权因子数据...")
            adj_factor_results = market_collector.batch_collect_adj_factors(
                stock_codes_path=args.stock_codes_path,
                start_date=args.start_date,
                end_date=args.end_date,
                overwrite=args.overwrite_csv,
                max_retries=args.max_retries
            )
            
            # 统计结果
            total_stocks = len(market_collector.load_stock_codes_from_json(args.stock_codes_path))
            quotes_success = sum(1 for success in quotes_results.values() if success)
            adj_factor_success = sum(1 for success in adj_factor_results.values() if success)
            
            logger.info("\n" + "=" * 80)
            logger.info("市场数据下载阶段完成")
            logger.info("=" * 80)
            logger.info(f"总股票数: {total_stocks}")
            logger.info(f"行情数据下载成功: {quotes_success}")
            logger.info(f"复权因子下载成功: {adj_factor_success}")
            
            if quotes_success < total_stocks or adj_factor_success < total_stocks:
                logger.warning("\n⚠️  部分股票下载失败，请检查失败原因并重新运行")
            else:
                logger.info("✅ 所有市场数据下载成功！")
                
        elif args.stage == 'download_cyq_perf':
            # 下载筹码数据
            if not args.start_date or not args.end_date:
                logger.error("错误: 当stage=download_cyq_perf时，必须指定start_date和end_date参数")
                sys.exit(1)
            
            logger.info("开始下载筹码数据...")
            
            chip_collector = CollectorFactory.create_collector('chip', config)
            cyq_perf_results = chip_collector.batch_collect_cyq_perf(
                stock_codes_path=args.stock_codes_path,
                start_date=args.start_date,
                end_date=args.end_date,
                overwrite=args.overwrite_csv,
                max_retries=args.max_retries
            )
            
            # 统计结果
            total_stocks = len(chip_collector.load_stock_codes_from_json(args.stock_codes_path))
            cyq_perf_success = sum(1 for success in cyq_perf_results.values() if success)
            
            logger.info("\n" + "=" * 80)
            logger.info("筹码数据下载阶段完成")
            logger.info("=" * 80)
            logger.info(f"总股票数: {total_stocks}")
            logger.info(f"筹码数据下载成功: {cyq_perf_success}")
            
            if cyq_perf_success < total_stocks:
                logger.warning("\n⚠️  部分股票筹码数据下载失败")
            else:
                logger.info("✅ 所有筹码数据下载成功！")
                
        elif args.stage == 'download_moneyflow':
            # 下载资金流向数据
            if not args.start_date or not args.end_date:
                logger.error("错误: 当stage=download_moneyflow时，必须指定start_date和end_date参数")
                sys.exit(1)
            
            logger.info("开始下载资金流向数据...")
            
            moneyflow_collector = CollectorFactory.create_collector('moneyflow', config)
            moneyflow_results = moneyflow_collector.batch_collect_moneyflow(
                stock_codes_path=args.stock_codes_path,
                start_date=args.start_date,
                end_date=args.end_date,
                overwrite=args.overwrite_csv,
                max_retries=args.max_retries
            )
            
            # 统计结果
            total_stocks = len(moneyflow_collector.load_stock_codes_from_json(args.stock_codes_path))
            moneyflow_success = sum(1 for success in moneyflow_results.values() if success)
            
            logger.info("\n" + "=" * 80)
            logger.info("资金流向数据下载阶段完成")
            logger.info("=" * 80)
            logger.info(f"总股票数: {total_stocks}")
            logger.info(f"资金流向数据下载成功: {moneyflow_success}")
            
            if moneyflow_success < total_stocks:
                logger.warning("\n⚠️  部分股票资金流向数据下载失败")
            else:
                logger.info("✅ 所有资金流向数据下载成功！")
                
        elif args.stage == 'download_industry':
            # 下载行业数据
            logger.info("开始下载申万行业成分数据...")
            
            industry_collector = CollectorFactory.create_collector('industry', config)
            industry_results = industry_collector.batch_collect_industry(
                stock_codes_path=args.stock_codes_path,
                overwrite=args.overwrite_csv,
                max_retries=args.max_retries
            )
            
            # 统计结果
            total_stocks = len(industry_collector.load_stock_codes_from_json(args.stock_codes_path))
            industry_success = sum(1 for success in industry_results.values() if success)
            
            logger.info("\n" + "=" * 80)
            logger.info("申万行业成分数据下载阶段完成")
            logger.info("=" * 80)
            logger.info(f"总股票数: {total_stocks}")
            logger.info(f"申万行业成分数据下载成功: {industry_success}")
            
            if industry_success < total_stocks:
                logger.warning("\n⚠️  部分股票申万行业成分数据下载失败")
            else:
                logger.info("✅ 所有申万行业成分数据下载成功！")
                
        elif args.stage == 'download_index':
            # 下载指数数据
            if not args.start_date or not args.end_date:
                logger.error("错误: 当stage=download_index时，必须指定start_date和end_date参数")
                sys.exit(1)
            
            logger.info("开始下载指数行情数据...")
            
            index_collector = CollectorFactory.create_collector('index', config)
            index_results = index_collector.batch_collect_stock_data(
                stock_codes_path=args.stock_codes_path,
                start_date=args.start_date,
                end_date=args.end_date,
                overwrite=args.overwrite_csv,
                max_retries=args.max_retries
            )
            
            # 统计结果
            total_codes = len(index_collector.load_stock_codes_from_json(args.stock_codes_path))
            index_success = sum(1 for success in index_results.values() if success)
            
            logger.info("\n" + "=" * 80)
            logger.info("指数行情数据下载阶段完成")
            logger.info("=" * 80)
            logger.info(f"总指数数: {total_codes}")
            logger.info(f"指数行情数据下载成功: {index_success}")
            
            if index_success < total_codes:
                logger.warning("\n⚠️  部分指数行情数据下载失败")
            else:
                logger.info("✅ 所有指数行情数据下载成功！")
                
        elif args.stage == 'download_minutes':
            # 下载分钟线数据
            if not args.start_date or not args.end_date:
                logger.error("错误: 当stage=download_minutes时，必须指定start_date和end_date参数")
                sys.exit(1)
            
            logger.info(f"开始下载{args.frequency}分钟线数据...")
            
            minutes_collector = CollectorFactory.create_collector('minutes', config)
            minutes_results = minutes_collector.batch_collect_minutes_data(
                stock_codes_path=args.stock_codes_path,
                start_date=args.start_date,
                end_date=args.end_date,
                overwrite=args.overwrite_csv,
                max_retries=args.max_retries,
                frequency=args.frequency
            )
            
            # 统计结果
            total_stocks = len(minutes_collector.load_stock_codes_from_json(args.stock_codes_path))
            minutes_success = sum(1 for success in minutes_results.values() if success)
            
            logger.info("\n" + "=" * 80)
            logger.info(f"{args.frequency}分钟线数据下载阶段完成")
            logger.info("=" * 80)
            logger.info(f"总股票数: {total_stocks}")
            logger.info(f"{args.frequency}分钟线数据下载成功: {minutes_success}")
            
            if minutes_success < total_stocks:
                logger.warning(f"\n⚠️  部分股票{args.frequency}分钟线数据下载失败")
            else:
                logger.info(f"✅ 所有{args.frequency}分钟线数据下载成功！")
                
        elif args.stage == 'download_sector':
            # 下载板块数据
            if not args.start_date or not args.end_date:
                logger.error("错误: 当stage=download_sector时，必须指定start_date和end_date参数")
                sys.exit(1)
            
            logger.info("开始下载板块数据...")
            
            sector_collector = CollectorFactory.create_collector('sector', config)


            sector_codes_path = os.path.join(str(Path(__file__).parent), ".cache", "sector_codes.json")
            
            # 获取板块代码列表（如果没有指定文件，自动获取）
            sector_codes = sector_collector.get_or_create_sector_codes(sector_codes_path, trade_date='20250710')
            
            sector_results = sector_collector.batch_collect_sector_data(
                stock_codes_path=sector_codes_path,
                start_date=args.start_date,
                end_date=args.end_date,
                overwrite=args.overwrite_csv,
                max_retries=args.max_retries
            )
            
            # 统计结果
            total_sectors = len(sector_codes) if sector_codes else 0
            sector_success = sum(1 for success in sector_results.values() if success)
            
            logger.info("\n" + "=" * 80)
            logger.info("板块数据下载阶段完成")
            logger.info("=" * 80)
            logger.info(f"总板块数: {total_sectors}")
            logger.info(f"板块数据下载成功: {sector_success}")
            
            if sector_success < total_sectors:
                logger.warning("\n⚠️  部分板块数据下载失败")
            else:
                logger.info("✅ 所有板块数据下载成功！")
                
        elif args.stage == 'import':
            # 导入数据
            logger.info(f"开始导入{args.import_type}数据到ClickHouse...")
            
            # 创建对应的收集器
            collector = CollectorFactory.create_collector(args.import_type, config)
            
            # 根据不同的数据类型调用不同的导入方法
            if args.import_type == 'market':
                import_results = collector.process_and_import_to_clickhouse(
                    stock_codes_path=args.stock_codes_path,
                    table_name=args.table_name,
                    batch_size=args.batch_size,
                    trade_calendar_path=args.trade_calendar_path,
                    data_type='share'
                )
            elif args.import_type == 'chip':
                import_results = collector.process_and_import_to_clickhouse(
                    stock_codes_path=args.stock_codes_path,
                    table_name=args.table_name,
                    batch_size=args.batch_size
                )
            elif args.import_type == 'moneyflow':
                import_results = collector.process_and_import_to_clickhouse(
                    stock_codes_path=args.stock_codes_path,
                    table_name=args.table_name,
                    batch_size=args.batch_size
                )
            elif args.import_type == 'industry':
                import_results = collector.process_and_import_to_clickhouse(
                    stock_codes_path=args.stock_codes_path,
                    table_name=args.table_name,
                    batch_size=args.batch_size
                )
            elif args.import_type == 'index':
                import_results = collector.process_and_import_to_clickhouse(
                    stock_codes_path=args.stock_codes_path,
                    table_name=args.table_name,
                    batch_size=args.batch_size,
                    trade_calendar_path=args.trade_calendar_path,
                    data_type='index'
                )
            elif args.import_type == 'minutes':
                import_results = collector.process_and_import_to_clickhouse_multi_process(
                    stock_codes_path=args.stock_codes_path,
                    table_name=args.table_name,
                    batch_size=args.batch_size,
                    cache_dir=os.path.join(args.data_dir, 'minutes_cache'),
                    n_jobs=50
                )
            elif args.import_type == 'sector':
                import_results = collector.process_and_import_to_clickhouse(
                    stock_codes_path=args.stock_codes_path,
                    table_name=args.table_name,
                    batch_size=args.batch_size,
                    trade_calendar_path=args.trade_calendar_path,
                    data_type='sector'
                )
            
            # 统计结果
            total_count = len(collector.load_stock_codes_from_json(args.stock_codes_path))
            success_count = sum(1 for count in import_results.values() if count > 0)
            total_imported = sum(import_results.values())
            
            logger.info("\n" + "=" * 80)
            logger.info(f"{args.import_type}数据导入阶段完成")
            logger.info("=" * 80)
            logger.info(f"总数量: {total_count}")
            logger.info(f"成功导入数量: {success_count}")
            logger.info(f"总导入记录数: {total_imported}")
            
            if success_count < total_count:
                failed_imports = [code for code, count in import_results.items() if count == 0]
                logger.warning(f"\n⚠️  部分数据导入失败: {failed_imports[:10]}{'...' if len(failed_imports) > 10 else ''}")
            else:
                logger.info("✅ 所有数据导入成功！")
        
        else:
            logger.error(f"错误: 不支持的阶段 - {args.stage}")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"❌ 执行过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()