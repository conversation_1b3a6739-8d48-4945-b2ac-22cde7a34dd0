#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
StockDataManager 测试脚本
用于测试股票数据管理器的各个获取函数
"""

import os
import sys
import json
from datetime import datetime, timedelta
import traceback

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.src.manager.stock_data_manager import StockDataManager
from app.server.settings import settings
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
# 只对我们关心的模块开启DEBUG
logging.getLogger('app.src.manager.stock_data_manager').setLevel(logging.DEBUG)
logger = logging.getLogger(__name__)

class StockDataManagerTester:
    """StockDataManager 测试类"""
    
    def __init__(self):
        """初始化测试环境"""
        # 直接设置ClickHouse连接信息
        settings.clickhouse_host = '127.0.0.1'
        settings.clickhouse_port = 8714
        settings.clickhouse_user = 'root'
        settings.clickhouse_password = 'Seue2vnILYi4F6'
        settings.clickhouse_database = 'seekalpha'
        
        # 初始化数据管理器
        self.data_manager = StockDataManager.get_instance()
        
        # 测试用的股票代码列表
        self.test_stocks = ['000012.SZ', '688563.SH', '600004.SH', '688676.SH']
        
        # 测试日期范围
        # 2015-01-01 到2015-01-31
        start_date = datetime(2014, 1, 1)
        end_date = datetime(2024, 1, 31)
        self.start_date = start_date.strftime('%Y-%m-%d')
        self.end_date = end_date.strftime('%Y-%m-%d')
        self.test_date = (end_date - timedelta(days=15)).strftime('%Y-%m-%d')
        
        logger.info(f"测试初始化完成")
        logger.info(f"测试股票: {self.test_stocks}")
        logger.info(f"测试日期范围: {self.start_date} 到 {self.end_date}")

    def print_result(self, test_name: str, result, limit: int = 3):
        """打印测试结果"""
        print(f"\n{'='*60}")
        print(f"测试: {test_name}")
        print(f"{'='*60}")
        
        if isinstance(result, dict) and 'data' in result:
            # 统一的分页结果格式
            print(f"总记录数: {result.get('total', 0)}")
            print(f"当前页: {result.get('page', 0)}")
            print(f"每页大小: {result.get('page_size', 0)}")
            
            data = result['data']
            if data:
                print(f"\n前{min(limit, len(data))}条数据:")
                for i, item in enumerate(data[:limit]):
                    print(f"  {i+1}. {json.dumps(item, ensure_ascii=False, default=str)}")
            else:
                print("无数据")
        elif isinstance(result, list):
            # 列表结果（如get_latest_quotes）
            print(f"总记录数: {len(result)}")
            if result:
                print(f"\n前{min(limit, len(result))}条数据:")
                for i, item in enumerate(result[:limit]):
                    print(f"  {i+1}. {json.dumps(item, ensure_ascii=False, default=str)}")
            else:
                print("无数据")
        else:
            print(f"结果: {result}")

    def test_get_daily_quotes(self):
        """测试获取日线行情数据"""
        print(f"\n🚀 开始测试 get_daily_quotes")
        
        # 测试1: 查询所有字段（默认包含后复权价格）
        try:
            result = self.data_manager.get_daily_quotes(
                stock_list=self.test_stocks[:2],
                start_date=self.start_date,
                end_date=self.end_date
            )
            self.print_result("查询所有字段（默认包含后复权价格）", result)
            
            # 验证是否包含后复权价格字段
            if result['data']:
                first_record = result['data'][0]
                adj_fields = [k for k in first_record.keys() if k.startswith('adj_') and k != 'adj_factor']
                print(f"  📊 后复权价格字段: {adj_fields}")
                expected_adj_fields = ['adj_open', 'adj_high', 'adj_low', 'adj_close']
                if all(field in first_record for field in expected_adj_fields):
                    print(f"  ✅ 包含所有预期的后复权价格字段")
                else:
                    print(f"  ❌ 缺少后复权价格字段")
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            traceback.print_exc()

        # 测试2: 只查询后复权价格字段
        try:
            result = self.data_manager.get_daily_quotes(
                stock_list=self.test_stocks[:1],
                start_date=self.start_date,
                end_date=self.end_date,
                columns=['instrument_id', 'trade_date', 'adj_open', 'adj_close'],
                page=1,
                page_size=5
            )
            self.print_result("只查询后复权价格字段", result)
            
            if result['data']:
                first_record = result['data'][0]
                print(f"  📊 返回的字段: {list(first_record.keys())}")
                expected_fields = ['instrument_id', 'trade_date', 'adj_open', 'adj_close']
                actual_fields = list(first_record.keys())
                
                # 检查是否完全匹配用户指定的字段
                if set(actual_fields) == set(expected_fields):
                    print(f"  ✅ 完全匹配用户指定的字段，没有额外字段")
                else:
                    extra_fields = set(actual_fields) - set(expected_fields)
                    missing_fields = set(expected_fields) - set(actual_fields)
                    if extra_fields:
                        print(f"  ⚠️ 包含额外字段: {extra_fields}")
                    if missing_fields:
                        print(f"  ❌ 缺少字段: {missing_fields}")
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            traceback.print_exc()

        # 测试3: 混合查询基础字段和后复权字段
        try:
            result = self.data_manager.get_daily_quotes(
                stock_list=self.test_stocks[:1],
                start_date=self.start_date,
                end_date=self.end_date,
                columns=['instrument_id', 'trade_date', 'open', 'adj_open', 'volume', 'adj_close']
            )
            self.print_result("混合查询基础字段和后复权字段", result)
            
            if result['data']:
                first_record = result['data'][0]
                print(f"  📊 返回的字段: {list(first_record.keys())}")
                expected_fields = ['instrument_id', 'trade_date', 'open', 'adj_open', 'volume', 'adj_close']
                if all(field in first_record for field in expected_fields):
                    print(f"  ✅ 正确返回混合字段")
                else:
                    print(f"  ❌ 字段返回不完整")
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            traceback.print_exc()

        # 测试4: 只查询基础字段（不包含后复权价格）
        try:
            result = self.data_manager.get_daily_quotes(
                stock_list=self.test_stocks[:1],
                start_date=self.start_date,
                end_date=self.end_date,
                columns=['instrument_id', 'trade_date', 'open', 'close', 'volume'],
                page=1,
                page_size=5
            )
            self.print_result("只查询基础字段（不包含后复权价格）", result)
            
            if result['data']:
                first_record = result['data'][0]
                adj_fields = [k for k in first_record.keys() if k.startswith('adj_') and k != 'adj_factor']
                print(f"  📊 adj_开头的价格字段: {adj_fields}")
                print(f"  📊 返回的字段: {list(first_record.keys())}")
                if not adj_fields:
                    print(f"  ✅ 正确地不包含后复权价格字段")
                else:
                    print(f"  ❌ 意外地包含了后复权价格字段")
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            traceback.print_exc()

        # 测试5: 验证后复权价格计算的正确性
        try:
            result = self.data_manager.get_daily_quotes(
                stock_list=self.test_stocks[:1],
                start_date=self.start_date,
                end_date=self.end_date,
                columns=['instrument_id', 'trade_date', 'open', 'close', 'adj_open', 'adj_close'],
                page=1,
                page_size=3
            )
            
            print(f"\n📈 验证后复权价格计算正确性:")
            if result['data']:
                for i, record in enumerate(result['data'][:2]):
                    open_price = float(record['open'])
                    close_price = float(record['close'])
                    adj_open = float(record['adj_open'])
                    adj_close = float(record['adj_close'])
                    
                    # 计算复权因子
                    adj_factor_from_open = adj_open / open_price if open_price != 0 else 0
                    adj_factor_from_close = adj_close / close_price if close_price != 0 else 0
                    
                    print(f"  记录 {i+1}:")
                    print(f"    原始价格: 开盘={open_price}, 收盘={close_price}")
                    print(f"    后复权价格: 开盘={adj_open:.4f}, 收盘={adj_close:.4f}")
                    print(f"    计算的复权因子: 从开盘价={adj_factor_from_open:.6f}, 从收盘价={adj_factor_from_close:.6f}")
                    
                    # 检查复权因子是否一致
                    if abs(adj_factor_from_open - adj_factor_from_close) < 0.001:
                        print(f"    ✅ 后复权价格计算一致")
                    else:
                        print(f"    ❌ 后复权价格计算不一致")
            else:
                print(f"  ❌ 无数据可验证")
        except Exception as e:
            print(f"❌ 后复权价格验证失败: {e}")
            traceback.print_exc()

        # 测试6: 只查询所有后复权价格字段
        try:
            result = self.data_manager.get_daily_quotes(
                stock_list=self.test_stocks[:1],
                start_date=self.start_date,
                end_date=self.end_date,
                columns=['instrument_id', 'trade_date', 'adj_open', 'adj_high', 'adj_low', 'adj_close'],
                page=1,
                page_size=3
            )
            self.print_result("只查询所有后复权价格字段", result)
            
            if result['data']:
                first_record = result['data'][0]
                print(f"  📊 返回的字段: {list(first_record.keys())}")
                expected_adj_fields = ['adj_open', 'adj_high', 'adj_low', 'adj_close']
                if all(field in first_record for field in expected_adj_fields):
                    print(f"  ✅ 正确返回所有后复权价格字段")
                else:
                    print(f"  ❌ 后复权价格字段不完整")
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            traceback.print_exc()

    def test_get_daily_chips(self):
        """测试获取日线筹码数据"""
        print(f"\n🚀 开始测试 get_daily_chips")
        
        # 测试1: 查询所有字段（默认包含筹码集中度）
        try:
            result = self.data_manager.get_daily_chips(
                stock_list=self.test_stocks[:2],
                start_date=self.start_date,
                end_date=self.end_date,
                page=1,
                page_size=5
            )
            self.print_result("查询所有筹码字段（默认包含筹码集中度）", result)
            
            # 验证是否包含筹码集中度字段
            if result['data']:
                first_record = result['data'][0]
                if 'chip_conct' in first_record:
                    print(f"  ✅ 包含筹码集中度字段")
                    print(f"  📊 筹码集中度值: {first_record['chip_conct']}")
                else:
                    print(f"  ❌ 缺少筹码集中度字段")
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            traceback.print_exc()

        # 测试2: 只查询基础字段（不包含筹码集中度）
        try:
            result = self.data_manager.get_daily_chips(
                stock_list=self.test_stocks[:1],
                start_date=self.start_date,
                end_date=self.end_date,
                columns=['instrument_id', 'trade_date', 'cost_5pct', 'cost_50pct', 'cost_95pct'],
                page=1,
                page_size=3
            )
            self.print_result("只查询基础筹码字段", result)
            
            if result['data']:
                first_record = result['data'][0]
                print(f"  📊 返回的字段: {list(first_record.keys())}")
                expected_fields = ['instrument_id', 'trade_date', 'cost_5pct', 'cost_50pct', 'cost_95pct']
                if set(first_record.keys()) == set(expected_fields):
                    print(f"  ✅ 正确返回指定的基础字段，不包含筹码集中度")
                else:
                    print(f"  ❌ 字段返回不符合预期")
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            traceback.print_exc()

        # 测试3: 只查询筹码集中度字段
        try:
            result = self.data_manager.get_daily_chips(
                stock_list=self.test_stocks[:1],
                start_date=self.start_date,
                end_date=self.end_date,
                columns=['instrument_id', 'trade_date', 'chip_conct'],
                page=1,
                page_size=3
            )
            self.print_result("只查询筹码集中度字段", result)
            
            if result['data']:
                first_record = result['data'][0]
                print(f"  📊 返回的字段: {list(first_record.keys())}")
                expected_fields = ['instrument_id', 'trade_date', 'chip_conct']
                if set(first_record.keys()) == set(expected_fields):
                    print(f"  ✅ 正确返回筹码集中度字段")
                    print(f"  📊 筹码集中度值: {first_record['chip_conct']}")
                else:
                    print(f"  ❌ 字段返回不符合预期")
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            traceback.print_exc()

        # 测试4: 混合查询基础字段和筹码集中度
        try:
            result = self.data_manager.get_daily_chips(
                stock_list=self.test_stocks[:1],
                start_date=self.start_date,
                end_date=self.end_date,
                columns=['instrument_id', 'trade_date', 'cost_5pct', 'cost_95pct', 'chip_conct'],
                page=1,
                page_size=3
            )
            self.print_result("混合查询基础字段和筹码集中度", result)
            
            if result['data']:
                first_record = result['data'][0]
                expected_fields = ['instrument_id', 'trade_date', 'cost_5pct', 'cost_95pct', 'chip_conct']
                if all(field in first_record for field in expected_fields):
                    print(f"  ✅ 正确返回混合字段")
                else:
                    print(f"  ❌ 字段返回不完整")
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            traceback.print_exc()

    def test_get_daily_money_flow(self):
        """测试获取日线资金流数据"""
        print(f"\n🚀 开始测试 get_daily_money_flow")
        
        try:
            result = self.data_manager.get_daily_money_flow(
                stock_list=self.test_stocks[:2],
                start_date=self.start_date,
                end_date=self.end_date,
                page=1,
                page_size=5
            )
            self.print_result("查询资金流数据", result)
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            traceback.print_exc()

    def test_get_stock_industry(self):
        """测试获取股票行业分类数据"""
        print(f"\n🚀 开始测试 get_stock_industry")
        
        # 测试1: 获取最新行业分类（不分页）
        try:
            result = self.data_manager.get_stock_industry(
                stock_list=self.test_stocks
            )
            self.print_result("获取最新行业分类（不分页）", result)
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            traceback.print_exc()

        # 测试2: 分页查询行业分类
        try:
            result = self.data_manager.get_stock_industry(
                stock_list=self.test_stocks,
                page=1,
                page_size=2
            )
            self.print_result("分页查询行业分类", result)
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            traceback.print_exc()

        # 测试3: 查询指定列
        try:
            result = self.data_manager.get_stock_industry(
                stock_list=self.test_stocks[:2],
                columns=['instrument_id', 'name', 'l1_name', 'l2_name', 'l3_name']
            )
            self.print_result("查询指定列的行业分类", result)
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            traceback.print_exc()

        # 测试4: 查询所有股票的行业分类（分页）
        try:
            result = self.data_manager.get_stock_industry(
                page=1,
                page_size=10
            )
            self.print_result("查询所有股票行业分类（分页）", result)
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            traceback.print_exc()

    def test_get_stock_quote(self):
        """测试获取股票行情数据（单只股票）"""
        print(f"\n🚀 开始测试 get_stock_quote")
        
        # 测试1: 查询单只股票指定日期
        try:
            result = self.data_manager.get_stock_quote(
                stock_code=self.test_stocks[0],
                trade_date=self.test_date
            )
            self.print_result("查询单只股票指定日期", result)
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            traceback.print_exc()

        # 测试2: 查询单只股票所有日期（分页）
        try:
            result = self.data_manager.get_stock_quote(
                stock_code=self.test_stocks[0],
                page=1,
                page_size=10
            )
            self.print_result("查询单只股票所有日期（分页）", result)
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            traceback.print_exc()

    def test_get_latest_quotes(self):
        """测试获取最新行情"""
        print(f"\n🚀 开始测试 get_latest_quotes")
        
        try:
            result = self.data_manager.get_latest_quotes(self.test_stocks)
            self.print_result("获取最新行情", result)
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            traceback.print_exc()

    def test_adj_fields_only(self):
        """测试只查询后复权字段的行为"""
        print(f"\n🚀 开始测试只查询后复权字段")
        
        # 测试1: 只查询adj_open，不应该返回open和adj_factor
        try:
            result = self.data_manager.get_daily_quotes(
                stock_list=self.test_stocks[:1],
                start_date=self.start_date,
                end_date=self.end_date,
                columns=['instrument_id', 'trade_date', 'adj_open'],
                page=1,
                page_size=3
            )
            
            print(f"\n测试1: 只查询adj_open字段")
            if result['data']:
                first_record = result['data'][0]
                actual_fields = set(first_record.keys())
                expected_fields = {'instrument_id', 'trade_date', 'adj_open'}
                
                print(f"  📊 用户指定字段: {expected_fields}")
                print(f"  📊 实际返回字段: {actual_fields}")
                
                if actual_fields == expected_fields:
                    print(f"  ✅ 完美！只返回用户指定的字段，没有额外的open或adj_factor")
                else:
                    extra_fields = actual_fields - expected_fields
                    missing_fields = expected_fields - actual_fields
                    if extra_fields:
                        print(f"  ❌ 不应该有额外字段: {extra_fields}")
                    if missing_fields:
                        print(f"  ❌ 缺少字段: {missing_fields}")
            else:
                print(f"  ❌ 无数据返回")
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            traceback.print_exc()

        # 测试2: 查询多个后复权字段
        try:
            result = self.data_manager.get_daily_quotes(
                stock_list=self.test_stocks[:1],
                start_date=self.start_date,
                end_date=self.end_date,
                columns=['instrument_id', 'adj_open', 'adj_high', 'adj_close'],
                page=1,
                page_size=2
            )
            
            print(f"\n测试2: 查询多个后复权字段")
            if result['data']:
                first_record = result['data'][0]
                actual_fields = set(first_record.keys())
                expected_fields = {'instrument_id', 'adj_open', 'adj_high', 'adj_close'}
                
                print(f"  📊 用户指定字段: {expected_fields}")
                print(f"  📊 实际返回字段: {actual_fields}")
                
                if actual_fields == expected_fields:
                    print(f"  ✅ 完美！只返回用户指定的后复权字段")
                    
                    # 验证数据的合理性
                    adj_open = first_record['adj_open']
                    adj_high = first_record['adj_high']
                    adj_close = first_record['adj_close']
                    print(f"  📈 后复权价格: 开盘={adj_open}, 最高={adj_high}, 收盘={adj_close}")
                    
                    if adj_open > 0 and adj_high > 0 and adj_close > 0:
                        print(f"  ✅ 后复权价格数值合理")
                    else:
                        print(f"  ⚠️ 后复权价格数值可能有问题")
                else:
                    extra_fields = actual_fields - expected_fields
                    if extra_fields:
                        print(f"  ❌ 不应该有额外字段: {extra_fields}")
            else:
                print(f"  ❌ 无数据返回")
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            traceback.print_exc()

        # 测试3: 对比查询原始字段 vs 后复权字段的结果大小
        try:
            # 查询原始字段
            result_raw = self.data_manager.get_daily_quotes(
                stock_list=self.test_stocks[:1],
                start_date=self.start_date,
                end_date=self.end_date,
                columns=['instrument_id', 'trade_date', 'open', 'high', 'close'],
                page=1,
                page_size=2
            )
            
            # 查询对应的后复权字段
            result_adj = self.data_manager.get_daily_quotes(
                stock_list=self.test_stocks[:1],
                start_date=self.start_date,
                end_date=self.end_date,
                columns=['instrument_id', 'trade_date', 'adj_open', 'adj_high', 'adj_close'],
                page=1,
                page_size=2
            )
            
            print(f"\n测试3: 对比原始字段 vs 后复权字段")
            print(f"  📊 原始字段查询返回字段: {list(result_raw['data'][0].keys()) if result_raw['data'] else '无数据'}")
            print(f"  📊 后复权字段查询返回字段: {list(result_adj['data'][0].keys()) if result_adj['data'] else '无数据'}")
            
            if result_raw['data'] and result_adj['data']:
                raw_fields = set(result_raw['data'][0].keys())
                adj_fields = set(result_adj['data'][0].keys())
                
                # 检查字段没有重叠（除了公共字段instrument_id, trade_date）
                common_fields = {'instrument_id', 'trade_date'}
                raw_price_fields = raw_fields - common_fields
                adj_price_fields = adj_fields - common_fields
                
                print(f"  📊 原始价格字段: {raw_price_fields}")
                print(f"  📊 后复权价格字段: {adj_price_fields}")
                
                if not (raw_price_fields & adj_price_fields):  # 没有交集
                    print(f"  ✅ 很好！原始字段和后复权字段完全独立，没有混合")
                else:
                    overlap = raw_price_fields & adj_price_fields
                    print(f"  ⚠️ 存在重叠字段: {overlap}")
                    
        except Exception as e:
            print(f"❌ 对比测试失败: {e}")
            traceback.print_exc()

    def test_chip_conct_calculation(self):
        """测试筹码集中度计算正确性"""
        print(f"\n🚀 开始测试筹码集中度计算正确性")
        
        # 测试筹码集中度计算公式的正确性
        try:
            result = self.data_manager.get_daily_chips(
                stock_list=self.test_stocks[:1],
                start_date=self.start_date,
                end_date=self.end_date,
                columns=['instrument_id', 'trade_date', 'cost_5pct', 'cost_95pct', 'chip_conct'],
                page=1,
                page_size=5
            )
            
            print(f"\n📈 验证筹码集中度计算正确性:")
            if result['data']:
                for i, record in enumerate(result['data'][:3]):
                    cost_5pct = record['cost_5pct']
                    cost_95pct = record['cost_95pct']
                    chip_conct = record['chip_conct']
                    
                    # 手动计算筹码集中度
                    if cost_5pct is not None and cost_95pct is not None and (cost_5pct + cost_95pct) != 0:
                        expected_concentration = (cost_95pct - cost_5pct) / (cost_95pct + cost_5pct) * 100
                    else:
                        expected_concentration = None
                    
                    print(f"  记录 {i+1}:")
                    print(f"    cost_5pct: {cost_5pct}")
                    print(f"    cost_95pct: {cost_95pct}")
                    print(f"    数据库计算的筹码集中度: {chip_conct}")
                    print(f"    手动计算的筹码集中度: {expected_concentration}")
                    
                    # 检查计算是否一致（考虑浮点数精度）
                    if chip_conct is not None and expected_concentration is not None:
                        if abs(float(chip_conct) - float(expected_concentration)) < 0.01:
                            print(f"    ✅ 筹码集中度计算正确")
                        else:
                            print(f"    ❌ 筹码集中度计算不一致")
                    elif chip_conct is None and expected_concentration is None:
                        print(f"    ✅ 空值处理正确")
                    else:
                        print(f"    ❌ 空值处理不一致")
            else:
                print(f"  ❌ 无数据可验证")
        except Exception as e:
            print(f"❌ 筹码集中度计算验证失败: {e}")
            traceback.print_exc()

        # 测试只查询筹码集中度字段的数据合理性
        try:
            result = self.data_manager.get_daily_chips(
                stock_list=self.test_stocks[:2],
                start_date=self.start_date,
                end_date=self.end_date,
                columns=['instrument_id', 'trade_date', 'chip_conct'],
                page=1,
                page_size=10
            )
            
            print(f"\n📊 筹码集中度数据合理性分析:")
            if result['data']:
                concentrations = [float(record['chip_conct']) for record in result['data'] 
                                if record['chip_conct'] is not None]
                
                if concentrations:
                    min_concentration = min(concentrations)
                    max_concentration = max(concentrations)
                    avg_concentration = sum(concentrations) / len(concentrations)
                    
                    print(f"  📈 筹码集中度统计:")
                    print(f"    最小值: {min_concentration:.4f}%")
                    print(f"    最大值: {max_concentration:.4f}%")
                    print(f"    平均值: {avg_concentration:.4f}%")
                    print(f"    样本数: {len(concentrations)}")
                    
                    # 检查数据合理性（筹码集中度应该在合理范围内）
                    if 0 <= min_concentration <= 100 and 0 <= max_concentration <= 100:
                        print(f"    ✅ 筹码集中度数值在合理范围内 (0-100%)")
                    else:
                        print(f"    ⚠️ 筹码集中度数值可能超出合理范围")
                else:
                    print(f"  ❌ 无有效的筹码集中度数据")
            else:
                print(f"  ❌ 无数据返回")
        except Exception as e:
            print(f"❌ 筹码集中度数据合理性分析失败: {e}")
            traceback.print_exc()

    def test_error_cases(self):
        """测试错误情况"""
        print(f"\n🚀 开始测试错误情况")
        
        # 测试1: 股票列表和日期都为空
        try:
            result = self.data_manager.get_daily_quotes()
            print("❌ 应该抛出ValueError异常")
        except ValueError as e:
            print(f"✅ 正确捕获异常: {e}")
        except Exception as e:
            print(f"❌ 意外异常: {e}")

        # 测试2: 分页参数错误 - 页码为0
        try:
            result = self.data_manager.get_daily_quotes(
                stock_list=self.test_stocks,
                page=0,
                page_size=10
            )
            print("❌ 应该抛出ValueError异常")
        except ValueError as e:
            print(f"✅ 正确捕获异常: {e}")
        except Exception as e:
            print(f"❌ 意外异常: {e}")

        # 测试3: 分页参数不一致 - page有值但page_size为None
        try:
            result = self.data_manager.get_daily_quotes(
                stock_list=self.test_stocks,
                page=1,
                page_size=None
            )
            print("❌ 应该抛出ValueError异常")
        except ValueError as e:
            print(f"✅ 正确捕获异常: {e}")
        except Exception as e:
            print(f"❌ 意外异常: {e}")

        # 测试4: 分页参数不一致 - page为None但page_size有值
        try:
            result = self.data_manager.get_daily_quotes(
                stock_list=self.test_stocks,
                page=None,
                page_size=10
            )
            print("❌ 应该抛出ValueError异常")
        except ValueError as e:
            print(f"✅ 正确捕获异常: {e}")
        except Exception as e:
            print(f"❌ 意外异常: {e}")

        # 测试5: 空股票列表的最新行情查询
        try:
            result = self.data_manager.get_latest_quotes([])
            print("❌ 应该抛出ValueError异常")
        except ValueError as e:
            print(f"✅ 正确捕获异常: {e}")
        except Exception as e:
            print(f"❌ 意外异常: {e}")

        # 测试6: get_stock_quote股票代码和日期都为空
        try:
            result = self.data_manager.get_stock_quote()
            print("❌ 应该抛出ValueError异常")
        except ValueError as e:
            print(f"✅ 正确捕获异常: {e}")
        except Exception as e:
            print(f"❌ 意外异常: {e}")

    def test_pagination_consistency(self):
        """测试分页一致性"""
        print(f"\n🚀 开始测试分页一致性")
        
        # 测试1: 验证不分页时返回的page_size等于total
        try:
            result = self.data_manager.get_daily_quotes(
                stock_list=self.test_stocks[:1],
                start_date=self.start_date,
                end_date=self.end_date
            )
            if result['page'] == 1 and result['page_size'] == result['total']:
                print(f"✅ 不分页一致性测试通过: page=1, page_size={result['page_size']}, total={result['total']}")
            else:
                print(f"❌ 不分页一致性测试失败: page={result['page']}, page_size={result['page_size']}, total={result['total']}")
        except Exception as e:
            print(f"❌ 不分页一致性测试失败: {e}")

        # 测试2: 验证分页时的数据条数
        try:
            page_size = 5
            result = self.data_manager.get_daily_quotes(
                stock_list=self.test_stocks[:2],
                start_date=self.start_date,
                end_date=self.end_date,
                page=1,
                page_size=page_size
            )
            actual_data_count = len(result['data'])
            expected_count = min(page_size, result['total'])
            
            if actual_data_count == expected_count:
                print(f"✅ 分页数据条数测试通过: 期望{expected_count}条, 实际{actual_data_count}条")
            else:
                print(f"❌ 分页数据条数测试失败: 期望{expected_count}条, 实际{actual_data_count}条")
        except Exception as e:
            print(f"❌ 分页数据条数测试失败: {e}")

    def test_get_combined_stock_data(self):
        """测试合并股票数据功能"""
        print(f"\n🚀 开始测试 get_combined_stock_data")
        
        # 测试1: 小范围数据查询（包含行情+筹码+资金流）
        try:
            print("\n测试1: 小范围合并数据查询")
            df = self.data_manager.get_combined_stock_data(
                start_date=self.start_date,
                end_date=self.end_date,
                stock_list=self.test_stocks[:3],  # 只查询2只股票
                batch_years=1,
                include_quotes=True,
                include_chips=True,
                include_money_flow=True
            )
            
            print(f"  📊 查询结果形状: {df.shape}")
            print(f"  📊 列名: {df.columns.tolist()}")
            print(f"  📊 索引名称: {df.index.names}")
            print(f"  📊 数据类型概览:")
            for col in df.columns[:10]:  # 只显示前10列的类型
                print(f"    {col}: {df[col].dtype}")
            
            if not df.empty:
                print(f"  📊 前3行数据:")
                print(df.head(3))
                
                # 检查索引是否正确设置
                if df.index.names == ['trade_date', 'instrument_id']:
                    print(f"  ✅ 多级索引设置正确")
                else:
                    print(f"  ❌ 多级索引设置错误")
                
                # 检查是否包含三种数据的字段
                has_quotes = any(col in df.columns for col in ['open', 'high', 'low', 'close'])
                has_chips = any(col in df.columns for col in ['cost_5pct', 'cost_50pct', 'cost_95pct'])
                has_money_flow = any(col in df.columns for col in ['buy_sm_amount', 'net_mf_amount'])
                
                print(f"  📊 数据完整性检查:")
                print(f"    包含行情数据: {'✅' if has_quotes else '❌'}")
                print(f"    包含筹码数据: {'✅' if has_chips else '❌'}")
                print(f"    包含资金流数据: {'✅' if has_money_flow else '❌'}")
                
                # 检查数据类型转换
                if 'instrument_id' in df.columns:
                    print(f"    instrument_id类型: {df['instrument_id'].dtype} ({'✅' if df['instrument_id'].dtype == 'string' else '❌'})")
                
                # 检查trade_date是否在索引中并且是datetime类型
                if df.index.names[0] == 'trade_date':
                    trade_date_dtype = df.index.get_level_values(0).dtype
                    print(f"    trade_date类型: {trade_date_dtype} ({'✅' if 'datetime' in str(trade_date_dtype) else '❌'})")
                
            else:
                print(f"  ⚠️ 返回空DataFrame")
                
        except Exception as e:
            print(f"❌ 测试1失败: {e}")
            traceback.print_exc()

        # 测试2: 跨年查询（测试分批功能和筹码数据可能缺失的情况）
        try:
            print("\n测试2: 跨年查询（2017-2019，测试筹码数据缺失处理）")
            df = self.data_manager.get_combined_stock_data(
                start_date='2017-01-01',
                end_date='2019-01-31',
                stock_list=self.test_stocks[:1],  # 只查询1只股票
                batch_years=1,
                include_quotes=True,
                include_chips=True,
                include_money_flow=False  # 不包含资金流
            )
            
            print(f"  📊 查询结果形状: {df.shape}")
            
            if not df.empty:
                # 检查筹码数据是否有空值（2017年可能没有筹码数据）
                chips_columns = [col for col in df.columns if col.startswith(('cost_', 'his_', 'winner_', 'weight_', 'chip_'))]
                print(f"  📊 筹码相关列: {chips_columns}")
                
                if chips_columns:
                    print(f"  📊 筹码数据空值情况:")
                    for col in chips_columns[:5]:  # 只检查前5个筹码字段
                        null_count = df[col].isnull().sum()
                        total_count = len(df)
                        null_percentage = null_count / total_count * 100
                        print(f"    {col}: {null_count}/{total_count} ({null_percentage:.1f}% 为空)")
                        
                    # 特别检查是否符合预期：2017年的数据筹码字段应该为空，2018年之后应该有数据
                    df_reset = df.reset_index()
                    df_2017 = df_reset[df_reset['trade_date'].dt.year == 2017]
                    df_2018_after = df_reset[df_reset['trade_date'].dt.year >= 2018]
                    
                    if not df_2017.empty and not df_2018_after.empty:
                        # 检查2017年筹码数据是否为空
                        chips_2017_null = df_2017[chips_columns[0]].isnull().all() if chips_columns else True
                        # 检查2018年后是否有筹码数据
                        chips_2018_has_data = not df_2018_after[chips_columns[0]].isnull().all() if chips_columns else False
                        
                        print(f"  📊 时间段数据检查:")
                        print(f"    2017年筹码数据全为空: {'✅' if chips_2017_null else '❌'}")
                        print(f"    2018年后有筹码数据: {'✅' if chips_2018_has_data else '❌'}")
                
                print(f"  📊 前3行数据:")
                print(df.head(3))
            else:
                print(f"  ⚠️ 返回空DataFrame")
                
        except Exception as e:
            print(f"❌ 测试2失败: {e}")
            traceback.print_exc()

        # 测试3: 只查询行情数据
        try:
            print("\n测试3: 只查询行情数据")
            df = self.data_manager.get_combined_stock_data(
                start_date=self.start_date,
                end_date=self.end_date,
                stock_list=self.test_stocks[:1],
                batch_years=1,
                include_quotes=True,
                include_chips=False,
                include_money_flow=False
            )
            
            print(f"  📊 查询结果形状: {df.shape}")
            print(f"  📊 列名: {df.columns.tolist()}")
            
            if not df.empty:
                # 检查是否只包含行情数据
                has_quotes = any(col in df.columns for col in ['open', 'high', 'low', 'close'])
                has_chips = any(col in df.columns for col in ['cost_5pct', 'cost_50pct'])
                has_money_flow = any(col in df.columns for col in ['buy_sm_amount', 'net_mf_amount'])
                
                print(f"  📊 数据类型检查:")
                print(f"    包含行情数据: {'✅' if has_quotes else '❌'}")
                print(f"    包含筹码数据: {'❌' if not has_chips else '⚠️ 不应该包含'}")
                print(f"    包含资金流数据: {'❌' if not has_money_flow else '⚠️ 不应该包含'}")
                
                print(f"  📊 数据样例:")
                print(df.head(2))
            else:
                print(f"  ⚠️ 返回空DataFrame")
                
        except Exception as e:
            print(f"❌ 测试3失败: {e}")
            traceback.print_exc()

        # 测试4: 测试数据类型转换
        try:
            print("\n测试4: 数据类型转换验证")
            df = self.data_manager.get_combined_stock_data(
                start_date=self.start_date,
                end_date=self.end_date,
                stock_list=self.test_stocks[:1],
                batch_years=1,
                include_quotes=True,
                include_chips=True,
                include_money_flow=False
            )
            
            if not df.empty:
                print(f"  📊 重要字段的数据类型:")
                
                # 检查索引的数据类型
                if len(df.index.levels) >= 2:
                    trade_date_dtype = df.index.levels[0].dtype
                    instrument_id_dtype = df.index.levels[1].dtype
                    print(f"    trade_date (索引): {trade_date_dtype}")
                    print(f"    instrument_id (索引): {instrument_id_dtype}")
                
                # 检查数值字段的类型
                numeric_fields = ['open', 'high', 'low', 'close', 'volume', 'amount']
                for field in numeric_fields:
                    if field in df.columns:
                        dtype = df[field].dtype
                        print(f"    {field}: {dtype}")
                        
                # 检查筹码字段的类型
                chips_fields = ['cost_5pct', 'cost_50pct', 'cost_95pct']
                for field in chips_fields:
                    if field in df.columns:
                        dtype = df[field].dtype
                        print(f"    {field}: {dtype}")
                
                # 验证数值的合理性
                if 'open' in df.columns:
                    open_values = df['open'].dropna()
                    if not open_values.empty:
                        print(f"    开盘价范围: {open_values.min():.2f} - {open_values.max():.2f}")
                        print(f"    开盘价数据类型验证: {'✅' if open_values.dtype in ['float64', 'float32'] else '❌'}")
                
            else:
                print(f"  ⚠️ 返回空DataFrame，无法验证数据类型")
                
        except Exception as e:
            print(f"❌ 测试4失败: {e}")
            traceback.print_exc()

        # 测试5: 错误情况测试
        try:
            print("\n测试5: 错误情况测试")
            
            # 测试空的开始和结束日期
            try:
                df = self.data_manager.get_combined_stock_data(
                    start_date='',
                    end_date='',
                    stock_list=self.test_stocks[:1]
                )
                print("  ❌ 应该抛出ValueError异常")
            except ValueError as e:
                print(f"  ✅ 正确捕获空日期异常: {e}")
            
            # 测试不包含行情数据
            try:
                df = self.data_manager.get_combined_stock_data(
                    start_date=self.start_date,
                    end_date=self.end_date,
                    stock_list=self.test_stocks[:1],
                    include_quotes=False
                )
                print("  ❌ 应该抛出ValueError异常")
            except ValueError as e:
                print(f"  ✅ 正确捕获必需行情数据异常: {e}")
                
        except Exception as e:
            print(f"❌ 测试5失败: {e}")
            traceback.print_exc()

        # 测试6: 大范围分批查询（如果数据库连接正常）
        try:
            print("\n测试6: 分批查询功能测试")
            df = self.data_manager.get_combined_stock_data(
                start_date='2018-01-01',
                end_date='2020-12-31',  # 3年数据，会分批查询
                stock_list=self.test_stocks[:3],
                batch_years=3,  # 每年一批
                include_quotes=True,
                include_chips=True,
                include_money_flow=False
            )
            
            print(f"  📊 分批查询结果形状: {df.shape}")
            
            if not df.empty:
                # 检查日期范围是否正确
                df_reset = df.reset_index()
                min_date = df_reset['trade_date'].min()
                max_date = df_reset['trade_date'].max()
                print(f"  📊 实际日期范围: {min_date.date()} 到 {max_date.date()}")
                
                # 检查数据是否按索引正确排序
                is_sorted = df_reset['trade_date'].is_monotonic_increasing
                print(f"  📊 数据按日期排序: {'✅' if is_sorted else '❌'}")
                
                # 检查是否有重复数据
                duplicates = df.index.duplicated().sum()
                print(f"  📊 重复索引数量: {duplicates} ({'✅' if duplicates == 0 else '❌'}")
                
            else:
                print(f"  ⚠️ 分批查询返回空DataFrame")
                
        except Exception as e:
            print(f"❌ 测试6失败: {e}")
            traceback.print_exc()

        # 测试7: 验证列结构一致性（即使某些数据为空）
        try:
            print("\n测试7: 验证列结构一致性")
            
            # 测试早期年份（可能筹码数据为空）
            df_early = self.data_manager.get_combined_stock_data(
                start_date='2015-01-01',
                end_date='2015-01-31',
                stock_list=self.test_stocks[:1],
                batch_years=1,
                include_quotes=True,
                include_chips=True,
                include_money_flow=True
            )
            
            # 测试后期年份（筹码数据应该存在）
            df_later = self.data_manager.get_combined_stock_data(
                start_date='2019-01-01',
                end_date='2019-01-31',
                stock_list=self.test_stocks[:1],
                batch_years=1,
                include_quotes=True,
                include_chips=True,
                include_money_flow=True
            )
            
            print(f"  📊 早期数据形状: {df_early.shape}")
            print(f"  📊 后期数据形状: {df_later.shape}")
            
            if not df_early.empty and not df_later.empty:
                early_columns = set(df_early.columns)
                later_columns = set(df_later.columns)
                
                print(f"  📊 早期列数: {len(early_columns)}")
                print(f"  📊 后期列数: {len(later_columns)}")
                
                # 检查列结构是否一致
                if early_columns == later_columns:
                    print(f"  ✅ 列结构完全一致！")
                    
                    # 检查早期数据是否包含预期的筹码字段（即使值为空）
                    expected_chips_columns = ['cost_5pct', 'cost_50pct', 'cost_95pct', 'chip_conct']
                    has_chips_columns = all(col in early_columns for col in expected_chips_columns)
                    print(f"  📊 早期数据包含筹码字段: {'✅' if has_chips_columns else '❌'}")
                    
                    if has_chips_columns:
                        # 检查早期数据的筹码字段是否为空值
                        chips_null_count = df_early[expected_chips_columns[0]].isnull().sum()
                        total_count = len(df_early)
                        print(f"  📊 早期筹码数据空值率: {chips_null_count}/{total_count} ({chips_null_count/total_count*100:.1f}%)")
                        
                        if chips_null_count == total_count:
                            print(f"  ✅ 早期筹码数据正确地全为空值（NaN）")
                        else:
                            print(f"  ⚠️ 早期筹码数据不全为空")
                    
                else:
                    missing_in_early = later_columns - early_columns
                    missing_in_later = early_columns - later_columns
                    
                    if missing_in_early:
                        print(f"  ❌ 早期数据缺少列: {missing_in_early}")
                    if missing_in_later:
                        print(f"  ❌ 后期数据缺少列: {missing_in_later}")
            else:
                print(f"  ⚠️ 数据为空，无法验证列结构一致性")
                
        except Exception as e:
            print(f"❌ 测试7失败: {e}")
            traceback.print_exc()

    def test_cache_mechanism(self):
        """测试缓存机制"""
        print(f"\n🚀 开始测试缓存机制")
        
        # 测试1: 缓存合并数据
        try:
            print("\n测试1: 缓存合并数据")
            
            # 先清空缓存
            self.data_manager.clear_cache()
            cache_info = self.data_manager.get_cache_info()
            print(f"  📊 清空后缓存状态: {cache_info}")
            
            # 缓存数据
            print(f"  📊 开始缓存数据: {self.start_date} 到 {self.end_date}")
            self.data_manager.cache_combined_data(
                start_date=self.start_date,
                end_date=self.end_date,
                stock_list=self.test_stocks[:3],  # 缓存3只股票
                batch_years=1
            )
            
            # 检查缓存信息
            cache_info = self.data_manager.get_cache_info()
            if cache_info:
                print(f"  ✅ 缓存成功!")
                print(f"    缓存日期范围: {cache_info['start_date']} 到 {cache_info['end_date']}")
                print(f"    缓存股票数量: {cache_info['stock_count']}")
                print(f"    缓存数据形状: {cache_info['data_shape']}")
                print(f"    缓存列数: {len(cache_info['columns'])}")
                
                # 测试缓存验证函数
                is_valid = self.data_manager._is_cache_valid(
                    start_date=self.start_date,
                    end_date=self.end_date,
                    stock_list=self.test_stocks[:2]
                )
                print(f"    缓存验证测试: {'✅通过' if is_valid else '❌失败'}")
            else:
                print(f"  ❌ 缓存失败")
                
        except Exception as e:
            print(f"❌ 测试1失败: {e}")
            traceback.print_exc()

        # 测试2: 缓存命中 - 完全包含的查询
        try:
            print("\n测试2: 缓存命中测试（完全包含）")
            
            # 获取实际缓存的日期范围
            cache_info = self.data_manager.get_cache_info()
            if cache_info:
                actual_start = cache_info['start_date']
                actual_end = cache_info['end_date']
                print(f"  📊 使用实际缓存范围: {actual_start} 到 {actual_end}")
            else:
                actual_start = '2014-01-02'
                actual_end = '2024-01-31'
            
            # 查询缓存范围内的数据
            result = self.data_manager.get_daily_quotes(
                stock_list=self.test_stocks[:2],  # 查询前2只股票（包含在缓存的3只中）
                start_date=actual_start,
                end_date=actual_end,
                page=1,
                page_size=5
            )
            
            print(f"  📊 查询结果: 总数={result.get('total', 0)}, 返回={len(result.get('data', []))}")
            if result.get('data'):
                first_record = result['data'][0]
                print(f"  📊 第一条数据字段: {list(first_record.keys())}")
                print(f"  📊 第一条数据: {first_record}")
                print(f"  ✅ 缓存命中，成功获取行情数据")
            else:
                print(f"  ❌ 缓存命中但无数据返回")
                
        except Exception as e:
            print(f"❌ 测试2失败: {e}")
            traceback.print_exc()

        # 测试3: 缓存命中 - 筹码数据
        try:
            print("\n测试3: 缓存命中测试（筹码数据）")
            
            # 使用实际缓存范围
            cache_info = self.data_manager.get_cache_info()
            actual_start = cache_info['start_date'] if cache_info else '2014-01-02'
            actual_end = cache_info['end_date'] if cache_info else '2024-01-31'
            
            result = self.data_manager.get_daily_chips(
                stock_list=self.test_stocks[:1],
                start_date=actual_start,
                end_date=actual_end,
                columns=['instrument_id', 'trade_date', 'cost_50pct', 'chip_conct'],
                page=1,
                page_size=3
            )
            
            print(f"  📊 查询结果: 总数={result.get('total', 0)}")
            if result.get('data'):
                first_record = result['data'][0]
                print(f"  📊 返回的字段: {list(first_record.keys())}")
                print(f"  📊 筹码集中度: {first_record.get('chip_conct', 'N/A')}")
                print(f"  ✅ 缓存命中，成功获取筹码数据")
            else:
                print(f"  ❌ 缓存命中但无筹码数据")
                
        except Exception as e:
            print(f"❌ 测试3失败: {e}")
            traceback.print_exc()

        # 测试4: 缓存命中 - 资金流数据
        try:
            print("\n测试4: 缓存命中测试（资金流数据）")
            
            # 使用实际缓存范围
            cache_info = self.data_manager.get_cache_info()
            actual_start = cache_info['start_date'] if cache_info else '2014-01-02'
            actual_end = cache_info['end_date'] if cache_info else '2024-01-31'
            
            result = self.data_manager.get_daily_money_flow(
                stock_list=self.test_stocks[:1],
                start_date=actual_start,
                end_date=actual_end,
                columns=['instrument_id', 'trade_date', 'net_mf_amount', 'buy_lg_amount'],
                page=1,
                page_size=3
            )
            
            print(f"  📊 查询结果: 总数={result.get('total', 0)}")
            if result.get('data'):
                first_record = result['data'][0]
                print(f"  📊 返回的字段: {list(first_record.keys())}")
                print(f"  📊 净流入额: {first_record.get('net_mf_amount', 'N/A')}")
                print(f"  ✅ 缓存命中，成功获取资金流数据")
            else:
                print(f"  ❌ 缓存命中但无资金流数据")
                
        except Exception as e:
            print(f"❌ 测试4失败: {e}")
            traceback.print_exc()

        # 测试5: 缓存未命中 - 超出日期范围
        try:
            print("\n测试5: 缓存未命中测试（超出日期范围）")
            
            # 查询超出缓存日期范围的数据
            future_start = '2017-01-01'  # 超出缓存范围
            future_end = '2017-01-31'
            
            result = self.data_manager.get_daily_quotes(
                stock_list=self.test_stocks[:1],
                start_date=future_start,
                end_date=future_end,
                page=1,
                page_size=3
            )
            
            print(f"  📊 超出缓存范围查询: {future_start} 到 {future_end}")
            print(f"  📊 查询结果: 总数={result.get('total', 0)}")
            if result.get('total', 0) > 0:
                print(f"  ✅ 缓存未命中，成功从数据库获取数据")
            else:
                print(f"  ⚠️ 缓存未命中，但该时期可能确实无数据")
                
        except Exception as e:
            print(f"❌ 测试5失败: {e}")
            traceback.print_exc()

        # 测试6: 缓存未命中 - 超出股票范围
        try:
            print("\n测试6: 缓存未命中测试（超出股票范围）")
            
            # 查询不在缓存中的股票
            other_stocks = ['300001.SZ']  # 不在缓存的股票列表中
            
            result = self.data_manager.get_daily_quotes(
                stock_list=other_stocks,
                start_date=self.start_date,
                end_date=self.end_date,
                page=1,
                page_size=3
            )
            
            print(f"  📊 查询缓存外股票: {other_stocks}")
            print(f"  📊 查询结果: 总数={result.get('total', 0)}")
            if result.get('total', 0) > 0:
                print(f"  ✅ 缓存未命中，成功从数据库获取数据")
            else:
                print(f"  ⚠️ 缓存未命中，但该股票可能确实无数据")
                
        except Exception as e:
            print(f"❌ 测试6失败: {e}")
            traceback.print_exc()

        # 测试7: 缓存性能对比
        try:
            print("\n测试7: 缓存性能对比测试")
            
            import time
            
            # 测试缓存命中的查询时间
            start_time = time.time()
            result_cached = self.data_manager.get_daily_quotes(
                stock_list=self.test_stocks[:4],
                start_date='2018-01-01',
                end_date='2022-01-31'
            )
            cached_time = time.time() - start_time
            
            # 清空缓存，测试数据库查询时间
            self.data_manager.clear_cache()
            start_time = time.time()
            result_db = self.data_manager.get_daily_quotes(
                stock_list=self.test_stocks[:4],
                start_date='2018-01-01',
                end_date='2022-01-31'
            )
            db_time = time.time() - start_time
            
            print(f"  📊 缓存查询时间: {cached_time:.4f}秒")
            print(f"  📊 数据库查询时间: {db_time:.4f}秒")
            print(f"  📊 缓存数据量: {result_cached.get('total', 0)}")
            print(f"  📊 数据库数据量: {result_db.get('total', 0)}")
            
            if cached_time < db_time:
                speedup = db_time / cached_time
                print(f"  ✅ 缓存加速 {speedup:.2f}x")
            else:
                print(f"  ⚠️ 缓存未显示明显加速（可能数据量太小）")
                
        except Exception as e:
            print(f"❌ 测试7失败: {e}")
            traceback.print_exc()

        # # 测试8: 测试不同字段组合的缓存命中
        # try:
        #     print("\n测试8: 不同字段组合缓存命中测试")
            
        #     # 重新缓存数据
        #     self.data_manager.cache_combined_data(
        #         start_date=self.start_date,
        #         end_date=self.end_date,
        #         stock_list=self.test_stocks[:2]
        #     )
            
        #     # 测试只查询后复权价格字段
        #     result1 = self.data_manager.get_daily_quotes(
        #         stock_list=self.test_stocks[:1],
        #         start_date=self.start_date,
        #         end_date=self.end_date,
        #         columns=['instrument_id', 'trade_date', 'adj_open', 'adj_close'],
        #         page=1,
        #         page_size=2
        #     )
            
        #     print(f"  📊 后复权价格字段查询:")
        #     if result1.get('data'):
        #         fields = list(result1['data'][0].keys())
        #         print(f"    返回字段: {fields}")
        #         expected = ['instrument_id', 'trade_date', 'adj_open', 'adj_close']
        #         if set(fields) == set(expected):
        #             print(f"    ✅ 字段过滤正确")
        #         else:
        #             print(f"    ❌ 字段过滤错误")
            
        #     # 测试混合字段查询
        #     result2 = self.data_manager.get_daily_chips(
        #         stock_list=self.test_stocks[:1],
        #         start_date=self.start_date,
        #         end_date=self.end_date,
        #         columns=['instrument_id', 'trade_date', 'cost_5pct', 'cost_95pct', 'chip_conct'],
        #         page=1,
        #         page_size=2
        #     )
            
        #     print(f"  📊 筹码混合字段查询:")
        #     if result2.get('data'):
        #         fields = list(result2['data'][0].keys())
        #         print(f"    返回字段: {fields}")
        #         if 'chip_conct' in fields:
        #             print(f"    ✅ 包含计算字段 chip_conct")
        #         else:
        #             print(f"    ❌ 缺少计算字段 chip_conct")
                    
        # except Exception as e:
        #     print(f"❌ 测试8失败: {e}")
        #     traceback.print_exc()

        # print(f"\n🎉 缓存机制测试完成！")

    def run_all_tests(self):
        """运行所有测试"""
        print("🎯 开始执行 StockDataManager 功能测试")
        print(f"连接信息: {settings.clickhouse_host}:{settings.clickhouse_port}")
        print(f"数据库: {settings.clickhouse_database}")
        
        try:
            # 运行各项测试
            # self.test_get_daily_quotes()
            # self.test_adj_fields_only()  # 新增的专门测试后复权字段的函数
            # self.test_get_daily_chips()
            # self.test_chip_conct_calculation()  # 新增的专门测试筹码集中度的函数
            # self.test_get_daily_money_flow()
            # self.test_get_stock_industry()
            # self.test_get_stock_quote()
            # self.test_get_latest_quotes()
            # self.test_error_cases()
            # self.test_pagination_consistency()
            # self.test_get_combined_stock_data()  # 新增的合并数据测试
            self.test_cache_mechanism()  # 新增的缓存机制测试
            
            print(f"\n🎉 所有测试执行完成！")
            
        except Exception as e:
            print(f"\n💥 测试过程中发生错误: {e}")
            traceback.print_exc()

def main():
    """主函数"""
    # 创建测试实例并运行测试
    tester = StockDataManagerTester()
    tester.run_all_tests()

if __name__ == "__main__":
    main() 