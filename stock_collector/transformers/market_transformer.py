"""
市场数据转换器
处理股票市场数据的格式转换和清洗
"""

import os
from typing import List, Dict, Any
from datetime import datetime, UTC
import pandas as pd
from ..core.base_transformer import BaseTransformer
from app.server.core.log import logger


class MarketTransformer(BaseTransformer):
    """市场数据转换器"""
    def get_data_file_path(self, ts_code: str, **kwargs) -> str:
        """获取市场数据文件路径"""
        target_dir = os.path.join(self.data_dir, "market", "no_adj", "D")
        filename = f"{ts_code.replace('.', '_')}.csv"
        return os.path.join(target_dir, filename)
    
    def get_data_description(self) -> str:
        return "行情数据"
    
    def transform_data(
        self, 
        df: pd.DataFrame, 
        trade_calendar: pd.DataFrame, 
        data_type: str = 'share', 
        exclude_date: str = None,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        转换市场数据格式以适配ClickHouse表结构
        
        Args:
            df: 原始数据DataFrame
            trade_calendar: 交易日历DataFrame
            data_type: 数据类型，'share'为股票，'index'为指数，'sector'为板块
            exclude_date: 排除的日期
            **kwargs: 额外参数
            
        Returns:
            转换后的数据列表
        """
        try:
            
            if trade_calendar is None or trade_calendar.empty:
                raise ValueError("交易日历为空")
            
            # 准备交易日历映射（仅对股票数据）
            calendar_map = {}

            for _, row in trade_calendar.iterrows():
                calendar_map[str(int(row['cal_date']))] = str(int(row['pretrade_date']))
                    
            # 设置multi-index进行计算
            df['trade_date'] = df['trade_date'].astype(str)
            df = df.set_index(['ts_code', 'trade_date']).sort_index()
            # 计算change和return
            df['calculated_change'] = None
            df['calculated_return'] = None

            # 按股票分组计算
            for stock_code in df.index.get_level_values(0).unique():
                stock_data = df.loc[stock_code]
                stock_data = stock_data.sort_index()  # 按日期排序

                for trade_date in stock_data.index:
                    current_date = str(int(trade_date))
                    current_close = stock_data.loc[trade_date, 'close']
                    
                    # 获取前一个交易日
                    prev_trade_date = calendar_map.get(current_date)
                    prev_close = None

                    # if prev_trade_date not in stock_data.index:
                    #     print(f"stock_code: {stock_code}, prev_trade_date: {prev_trade_date} not in stock_data.index")

                    if prev_trade_date and prev_trade_date in stock_data.index:
                        prev_close = stock_data.loc[prev_trade_date, 'close']


                    # 计算change和return
                    if not pd.isna(prev_close) and not pd.isna(current_close):
                        df.loc[(stock_code, trade_date), 'calculated_change'] = current_close - prev_close
                        if prev_close != 0:
                            df.loc[(stock_code, trade_date), 'calculated_return'] = (current_close - prev_close) / prev_close
                        else:
                            df.loc[(stock_code, trade_date), 'calculated_return'] = None
                    else:
                        # 没有前一日数据或者没有当日数据，change和return为None
                        df.loc[(stock_code, trade_date), 'calculated_change'] = None
                        df.loc[(stock_code, trade_date), 'calculated_return'] = None

            # 重置index
            df = df.reset_index()


            # for i in range(len(df)):
            #     current_date = str(int(df.iloc[i]['trade_date']))
            #     current_close = df.iloc[i]['close']
                
            #     # 获取前一个交易日
            #     prev_trade_date = None
            #     if current_date in calendar_map:
            #         # 股票数据使用交易日历获取前一个交易日
            #         prev_trade_date = calendar_map[current_date]
            #     else:
            #         logger.warning(f"交易日 {current_date} 在交易日历中未找到对应的前一交易日")
                
            #     # 查找前一个交易日的收盘价
            #     prev_close = None
            #     if prev_trade_date:
            #         if is_index and i > 0:
            #             # 指数数据直接使用前一条记录
            #             prev_close = df.iloc[i-1]['close']
            #         else:
            #             # 股票数据查找对应交易日的记录
            #             prev_row = df[df['trade_date'] == int(prev_trade_date)]
            #             if prev_row is not None and not prev_row.empty:
            #                 prev_close = prev_row.iloc[0]['close']
                
            #     # 计算change和return
            #     if prev_close is not None and not pd.isna(prev_close) and not pd.isna(current_close):
            #         df.at[i, 'calculated_change'] = current_close - prev_close
            #         if prev_close != 0:
            #             df.at[i, 'calculated_return'] = (current_close - prev_close) / prev_close
            #         else:
            #             df.at[i, 'calculated_return'] = None
            #     else:
            #         df.at[i, 'calculated_change'] = None
            #         df.at[i, 'calculated_return'] = None
            # 定义必需的行情数据字段

            # 排除前一日数据（exclude_date）
            if exclude_date:
                before_count = len(df)
                df = df[df['trade_date'] != exclude_date]
                after_count = len(df)
                logger.info(f"排除前一日数据 {exclude_date}，从 {before_count} 条减少到 {after_count} 条")

            processed_data = []
            skipped_rows = 0
                
            required_price_fields = ['open', 'high', 'low', 'close']
            required_volume_fields = ['vol']
            if data_type != 'sector':
                required_volume_fields.append('amount')

            for _, row in df.iterrows():
                # 跳过缺失关键数据的行
                if pd.isna(row['ts_code']) or pd.isna(row['trade_date']):
                    skipped_rows += 1
                    continue
                
                # 检查价格数据完整性
                price_missing = any(pd.isna(row[field]) for field in required_price_fields)
                if price_missing:
                    missing_fields = [field for field in required_price_fields if pd.isna(row[field])]
                    logger.warning(f"  跳过 {row['ts_code']} {row['trade_date']}: 价格数据缺失 {missing_fields}")
                    skipped_rows += 1
                    continue
                
                # 检查成交量和成交额数据完整性
                volume_missing = any(pd.isna(row[field]) for field in required_volume_fields)
                if volume_missing:
                    missing_fields = [field for field in required_volume_fields if pd.isna(row[field])]
                    logger.warning(f"  跳过 {row['ts_code']} {row['trade_date']}: 成交数据缺失 {missing_fields}")
                    skipped_rows += 1
                    continue
                
                # 检查复权因子精度范围（仅对股票数据）
                if data_type == 'share' and not pd.isna(row.get('adj_factor')):
                    adj_factor_val = float(row['adj_factor'])
                    if adj_factor_val <= 0 or adj_factor_val > 999999.999999:
                        logger.warning(f"  跳过 {row['ts_code']} {row['trade_date']}: 复权因子超出合理范围 {adj_factor_val}")
                        skipped_rows += 1
                        continue
                
                # 检查数据合理性
                if row['vol'] < 0 or ('amount' in row and row['amount'] < 0):
                    logger.warning(f"  跳过 {row['ts_code']} {row['trade_date']}: 成交数据异常 (vol={row['vol']}, amount={row['amount']})")
                    skipped_rows += 1
                    continue
                
                # 检查价格数据合理性
                if any(row[field] <= 0 for field in required_price_fields):
                    invalid_prices = {field: row[field] for field in required_price_fields if row[field] <= 0}
                    logger.warning(f"  跳过 {row['ts_code']} {row['trade_date']}: 价格数据异常 {invalid_prices}")
                    skipped_rows += 1
                    continue
                
                # 检查价格数据精度范围
                max_price = 99999999.9999
                if any(row[field] > max_price for field in required_price_fields):
                    invalid_prices = {field: row[field] for field in required_price_fields if row[field] > max_price}
                    logger.warning(f"  跳过 {row['ts_code']} {row['trade_date']}: 价格数据超出精度范围 {invalid_prices}")
                    skipped_rows += 1
                    continue
                
                # 处理交易日期格式
                trade_date = self._parse_date(row['trade_date'])
                
                # 构建数据记录
                record = {
                    'instrument_id': row['ts_code'],
                    'trade_date': trade_date,
                    # 价格数据：Decimal32(4) - 保留4位小数
                    'open': self._to_decimal(row['open'], precision=4),
                    'high': self._to_decimal(row['high'], precision=4),
                    'low': self._to_decimal(row['low'], precision=4),
                    'close': self._to_decimal(row['close'], precision=4),
                    'volume': self._to_int(row['vol'], multiplier=100),  # 成交量单位：手 -> 股
                    # 涨跌额：Decimal64(8) - 保留8位小数，涨跌幅：Decimal32(4) - 保留4位小数
                    'change': self._to_decimal(row['calculated_change'], precision=8),
                    'pct_chg': self._to_decimal(row['calculated_return'], precision=4),
                    'ingest_time': datetime.now(UTC)
                }

                if 'amount' in row:
                    record['amount'] = self._to_decimal(row['amount'], multiplier=1000, precision=2)  # 千元 -> 元
                
                # 复权因子（仅对股票数据）
                if data_type == 'share':
                    record['adj_factor'] = self._to_decimal(row.get('adj_factor'), precision=6)
                
                # 板块数据
                if data_type == 'sector':
                    fields = ['total_mv', 'float_mv']
                    if any(not pd.isna(row[field]) and row[field] < 0 for field in fields):
                        logger.warning(f"  跳过 {row['ts_code']} {row['trade_date']}: 市值数据异常 {row[fields]}")
                        skipped_rows += 1
                        continue
                    if not pd.isna(row['avg_price']) and row['avg_price'] <= 0:
                        logger.warning(f"  跳过 {row['ts_code']} {row['trade_date']}: 平均价数据异常 {row['avg_price']}")
                        skipped_rows += 1
                        continue
                    if not pd.isna(row['turnover_rate']) and row['turnover_rate'] < 0:
                        logger.warning(f"  跳过 {row['ts_code']} {row['trade_date']}: 换手率数据异常 {row['turnover_rate']}")
                        skipped_rows += 1
                        continue

                    record['avg_price'] = self._to_decimal(row['avg_price'], precision=4)
                    record['turnover_rate'] = self._to_decimal(row['turnover_rate'], precision=6)
                    record['total_mv'] = self._to_decimal(row['total_mv'], precision=2)
                    record['float_mv'] = self._to_decimal(row['float_mv'], precision=2)
                    # 这里change用4位小数，pct_chg用6位小数。
                    # TODO: 后续如果有机会重新整理数据库，所有表统一用这个标准。或者改为pct_chg用百分数，然后保留4位小数。
                    record['change'] = self._to_decimal(row['calculated_change'], precision=4)
                    record['pct_chg'] = self._to_decimal(row['calculated_return'], precision=6)
                
                processed_data.append(record)
            
            if skipped_rows > 0:
                logger.warning(f"  数据质量检查完成: 跳过了 {skipped_rows} 行不完整或异常的数据")
            
            return processed_data
            
        except Exception as e:
            logger.error(f"处理市场数据时发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
            return []
    
    def process_stock_data(self, ts_code: str, **kwargs) -> List[Dict[str, Any]]:
        """
        处理单个股票的市场数据，包括合并复权因子
        
        Args:
            ts_code: 股票代码
            **kwargs: 额外参数
            
        Returns:
            处理后的数据列表
        """
        try:
            data_type = kwargs.get('data_type', 'share')
            
            # 读取主数据文件
            main_data_file = self.get_data_file_path(ts_code, **kwargs)
            if not os.path.exists(main_data_file):
                logger.error(f"✗ 未找到股票 {ts_code} 的{self.get_data_description()}文件: {main_data_file}")
                return []
            
            main_df = pd.read_csv(main_data_file)
            
            if data_type == 'share':
                # 股票数据需要合并复权因子
                adj_factor_file = os.path.join(self.data_dir, "market", "adj_factor", f"{ts_code.replace('.', '_')}.csv")
                if not os.path.exists(adj_factor_file):
                    logger.error(f"✗ 未找到股票 {ts_code} 的复权因子文件: {adj_factor_file}")
                    return []
                
                adj_factor_df = pd.read_csv(adj_factor_file)
                
                # 合并数据
                merged_df = pd.merge(
                    main_df, 
                    adj_factor_df[['ts_code', 'trade_date', 'adj_factor']], 
                    on=['ts_code', 'trade_date'], 
                    how='left'
                )
            else:
                # 指数和板块数据不需要复权因子
                merged_df = main_df
            
            if merged_df.empty:
                logger.warning(f"✗ 股票 {ts_code} 合并后数据为空")
                return []
            
            # 转换数据格式
            processed_data = self.transform_data(merged_df, **kwargs)
            
            return processed_data
            
        except Exception as e:
            logger.error(f"处理股票 {ts_code} 市场数据时发生错误: {str(e)}")
            return []