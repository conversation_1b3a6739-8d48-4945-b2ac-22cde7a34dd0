"""
板块数据转换器
处理板块数据的格式转换和清洗，继承市场数据转换器
"""
import os
from .market_transformer import MarketTransformer


class SectorTransformer(MarketTransformer):
    """板块数据转换器，继承市场数据转换器"""
    
    def get_data_description(self) -> str:
        return "板块数据"
    
    def get_data_file_path(self, ts_code: str, **kwargs) -> str:
        """获取板块数据文件路径"""
        target_dir = os.path.join(self.data_dir, "sector", "sector_daily")
        filename = f"{ts_code.replace('.', '_')}.csv"
        return os.path.join(target_dir, filename)
    
    def process_stock_data(self, ts_code: str, **kwargs) -> list:
        """
        处理单个板块的数据
        
        Args:
            ts_code: 板块代码
            **kwargs: 额外参数
            
        Returns:
            处理后的数据列表
        """
        # 板块数据处理，设置data_type='sector'
        kwargs['data_type'] = 'sector'
        return super().process_stock_data(ts_code, **kwargs) 