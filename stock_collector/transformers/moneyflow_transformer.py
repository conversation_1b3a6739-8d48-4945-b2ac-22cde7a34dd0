"""
资金流向数据转换器
处理股票资金流向数据的格式转换和清洗
"""

import os
from typing import List, Dict, Any
from datetime import datetime, UTC
import pandas as pd
from ..core.base_transformer import BaseTransformer
from app.server.core.log import logger


class MoneyflowTransformer(BaseTransformer):
    """资金流向数据转换器"""
    
    def get_data_file_path(self, ts_code: str, **kwargs) -> str:
        """获取资金流向数据文件路径"""
        target_dir = os.path.join(self.data_dir, "moneyflow", "moneyflow")
        filename = f"{ts_code.replace('.', '_')}.csv"
        return os.path.join(target_dir, filename)
    
    def get_data_description(self) -> str:
        return "资金流向数据"
    
    def transform_data(self, df: pd.DataFrame, **kwargs) -> List[Dict[str, Any]]:
        """
        转换资金流向数据格式以适配ClickHouse表结构
        
        Args:
            df: 资金流向数据DataFrame
            **kwargs: 额外参数
            
        Returns:
            转换后的数据列表
        """
        try:
            processed_data = []
            skipped_rows = 0
            
            for _, row in df.iterrows():
                # 跳过缺失关键数据的行
                if pd.isna(row['ts_code']) or pd.isna(row['trade_date']):
                    skipped_rows += 1
                    continue
                
                # 检查成交量数据合理性
                volume_fields = ['buy_sm_vol', 'sell_sm_vol', 'buy_md_vol', 'sell_md_vol', 
                               'buy_lg_vol', 'sell_lg_vol', 'buy_elg_vol', 'sell_elg_vol', 'net_mf_vol']
                
                invalid_volumes = {}
                for field in volume_fields:
                    if not pd.isna(row[field]) and row[field] < 0:
                        # net_mf_vol 允许为负数，其他字段不允许
                        if field != 'net_mf_vol':
                            invalid_volumes[field] = row[field]
                
                if invalid_volumes:
                    logger.warning(f"  跳过 {row['ts_code']} {row['trade_date']}: 成交量数据异常 {invalid_volumes}")
                    skipped_rows += 1
                    continue
                
                # 检查成交金额数据合理性
                amount_fields = ['buy_sm_amount', 'sell_sm_amount', 'buy_md_amount', 'sell_md_amount', 
                               'buy_lg_amount', 'sell_lg_amount', 'buy_elg_amount', 'sell_elg_amount', 'net_mf_amount']
                
                invalid_amounts = {}
                for field in amount_fields:
                    if not pd.isna(row[field]) and row[field] < 0:
                        # net_mf_amount 允许为负数，其他字段不允许
                        if field != 'net_mf_amount':
                            invalid_amounts[field] = row[field]
                
                if invalid_amounts:
                    logger.warning(f"  跳过 {row['ts_code']} {row['trade_date']}: 成交金额数据异常 {invalid_amounts}")
                    skipped_rows += 1
                    continue
                
                # 处理交易日期格式
                trade_date = self._parse_date(row['trade_date'])
                
                # 构建数据记录，进行单位转换：手→股(*100)，万元→元(*10000)
                record = {
                    'instrument_id': row['ts_code'],
                    'trade_date': trade_date,
                    # 小单数据转换：手→股，万元→元
                    'buy_sm_vol': self._to_int(row['buy_sm_vol'], 100),
                    'buy_sm_amount': self._to_decimal(row['buy_sm_amount'], 10000),
                    'sell_sm_vol': self._to_int(row['sell_sm_vol'], 100),
                    'sell_sm_amount': self._to_decimal(row['sell_sm_amount'], 10000),
                    # 中单数据转换：手→股，万元→元
                    'buy_md_vol': self._to_int(row['buy_md_vol'], 100),
                    'buy_md_amount': self._to_decimal(row['buy_md_amount'], 10000),
                    'sell_md_vol': self._to_int(row['sell_md_vol'], 100),
                    'sell_md_amount': self._to_decimal(row['sell_md_amount'], 10000),
                    # 大单数据转换：手→股，万元→元
                    'buy_lg_vol': self._to_int(row['buy_lg_vol'], 100),
                    'buy_lg_amount': self._to_decimal(row['buy_lg_amount'], 10000),
                    'sell_lg_vol': self._to_int(row['sell_lg_vol'], 100),
                    'sell_lg_amount': self._to_decimal(row['sell_lg_amount'], 10000),
                    # 特大单数据转换：手→股，万元→元
                    'buy_elg_vol': self._to_int(row['buy_elg_vol'], 100),
                    'buy_elg_amount': self._to_decimal(row['buy_elg_amount'], 10000),
                    'sell_elg_vol': self._to_int(row['sell_elg_vol'], 100),
                    'sell_elg_amount': self._to_decimal(row['sell_elg_amount'], 10000),
                    # 净流入数据转换：手→股，万元→元（允许为负数）
                    'net_mf_vol': self._to_int(row['net_mf_vol'], 100),
                    'net_mf_amount': self._to_decimal(row['net_mf_amount'], 10000),
                    'ingest_time': datetime.now(UTC)
                }
                
                processed_data.append(record)
            
            if skipped_rows > 0:
                logger.warning(f"  资金流向数据质量检查完成: 跳过了 {skipped_rows} 行不完整或异常的数据")
            
            return processed_data
            
        except Exception as e:
            logger.error(f"处理资金流向数据时发生错误: {str(e)}")
            return []