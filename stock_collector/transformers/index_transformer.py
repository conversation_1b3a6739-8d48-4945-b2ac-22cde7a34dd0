"""
指数数据转换器
处理指数数据的格式转换和清洗，继承市场数据转换器
"""

import os
from .market_transformer import MarketTransformer


class IndexTransformer(MarketTransformer):
    """指数数据转换器，继承市场数据转换器"""
    
    def get_data_file_path(self, ts_code: str, **kwargs) -> str:
        """获取指数数据文件路径"""
        target_dir = os.path.join(self.data_dir, "index", "index_daily")
        filename = f"{ts_code.replace('.', '_')}.csv"
        return os.path.join(target_dir, filename)
    
    def get_data_description(self) -> str:
        return "指数数据"
    
    def process_stock_data(self, ts_code: str, **kwargs) -> list:
        """
        处理单个指数的数据
        
        Args:
            ts_code: 指数代码
            **kwargs: 额外参数
            
        Returns:
            处理后的数据列表
        """
        # 指数数据处理，设置data_type='index'
        kwargs['data_type'] = 'index'
        return super().process_stock_data(ts_code, **kwargs)