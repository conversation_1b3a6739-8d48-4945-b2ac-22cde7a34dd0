"""
筹码数据转换器
处理股票筹码数据的格式转换和清洗
"""

import os
from typing import List, Dict, Any
from datetime import datetime, UTC
import pandas as pd
from ..core.base_transformer import BaseTransformer
from app.server.core.log import logger


class ChipTransformer(BaseTransformer):
    """筹码数据转换器"""
    
    def get_data_file_path(self, ts_code: str, **kwargs) -> str:
        """获取筹码数据文件路径"""
        target_dir = os.path.join(self.data_dir, "special", "cyq_perf")
        filename = f"{ts_code.replace('.', '_')}.csv"
        return os.path.join(target_dir, filename)
    
    def get_data_description(self) -> str:
        return "筹码数据"
    
    def transform_data(self, df: pd.DataFrame, **kwargs) -> List[Dict[str, Any]]:
        """
        转换筹码数据格式以适配ClickHouse表结构
        
        Args:
            df: 筹码数据DataFrame
            **kwargs: 额外参数
            
        Returns:
            转换后的数据列表
        """
        try:
            processed_data = []
            skipped_rows = 0
            
            for _, row in df.iterrows():
                # 跳过缺失关键数据的行
                if pd.isna(row['ts_code']) or pd.isna(row['trade_date']):
                    skipped_rows += 1
                    continue
                
                # 检查价格数据合理性
                price_fields = ['his_low', 'his_high', 'cost_5pct', 'cost_15pct', 'cost_50pct', 
                               'cost_85pct', 'cost_95pct', 'weight_avg']
                invalid_prices = {}
                for field in price_fields:
                    if not pd.isna(row[field]) and row[field] <= 0:
                        invalid_prices[field] = row[field]
                
                if invalid_prices:
                    logger.warning(f"  跳过 {row['ts_code']} {row['trade_date']}: 价格数据异常 {invalid_prices}")
                    skipped_rows += 1
                    continue
                
                # 检查价格数据精度范围
                max_price = 99999999.9999
                oversized_prices = {}
                for field in price_fields:
                    if not pd.isna(row[field]) and row[field] > max_price:
                        oversized_prices[field] = row[field]
                
                if oversized_prices:
                    logger.warning(f"  跳过 {row['ts_code']} {row['trade_date']}: 价格数据超出精度范围 {oversized_prices}")
                    skipped_rows += 1
                    continue
                
                # 检查胜率范围 (0-100)
                if not pd.isna(row['winner_rate']) and (row['winner_rate'] < 0 or row['winner_rate'] > 100):
                    logger.warning(f"  跳过 {row['ts_code']} {row['trade_date']}: 胜率超出合理范围 {row['winner_rate']}")
                    skipped_rows += 1
                    continue
                
                # 检查价格逻辑：his_low <= his_high
                if not pd.isna(row['his_low']) and not pd.isna(row['his_high']) and row['his_low'] > row['his_high']:
                    logger.warning(f"  跳过 {row['ts_code']} {row['trade_date']}: 历史最低价大于最高价")
                    skipped_rows += 1
                    continue
                
                # 处理交易日期格式
                trade_date = self._parse_date(row['trade_date'])
                
                # 构建数据记录
                record = {
                    'instrument_id': row['ts_code'],
                    'trade_date': trade_date,
                    # 历史价格区间：Decimal32(4) - 保留4位小数
                    'his_low': self._to_decimal(row['his_low'], precision=4),
                    'his_high': self._to_decimal(row['his_high'], precision=4),
                    # 筹码成本价：Decimal32(4) - 保留4位小数
                    'cost_5pct': self._to_decimal(row['cost_5pct'], precision=4),
                    'cost_15pct': self._to_decimal(row['cost_15pct'], precision=4),
                    'cost_50pct': self._to_decimal(row['cost_50pct'], precision=4),
                    'cost_85pct': self._to_decimal(row['cost_85pct'], precision=4),
                    'cost_95pct': self._to_decimal(row['cost_95pct'], precision=4),
                    # 平均成本：Decimal32(4) - 保留4位小数
                    'weight_avg': self._to_decimal(row['weight_avg'], precision=4),
                    # 胜率：Decimal32(2) - 保留2位小数
                    'winner_rate': self._to_decimal(row['winner_rate'], precision=2),
                    'ingest_time': datetime.now(UTC)
                }
                
                processed_data.append(record)
            
            if skipped_rows > 0:
                logger.warning(f"  筹码数据质量检查完成: 跳过了 {skipped_rows} 行不完整或异常的数据")
            
            return processed_data
            
        except Exception as e:
            logger.error(f"处理筹码数据时发生错误: {str(e)}")
            return []