"""
行业数据转换器
处理股票行业数据的格式转换和清洗
"""

import os
from typing import List, Dict, Any
from datetime import datetime, UTC
import pandas as pd
from ..core.base_transformer import BaseTransformer
from app.server.core.log import logger


class IndustryTransformer(BaseTransformer):
    """行业数据转换器"""
    
    def get_data_file_path(self, ts_code: str, **kwargs) -> str:
        """获取行业数据文件路径"""
        target_dir = os.path.join(self.data_dir, "index", "index_member_all")
        filename = f"{ts_code.replace('.', '_')}.csv"
        return os.path.join(target_dir, filename)
    
    def get_data_description(self) -> str:
        return "申万行业成分数据"
    
    def transform_data(self, df: pd.DataFrame, **kwargs) -> List[Dict[str, Any]]:
        """
        转换行业数据格式以适配ClickHouse表结构
        
        Args:
            df: 行业数据DataFrame
            **kwargs: 额外参数
            
        Returns:
            转换后的数据列表
        """
        try:
            processed_data = []
            skipped_rows = 0
            
            for _, row in df.iterrows():
                # 跳过缺失关键数据的行
                if pd.isna(row['ts_code']) or pd.isna(row['name']):
                    skipped_rows += 1
                    continue
                
                # 检查纳入日期格式
                if pd.isna(row['in_date']):
                    logger.warning(f"  跳过 {row['ts_code']}: 纳入日期缺失")
                    skipped_rows += 1
                    continue
                
                # 处理日期格式
                in_date = self._parse_date(row['in_date'])
                
                out_date = None
                if not pd.isna(row['out_date']):
                    out_date = self._parse_date(row['out_date'])
                
                # 处理是否最新标志
                is_new = None
                if not pd.isna(row['is_new']):
                    is_new = 1 if str(row['is_new']).upper() == 'Y' else 0
                
                # 构建数据记录
                record = {
                    'instrument_id': row['ts_code'],
                    'name': str(row['name']),
                    # 行业分类维度（可为空）
                    'l1_code': str(row['l1_code']) if not pd.isna(row['l1_code']) else None,
                    'l1_name': str(row['l1_name']) if not pd.isna(row['l1_name']) else None,
                    'l2_code': str(row['l2_code']) if not pd.isna(row['l2_code']) else None,
                    'l2_name': str(row['l2_name']) if not pd.isna(row['l2_name']) else None,
                    'l3_code': str(row['l3_code']) if not pd.isna(row['l3_code']) else None,
                    'l3_name': str(row['l3_name']) if not pd.isna(row['l3_name']) else None,
                    # 关系生命周期
                    'in_date': in_date,
                    'out_date': out_date,
                    # 状态标志
                    'is_new': is_new,
                    'ingest_time': datetime.now(UTC)
                }
                
                processed_data.append(record)
            
            if skipped_rows > 0:
                logger.warning(f"  行业成分数据质量检查完成: 跳过了 {skipped_rows} 行不完整或异常的数据")
            
            return processed_data
            
        except Exception as e:
            logger.error(f"处理行业成分数据时发生错误: {str(e)}")
            return []