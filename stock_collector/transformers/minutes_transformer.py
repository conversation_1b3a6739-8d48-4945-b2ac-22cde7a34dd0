"""
分钟线数据转换器
处理BaoStock分钟线数据的格式转换和清洗
"""

import os
from typing import List, Dict, Any
from datetime import datetime, UTC
import pickle

import pandas as pd
from ..core.base_transformer import BaseTransformer
from app.server.core.log import logger


class MinutesTransformer(BaseTransformer):
    """分钟线数据转换器"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.frequency = config.get('frequency', '5')  # 默认5分钟
    
    def get_data_file_path(self, ts_code: str, **kwargs) -> str:
        """获取分钟线数据文件路径"""
        frequency = kwargs.get('frequency', self.frequency)
        target_dir = os.path.join(self.data_dir, "market", "stk_mins", f"{frequency}min")
        filename = f"{ts_code.replace('.', '_')}.csv"
        return os.path.join(target_dir, filename)
    
    def get_data_description(self) -> str:
        """获取数据描述"""
        return f"{self.frequency}分钟线数据"
    
    def transform_data(self, df: pd.DataFrame, **kwargs) -> List[Dict[str, Any]]:
        """
        转换分钟线数据格式以适配ClickHouse表结构
        
        Args:
            df: 分钟线数据DataFrame
            **kwargs: 额外参数
            
        Returns:
            转换后的数据列表
        """
        if df.empty:
            return []
        
        try:
            
            # 创建结果列表
            result_data = []
            
            for _, row in df.iterrows():
                try:
                    # 解析股票代码
                    bourse, ts_code = row['code'].split('.')
                    if bourse == 'sh':
                        ts_code =  ts_code + '.SH'
                    elif bourse == 'sz':
                        ts_code =  ts_code + '.SZ'
                    else:
                        continue

                    # 解析日期和时间
                    date_str = str(row['date'])
                    time_str = str(row['time'])
                    
                    # 组合日期时间字符串
                    datetime_str = time_str[:8] + ' ' + time_str[8:10] + ':' + time_str[10:12] + ':' + time_str[12:14]

                    # 转换为datetime对象
                    trade_time = datetime.strptime(datetime_str, '%Y%m%d %H:%M:%S')
                    
                    # 提取交易日期
                    trade_date = trade_time.date()
                    
                    # 构建数据记录
                    record = {
                        'instrument_id': ts_code,
                        'trade_time': trade_time,
                        'trade_date': trade_date,
                        'open': self._to_decimal(row['open'], precision=4),
                        'high': self._to_decimal(row['high'], precision=4),
                        'low': self._to_decimal(row['low'], precision=4),
                        'close': self._to_decimal(row['close'], precision=4),
                        'volume': self._to_int(row['volume']),
                        'amount': self._to_decimal(row['amount'], precision=4),
                    }
                    
                    # 数据验证
                    if self._validate_record(record):
                        result_data.append(record)
                    else:
                        logger.warning(f"处理无效记录: {record}")
                        for field in ['open', 'high', 'low', 'close', 'volume', 'amount']:
                            record[field] = None
                        result_data.append(record)
                except Exception as e:
                    logger.error(f"转换单条记录时发生错误: {str(e)}, 原始数据: {row.to_dict()}")
                    continue
            
            logger.info(f"成功转换 {len(result_data)} 条{self.frequency}分钟线记录")
            return result_data
            
        except Exception as e:
            logger.error(f"转换{self.frequency}分钟线数据时发生错误: {str(e)}")
            return []
    
    def _validate_record(self, record: Dict[str, Any]) -> bool:
        """
        验证记录的有效性
        
        Args:
            record: 数据记录
            
        Returns:
            是否有效
        """
        # 检查必需字段
        required_fields = ['instrument_id', 'trade_time', 'trade_date']
        for field in required_fields:
            if field not in record or not record[field]:
                raise ValueError(f"记录中缺少必需字段: {field}")
        
        # 检查价格数据的合理性
        prices = [record['open'], record['high'], record['low'], record['close']]
        if any(not pd.isna(price) and price <= 0 for price in prices):
            return False
        
        # 检查高低价关系
        if record['high'] < record['low']:
            return False
        
        # 检查开盘价、收盘价是否在高低价范围内
        if not (record['low'] <= record['open'] <= record['high']):
            return False
        
        if not (record['low'] <= record['close'] <= record['high']):
            return False
        
        # 检查成交量和成交额
        if record['volume'] < 0 or record['amount'] < 0:
            return False
        
        return True
    
    def process_stock_data(self, ts_code: str, **kwargs) -> List[Dict[str, Any]]:
        """
        处理单个股票的分钟线数据
        
        Args:
            ts_code: 股票代码
            **kwargs: 额外参数
            
        Returns:
            处理后的数据列表
        """
        try:
            # 读取数据文件
            data_file = self.get_data_file_path(ts_code, **kwargs)
            if not os.path.exists(data_file):
                logger.error(f"✗ 未找到股票 {ts_code} 的{self.get_data_description()}文件: {data_file}")
                return []
            
            df = pd.read_csv(data_file)
            
            if df.empty:
                logger.warning(f"✗ 股票 {ts_code} 的{self.get_data_description()}文件为空")
                return []
            
            # 转换数据格式
            processed_data = self.transform_data(df, **kwargs)
            
            return processed_data
            
        except Exception as e:
            logger.error(f"处理股票 {ts_code} 的{self.get_data_description()}时发生错误: {str(e)}")
            return []
    
    def process_stock_data_and_save(self, ts_code: str, output_dir: str) -> None:
        """
        处理单个股票的分钟线数据并保存到pkl文件
        """
        processed_data = self.process_stock_data(ts_code)
        output_file = os.path.join(output_dir, f"{ts_code}.pkl")
        if os.path.exists(output_file):
            return
        if processed_data:
            # 保存到pkl文件
            with open(output_file, 'wb') as f:
                pickle.dump(processed_data, f)
            logger.info(f"✓ 股票 {ts_code} 的{self.get_data_description()}已完成")
