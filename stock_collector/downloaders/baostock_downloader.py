"""
BaoStock数据下载器实现
支持5分钟线等分钟级数据下载
"""

import os
import time
from datetime import datetime
from typing import Optional
import baostock as bs
import pandas as pd
from ..core.base_downloader import BaseDownloader
from app.server.core.log import logger


class BaoStockMinutesDownloader(BaseDownloader):
    """BaoStock分钟线数据下载器"""
    
    def __init__(self, config):
        super().__init__(config)
        self.is_logged_in = False
        self.frequency = config.get('frequency', '5')  # 默认5分钟
        self.last_used_time = None

    def _ensure_login(self):
        """确保已登录BaoStock"""
        lg = None
        if not self.is_logged_in:
            lg = bs.login()
        elif self.last_used_time is not None and time.time() - self.last_used_time > 300:
            bs.logout()
            lg = bs.login()
        if lg is not None:
            if lg.error_code != '0':
                raise RuntimeError(f"BaoStock登录失败: {lg.error_msg}")
            self.is_logged_in = True
            logger.info("BaoStock登录成功")
    
    def _convert_code_to_baostock_format(self, ts_code: str) -> str:
        """
        将股票代码从tushare格式转换为baostock格式
        600000.SH -> sh.600000
        000001.SZ -> sz.000001
        """
        if '.' not in ts_code:
            return ts_code
        
        code, exchange = ts_code.split('.')
        if exchange.upper() == 'SH':
            return f"sh.{code}"
        elif exchange.upper() == 'SZ':
            return f"sz.{code}"
        else:
            return ts_code
    
    def download_data(
        self, 
        ts_code: str = '', 
        start_date: str = '', 
        end_date: str = '', 
        frequency: str = None,
        **kwargs
    ) -> pd.DataFrame:
        """
        下载分钟线数据
        
        Args:
            ts_code: 股票代码 (tushare格式，如600000.SH)
            start_date: 开始日期 (YYYY-MM-DD格式)
            end_date: 结束日期 (YYYY-MM-DD格式)
            frequency: 频率，支持5、15、30、60分钟
            **kwargs: 额外参数
            
        Returns:
            下载的数据DataFrame
        """
        try:
            self._ensure_login()
            
            # 转换股票代码格式
            baostock_code = self._convert_code_to_baostock_format(ts_code)
            
            # 使用传入的频率或默认频率
            freq = frequency or self.frequency

            # 如果日期格式为YYYYMMDD，则转换为YYYY-MM-DD
            if '-' not in start_date:
                start_date = datetime.strptime(start_date, '%Y%m%d').strftime('%Y-%m-%d')
            if '-' not in end_date:
                end_date = datetime.strptime(end_date, '%Y%m%d').strftime('%Y-%m-%d')
            
            # 调用baostock接口
            rs = bs.query_history_k_data_plus(
                baostock_code,
                "date,time,code,open,high,low,close,volume,amount,adjustflag",
                start_date=start_date,
                end_date=end_date,
                frequency=freq,
                adjustflag="3"  # 不复权
            )
            
            logger.info(f'BaoStock查询响应 - error_code: {rs.error_code}, error_msg: {rs.error_msg}')
            
            if rs.error_code != '0':
                logger.error(f"BaoStock查询失败: {rs.error_msg}")
                return pd.DataFrame()
            
            # 收集数据
            data_list = []
            while rs.next():
                data_list.append(rs.get_row_data())
            
            if not data_list:
                logger.warning(f"股票 {ts_code} 在 {start_date} 到 {end_date} 期间没有{freq}分钟线数据")
                return pd.DataFrame()
            
            # 创建DataFrame
            df = pd.DataFrame(data_list, columns=rs.fields)
            
            logger.info(f"成功获取股票 {ts_code} 的{freq}分钟线数据，共 {len(df)} 条记录")
            self.last_used_time = time.time()
            return df
            
        except Exception as e:
            logger.error(f"下载股票 {ts_code} 的分钟线数据时发生错误: {str(e)}")
            return pd.DataFrame()
    
    # def _clean_data(self, df: pd.DataFrame, ts_code: str) -> pd.DataFrame:
    #     """
    #     清洗数据
        
    #     Args:
    #         df: 原始数据DataFrame
    #         ts_code: 股票代码
            
    #     Returns:
    #         清洗后的DataFrame
    #     """
    #     if df.empty:
    #         return df
        
    #     # 过滤掉空值行
    #     df = df.dropna(subset=['date', 'time', 'open', 'high', 'low', 'close'])
        
    #     # 转换数据类型
    #     numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'amount']
    #     for col in numeric_columns:
    #         df[col] = pd.to_numeric(df[col], errors='coerce')
        
    #     # 过滤掉价格为0或负数的异常数据
    #     df = df[
    #         (df['open'] > 0) & 
    #         (df['high'] > 0) & 
    #         (df['low'] > 0) & 
    #         (df['close'] > 0) &
    #         (df['volume'] >= 0) &
    #         (df['amount'] >= 0)
    #     ]
        
    #     # 确保股票代码格式正确
    #     df['original_code'] = ts_code
        
    #     return df
    
    def get_file_path(self, ts_code: str, **kwargs) -> str:
        """
        获取分钟线数据文件路径
        
        Args:
            ts_code: 股票代码
            **kwargs: 额外参数
            
        Returns:
            文件保存路径
        """
        frequency = kwargs.get('frequency', self.frequency)
        target_dir = os.path.join(self.data_dir, "market", "stk_mins", f"{frequency}min")
        filename = f"{ts_code.replace('.', '_')}.csv"
        return os.path.join(target_dir, filename)
    
    def get_data_description(self) -> str:
        """获取数据描述"""
        return f"{self.frequency}分钟线数据"
    
    def __del__(self):
        """析构函数，确保登出BaoStock"""
        if self.is_logged_in:
            try:
                bs.logout()
                logger.info("BaoStock已登出")
            except:
                pass 