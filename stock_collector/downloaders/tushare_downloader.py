"""
Tushare数据下载器实现
包含各种股票数据类型的下载器
"""

import os
from typing import Literal, List
import json
from datetime import datetime, timedelta
from pathlib import Path


import tushare as ts
import pandas as pd
from ..core.base_downloader import BaseDownloader
from app.server.core.log import logger


class TushareMarketDownloader(BaseDownloader):
    """Tushare市场数据下载器"""
    
    def __init__(self, config):
        super().__init__(config)
        ts.set_token(self.token)
        self.pro = ts.pro_api(self.token)
    
    def download_data(
        self, 
        ts_code: str = '', 
        start_date: str = '', 
        end_date: str = '', 
        adj: Literal['qfq', 'hfq', None] = None, 
        freq: str = 'D', 
        ma: list = [],
        **kwargs
    ) -> pd.DataFrame:
        """下载市场数据"""
        
        return ts.pro_bar(
            ts_code=ts_code,
            start_date=start_date,
            end_date=end_date,
            asset='E',  # 股票
            adj=adj,
            freq=freq,
            ma=ma
        )
    
    def download_daily_data(self, trade_date: str = '') -> pd.DataFrame:
        """下载日行情数据"""
        return self.pro.daily(trade_date=trade_date)
    
    def get_file_path(self, ts_code: str, **kwargs) -> str:
        """获取市场数据文件路径"""
        adj = kwargs.get('adj', None)
        freq = kwargs.get('freq', 'D')
        
        adj_dir = adj if adj else "no_adj"
        target_dir = os.path.join(self.data_dir, "market", adj_dir, freq)
        filename = f"{ts_code.replace('.', '_')}.csv"
        return os.path.join(target_dir, filename)
    
    def get_data_description(self) -> str:
        return "行情数据"


class TushareAdjFactorDownloader(BaseDownloader):
    """Tushare复权因子下载器"""
    
    def __init__(self, config):
        super().__init__(config)
        ts.set_token(self.token)
        self.pro = ts.pro_api(self.token)
    
    def download_data(
        self, 
        ts_code: str = '', 
        start_date: str = '', 
        end_date: str = '', 
        trade_date: str = '',
        **kwargs
    ) -> pd.DataFrame:
        """下载复权因子数据"""
        
        return self.pro.adj_factor(
            ts_code=ts_code,
            start_date=start_date,
            end_date=end_date,
            trade_date=trade_date
        )
    
    def get_file_path(self, ts_code: str, **kwargs) -> str:
        """获取复权因子文件路径"""
        target_dir = os.path.join(self.data_dir, "market", "adj_factor")
        filename = f"{ts_code.replace('.', '_')}.csv"
        return os.path.join(target_dir, filename)
    
    def get_data_description(self) -> str:
        return "复权因子数据"


class TushareCyqPerfDownloader(BaseDownloader):
    """Tushare筹码数据下载器"""
    
    def __init__(self, config):
        super().__init__(config)
        ts.set_token(self.token)
        self.pro = ts.pro_api(self.token)
    
    def download_data(
        self, 
        ts_code: str = '', 
        start_date: str = '', 
        end_date: str = '', 
        trade_date: str = '',
        **kwargs
    ) -> pd.DataFrame:
        """下载筹码数据"""
        
        return self.pro.cyq_perf(
            ts_code=ts_code,
            start_date=start_date,
            end_date=end_date,
            trade_date=trade_date
        )
    
    def get_file_path(self, ts_code: str, **kwargs) -> str:
        """获取筹码数据文件路径"""
        target_dir = os.path.join(self.data_dir, "special", "cyq_perf")
        filename = f"{ts_code.replace('.', '_')}.csv"
        return os.path.join(target_dir, filename)
    
    def get_data_description(self) -> str:
        return "筹码成本及其胜率数据"


class TushareMoneyflowDownloader(BaseDownloader):
    """Tushare资金流向数据下载器"""
    
    def __init__(self, config):
        super().__init__(config)
        ts.set_token(self.token)
        self.pro = ts.pro_api(self.token)
    
    def download_data(
        self, 
        ts_code: str = '', 
        start_date: str = '', 
        end_date: str = '', 
        trade_date: str = '',
        **kwargs
    ) -> pd.DataFrame:
        """下载资金流向数据"""
        
        return self.pro.moneyflow(
            ts_code=ts_code,
            start_date=start_date,
            end_date=end_date,
            trade_date=trade_date
        )
    
    def get_file_path(self, ts_code: str, **kwargs) -> str:
        """获取资金流向数据文件路径"""
        target_dir = os.path.join(self.data_dir, "moneyflow", "moneyflow")
        filename = f"{ts_code.replace('.', '_')}.csv"
        return os.path.join(target_dir, filename)
    
    def get_data_description(self) -> str:
        return "资金流向数据"


class TushareIndustryDownloader(BaseDownloader):
    """Tushare行业数据下载器"""
    
    def __init__(self, config):
        super().__init__(config)
        ts.set_token(self.token)
        self.pro = ts.pro_api(self.token)
    
    def download_data(self, ts_code: str = '', **kwargs) -> pd.DataFrame:
        """下载行业数据"""
        return self.pro.index_member_all(
            ts_code=ts_code,
            is_new='Y'  # 默认获取最新数据
        )
    
    def get_file_path(self, ts_code: str, **kwargs) -> str:
        """获取行业数据文件路径"""
        target_dir = os.path.join(self.data_dir, "index", "index_member_all")
        filename = f"{ts_code.replace('.', '_')}.csv"
        return os.path.join(target_dir, filename)
    
    def get_data_description(self) -> str:
        return "申万行业成分数据"


class TushareIndexDownloader(BaseDownloader):
    """Tushare指数数据下载器"""
    
    def __init__(self, config):
        super().__init__(config)
        ts.set_token(self.token)
        self.pro = ts.pro_api(self.token)
    
    def download_data(
        self, 
        ts_code: str = '', 
        start_date: str = '', 
        end_date: str = '', 
        trade_date: str = '',
        **kwargs
    ) -> pd.DataFrame:
        """下载指数数据"""
        
        return self.pro.index_daily(
            ts_code=ts_code,
            start_date=start_date,
            end_date=end_date,
            trade_date=trade_date
        )
    
    def get_file_path(self, ts_code: str, **kwargs) -> str:
        """获取指数数据文件路径"""
        target_dir = os.path.join(self.data_dir, "index", "index_daily")
        filename = f"{ts_code.replace('.', '_')}.csv"
        return os.path.join(target_dir, filename)
    
    def get_data_description(self) -> str:
        return "指数行情数据"


class TushareSectorDownloader(BaseDownloader):
    """Tushare板块数据下载器"""
    
    def __init__(self, config):
        super().__init__(config)
        ts.set_token(self.token)
        self.pro = ts.pro_api(self.token)
    
    def download_data(
        self, 
        ts_code: str = '', 
        start_date: str = '', 
        end_date: str = '', 
        trade_date: str = '',
        **kwargs
    ) -> pd.DataFrame:
        """下载板块数据"""
        
        return self.pro.ths_daily(
            ts_code=ts_code,
            start_date=start_date,
            end_date=end_date,
            trade_date=trade_date,
            fields='ts_code,trade_date,open,high,low,close,pre_close,avg_price,change,pct_change,vol,turnover_rate,total_mv,float_mv'
        )
    
    def get_all_sector_codes(self, stock_codes_path: str, trade_date: str = None) -> List[str]:
        """
        获取所有板块代码列表
        
        Args:
            trade_date: 指定交易日期，如果为None则使用最近交易日
            
        Returns:
            板块代码列表
        """
        # 确保缓存目录存在
        cache_dir = os.path.dirname(stock_codes_path)
        os.makedirs(cache_dir, exist_ok=True)
        
        # 检查缓存文件是否存在且较新
        if os.path.exists(stock_codes_path):
            # 检查文件修改时间，如果是最近7天内的就使用缓存
            file_mod_time = datetime.fromtimestamp(os.path.getmtime(stock_codes_path))
            if datetime.now() - file_mod_time < timedelta(days=7):
                with open(stock_codes_path, 'r', encoding='utf-8') as f:
                    cached_data = json.load(f)
                    return cached_data.get('sector_codes', [])
        
        # 如果没有指定交易日期，使用最近的工作日
        if not trade_date:
            today = datetime.now()
            # 往前推几天找工作日
            for i in range(10):
                check_date = today - timedelta(days=i)
                if check_date.weekday() < 5:  # 周一到周五
                    trade_date = check_date.strftime('%Y%m%d')
                    break
        
        # 获取指定日期的所有板块数据
        logger.info(f"获取 {trade_date} 的所有板块代码...")
        df = self.pro.ths_daily(trade_date=trade_date)
        
        if df.empty:
            raise Exception(f"未获取到 {trade_date} 的板块数据")
        
        # 提取唯一的板块代码
        sector_codes = df['ts_code'].unique().tolist()
        logger.info(f"获取到 {len(sector_codes)} 个板块代码")
        
        # 保存到缓存文件
        cache_data = {
            'update_date': trade_date,
            'stock_codes': sector_codes,
            'total_count': len(sector_codes)
        }
        
        with open(stock_codes_path, 'w', encoding='utf-8') as f:
            json.dump(cache_data, f, ensure_ascii=False, indent=2)
            
        logger.info(f"板块代码列表已缓存到: {stock_codes_path}")
        
        return sector_codes
    
    def get_file_path(self, ts_code: str, **kwargs) -> str:
        """获取板块数据文件路径"""
        target_dir = os.path.join(self.data_dir, "sector", "sector_daily")
        filename = f"{ts_code.replace('.', '_')}.csv"
        return os.path.join(target_dir, filename)
    
    def get_data_description(self) -> str:
        return "板块行情数据"