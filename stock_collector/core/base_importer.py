"""
基础导入器抽象类
定义数据导入的通用接口
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any
from app.server.core.log import logger


class BaseImporter(ABC):
    """
    基础导入器抽象类
    定义数据导入的通用接口
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化导入器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.db_config = config.get('db_config') if config else None

    
    @abstractmethod
    def insert_data(self, data: List[Dict[str, Any]], table_name: str) -> bool:
        """
        插入数据的抽象方法
        
        Args:
            data: 要插入的数据列表
            table_name: 表名
            
        Returns:
            是否成功
        """
        pass
    
    def batch_insert(self, data: List[Dict[str, Any]], table_name: str, batch_size: int) -> int:
        """
        批量插入数据
        
        Args:
            data: 要插入的数据列表
            table_name: 表名
            batch_size: 批量大小
            
        Returns:
            成功插入的记录数
        """
        try:
            total_inserted = 0
            total_batches = len(data) // batch_size + (1 if len(data) % batch_size > 0 else 0)
            
            for i in range(0, len(data), batch_size):
                batch_data = data[i:i + batch_size]
                batch_num = i // batch_size + 1
                
                success = self.insert_data(batch_data, table_name)
                if success:
                    total_inserted += len(batch_data)
                    logger.info(f"  批次 {batch_num}/{total_batches}: 成功插入 {len(batch_data)} 条记录")
                else:
                    logger.error(f"  批次 {batch_num}/{total_batches}: 插入失败")
            
            return total_inserted
            
        except Exception as e:
            logger.error(f"批量插入数据时发生错误: {str(e)}")
            return 0