"""
基础转换器抽象类
定义数据转换的通用接口
"""

import os
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from datetime import datetime, UTC
import pandas as pd
from app.server.core.log import logger


class BaseTransformer(ABC):
    """
    基础转换器抽象类
    定义数据转换的通用接口
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化转换器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.data_dir = config.get('data_dir', None)
    
    @abstractmethod
    def get_data_file_path(self, ts_code: str, **kwargs) -> str:
        """
        获取数据文件路径
        
        Args:
            ts_code: 股票代码
            **kwargs: 额外参数
            
        Returns:
            数据文件路径
        """
        pass
    
    @abstractmethod
    def transform_data(self, df: pd.DataFrame, **kwargs) -> List[Dict[str, Any]]:
        """
        转换数据格式
        
        Args:
            df: 原始数据DataFrame
            **kwargs: 额外参数
            
        Returns:
            转换后的数据列表
        """
        pass
    
    @abstractmethod
    def get_data_description(self) -> str:
        """获取数据描述，用于日志显示"""
        pass
    
    def process_stock_data(self, ts_code: str, **kwargs) -> List[Dict[str, Any]]:
        """
        处理单个股票的数据
        
        Args:
            ts_code: 股票代码
            **kwargs: 额外参数
            
        Returns:
            处理后的数据列表
        """
        try:
            # 获取数据文件路径
            data_file = self.get_data_file_path(ts_code, **kwargs)
            
            if not os.path.exists(data_file):
                logger.error(f"✗ 未找到股票 {ts_code} 的{self.get_data_description()}文件: {data_file}")
                return []
            
            # 读取数据
            df = pd.read_csv(data_file)
            
            if df.empty:
                logger.warning(f"✗ 股票 {ts_code} {self.get_data_description()}为空")
                return []
            
            # 转换数据格式
            processed_data = self.transform_data(df, **kwargs)
            
            return processed_data
            
        except Exception as e:
            logger.error(f"处理股票 {ts_code} {self.get_data_description()}时发生错误: {str(e)}")
            return []
    
    def _to_int(self, value: float, multiplier: int = None) -> Optional[int]:
        """
        将浮点数转换为整数，支持单位转换
        """
        if pd.isna(value):
            return None
        if multiplier is None:
            return int(value)
        else:
            return int(value * multiplier)
    
    def _to_decimal(self, value: float, multiplier: float = None, precision: int = 2) -> Optional[float]:
        """
        将浮点数转换为指定精度的小数，支持单位转换
        """
        if pd.isna(value):
            return None
        if multiplier is None:
            return round(float(value), precision)
        else:
            return round(float(value * multiplier), precision)
    
    def _parse_date(self, date_value) -> datetime:
        """
        解析日期格式
        """
        if pd.isna(date_value):
            return None
        if isinstance(date_value, pd.Timestamp):
            return date_value.date()
            
        date_str = str(int(date_value))
        return datetime.strptime(date_str, '%Y%m%d').date()
    
    def _is_valid_row(self, row: pd.Series, required_fields: List[str]) -> bool:
        """
        检查数据行是否有效
        
        Args:
            row: 数据行
            required_fields: 必需字段列表
            
        Returns:
            是否有效
        """
        # 检查必需字段是否缺失
        for field in required_fields:
            if pd.isna(row.get(field)):
                return False
        
        return True