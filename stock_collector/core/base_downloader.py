"""
基础下载器抽象类
定义数据下载的通用接口
"""

import os
from abc import ABC, abstractmethod
from typing import Any, Dict
import pandas as pd
from app.server.core.log import logger


class BaseDownloader(ABC):
    """
    基础下载器抽象类
    定义数据下载的通用接口
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化下载器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.token = config.get('token')
        if not self.token:
            raise ValueError("token is None")
        self.data_dir = config.get('data_dir', None)
        
        # 确保数据目录存在
        if self.data_dir is not None:
            os.makedirs(self.data_dir, exist_ok=True)
    
    @abstractmethod
    def download_data(self, ts_code: str, start_date: str = '', end_date: str = '', **kwargs) -> pd.DataFrame:
        """
        下载数据的抽象方法
        
        Args:
            ts_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            **kwargs: 额外参数
            
        Returns:
            下载的数据DataFrame
        """
        pass
    
    @abstractmethod
    def get_file_path(self, ts_code: str, **kwargs) -> str:
        """
        获取文件保存路径
        
        Args:
            ts_code: 股票代码
            **kwargs: 额外参数
            
        Returns:
            文件保存路径
        """
        pass
    
    @abstractmethod
    def get_data_description(self) -> str:
        """获取数据描述，用于日志显示"""
        pass
    
    def download_and_save(self, 
                         ts_code: str,
                         start_date: str = '',
                         end_date: str = '',
                         overwrite: bool = False,
                         **kwargs) -> bool:
        """
        下载数据并保存到CSV文件
        
        Args:
            ts_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            overwrite: 是否覆盖已存在的文件
            **kwargs: 额外参数
            
        Returns:
            bool: 成功返回True，失败返回False
        """
        try:
            # 获取文件路径
            filepath = self.get_file_path(ts_code, **kwargs)
            
            # 确保目录存在
            os.makedirs(os.path.dirname(filepath), exist_ok=True)
            
            # 检查文件是否已存在
            filename = os.path.basename(filepath)
            if os.path.exists(filepath) and not overwrite:
                logger.info(f"文件 {filename} 已存在，跳过下载。如需覆盖请设置 overwrite=True")
                return True
            
            # 下载数据
            df = self.download_data(
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date,
                **kwargs
            )
            
            if df is None or df.empty:
                logger.warning(f"未获取到股票 {ts_code} 的{self.get_data_description()}")
                return False
            
            # 保存到CSV文件
            df.to_csv(filepath, index=False, encoding='utf-8')
            logger.info(f"{self.get_data_description()}已保存到: {filepath}")
            logger.info(f"共获取 {len(df)} 条记录")
            
            return True
            
        except Exception as e:
            logger.error(f"下载和保存数据时发生错误: {str(e)}")
            return False