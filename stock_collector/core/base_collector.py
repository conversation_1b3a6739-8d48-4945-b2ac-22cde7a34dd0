"""
基础收集器类
定义通用的数据收集流程，使用组合模式管理下载器、转换器、导入器
"""

import json
import os
import time
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional

import pandas as pd
from tqdm import tqdm
from .base_downloader import BaseDownloader

from app.server.core.log import logger

class BaseCollector(ABC):
    """
    基础收集器抽象类
    定义通用的数据收集流程，子类需要实现具体的组件创建方法
    """
    
    def __init__(self, config: Dict[str, Any], db_client = None):
        """
        初始化收集器
        
        Args:
            config: 配置字典，包含token、数据目录、数据库配置等
        """
        self.config = config
        self.token = config.get('token')
        self.data_dir = config.get('data_dir', None)
        self.db_config = config.get('db_config')
        self.db_client = db_client
        
        # 创建组件实例
        self.downloader = self._create_downloader()
        self.transformer = self._create_transformer()

        # 这里如果self.db_client为None, 则importerh会使用创建一个db_client。
        # 如果self.db_client不为None, 则importer会使用db_client。
        self.importer = self._create_importer()

        if self.db_client is None:
            self.db_client = self.importer.db_client

    
    @abstractmethod
    def _create_downloader(self):
        """创建下载器实例，子类必须实现"""
        pass
    
    @abstractmethod
    def _create_transformer(self):
        """创建转换器实例，子类必须实现"""
        pass
    
    @abstractmethod
    def _create_importer(self):
        """创建导入器实例，子类必须实现"""
        pass
    
    @abstractmethod
    def get_data_type_name(self) -> str:
        """获取数据类型名称，用于日志显示"""
        pass
    
    @abstractmethod
    def get_table_name(self) -> str:
        """获取对应的数据库表名"""
        pass
    
    @abstractmethod
    def get_date_column_name(self) -> str:
        """获取日期列名，用于查询数据库最新日期"""
        pass
    
    @abstractmethod
    def daily_update(self, 
                    stock_codes: List[str], 
                    latest_date_in_db: str, 
                    end_date: str, 
                    calendar_df: pd.DataFrame) -> Dict[str, Any]:
        """
        每日更新数据的抽象方法，子类必须实现
        
        Args:
            stock_codes: 股票代码列表
            latest_date_in_db: 数据库中最新日期
            end_date: 结束日期
            calendar_df: 交易日历数据
            
        Returns:
            Dict[str, Any]: 更新结果统计
        """
        pass
    
    def get_latest_date_from_db(self) -> Optional[str]:
        """
        从数据库获取指定表的最新日期
        
        Returns:
            str: 最新日期，格式YYYYMMDD，如果表为空则返回None
        """
        try:
            if not self.importer or not hasattr(self.importer, 'client'):
                logger.error("错误: 未配置ClickHouse客户端")
                return None
                
            table_name = self.get_table_name()
            date_column = self.get_date_column_name()
            
            query = f"SELECT MAX({date_column}) as max_date FROM {table_name}"
            result = self.importer.client.execute(query)
            
            if result and len(result) > 0 and result[0]['max_date'] is not None:
                max_date = result[0]['max_date']
                if isinstance(max_date, str):
                    return max_date.replace('-', '')
                else:
                    return max_date.strftime('%Y%m%d')
            return None
        except Exception as e:
            logger.error(f"查询数据库最新日期失败: {str(e)}")
            return None
    
    def get_missing_trade_dates(self, latest_db_date: Optional[str], end_date: str, calendar_df: pd.DataFrame) -> List[str]:
        """
        获取需要更新的交易日期列表
        
        Args:
            latest_db_date: 数据库最新日期，格式YYYYMMDD
            end_date: 结束日期，格式YYYYMMDD  
            calendar_df: 交易日历数据
            
        Returns:
            List[str]: 需要更新的交易日期列表
        """
        if latest_db_date is None:
            raise ValueError("latest_db_date is None")
        
        # 筛选交易日
        trade_dates = calendar_df[calendar_df['is_open'] == 1]['cal_date'].astype(str).astype(int).astype(str).tolist()
        
         # 返回晚于latest_db_date且不晚于end_date的交易日
        missing_dates = [date for date in trade_dates if latest_db_date < date <= end_date]
        
        return sorted(missing_dates)
    
    def _filter_target_stocks(self, df: pd.DataFrame, stock_codes: List[str]) -> pd.DataFrame:
        """
        从DataFrame中筛选目标股票
        
        Args:
            df: 原始数据DataFrame
            stock_codes: 目标股票代码列表
            
        Returns:
            pd.DataFrame: 筛选后的数据
        """
        if df.empty:
            return df
        res = df[df['ts_code'].isin(stock_codes)]
        return res
    
    def _fetch_daily_data_batch(self, 
                                 trade_dates: List[str], 
                                 stock_codes: List[str], 
                                 download_func: callable,
                                 sleep_time: float = 0.3) -> pd.DataFrame:
        """
        通用的批量数据获取方法
        
        Args:
            trade_dates: 交易日期列表
            stock_codes: 股票代码列表
            download_func: 下载函数
            sleep_time: API调用间隔时间
            
        Returns:
            pd.DataFrame: 合并后的数据
        """
        all_data = []
        data_type = self.get_data_type_name()
        
        logger.info(f"开始获取 {len(trade_dates)} 个交易日的{data_type}...")
        
        for i, trade_date in enumerate(trade_dates, 1):
            logger.info(f"正在获取 {trade_date} 的{data_type} ({i}/{len(trade_dates)})")
            
            try:
                # 调用下载函数获取该日期的数据
                df = download_func(trade_date=trade_date)
                time.sleep(sleep_time)  # 避免API限制
                
                # 立即筛选目标股票
                if not df.empty:
                    filtered = self._filter_target_stocks(df, stock_codes)
                    if not filtered.empty:
                        all_data.append(filtered)
                        logger.info(f"  获取到 {len(filtered)} 只目标股票的{data_type}")
                        
            except Exception as e:
                logger.error(f"获取 {trade_date} {data_type}时发生错误: {str(e)}")
                continue
        
        # 合并所有数据
        result_df = pd.concat(all_data, ignore_index=True) if all_data else pd.DataFrame()
        
        logger.info(f"总计获取{data_type}: {len(result_df)} 条")
        return result_df
    
    def _get_prev_trade_date(self, trade_date: str, calendar_df: pd.DataFrame) -> Optional[str]:
        """
        获取指定交易日的前一个交易日
        
        Args:
            trade_date: 交易日期，格式YYYYMMDD
            calendar_df: 交易日历数据
            
        Returns:
            str: 前一个交易日，如果没有则返回None
        """
        trade_dates = calendar_df[calendar_df['is_open'] == 1]['cal_date'].astype(str).astype(int).astype(str).tolist()
        trade_dates = sorted(trade_dates)
        
        try:
            current_index = trade_dates.index(trade_date)
            if current_index > 0:
                return trade_dates[current_index - 1]
        except ValueError:
            pass
        
        return None
    
    def _get_fetch_dates_with_prev(self, missing_dates: List[str], calendar_df: pd.DataFrame) -> tuple[List[str], Optional[str]]:
        """
        获取需要拉取的日期列表（包含用于计算的前一日）
        
        Args:
            missing_dates: 缺失的交易日期列表
            calendar_df: 交易日历数据
            
        Returns:
            Tuple[List[str], Optional[str]]: (需要获取的日期列表, 需要排除入库的日期)
        """
        if not missing_dates:
            return [], None
        
        # 找到最早缺失日期的前一个交易日
        earliest_missing = min(missing_dates)
        prev_date = self._get_prev_trade_date(earliest_missing, calendar_df)
        
        # 构建完整的获取日期列表
        fetch_dates = [prev_date] + missing_dates if prev_date else missing_dates
        
        return fetch_dates, prev_date
    
    
    def collect_single_stock_data(self, 
                                 downloader: BaseDownloader,
                                 ts_code: str,
                                 start_date: str = '',
                                 end_date: str = '',
                                 trade_date: str = '',
                                 overwrite: bool = False,
                                 **kwargs) -> bool:
        """
        收集单个股票的数据
        
        Args:
            ts_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            overwrite: 是否覆盖已存在的文件
            **kwargs: 额外参数
            
        Returns:
            bool: 成功返回True，失败返回False
        """
        try:
            logger.info(f"正在获取股票 {ts_code} 的{self.get_data_type_name()}...")
            
            # 使用下载器下载数据
            success = downloader.download_and_save(
                ts_code=ts_code,
                start_date=start_date,
                end_date=end_date,
                trade_date=trade_date,
                overwrite=overwrite,
                **kwargs
            )
            
            if success:
                logger.info(f"✓ {ts_code} {self.get_data_type_name()}收集成功")
                return True
            else:
                logger.warning(f"✗ {ts_code} {self.get_data_type_name()}收集失败")
                return False
                
        except Exception as e:
            logger.error(f"✗ 获取股票 {ts_code} {self.get_data_type_name()}时发生错误: {str(e)}")
            return False
    
    def batch_collect_data(self,
                          downloader: BaseDownloader,
                          stock_codes_path: str,
                          start_date: str = '',
                          end_date: str = '',
                          trade_date: str = '',
                          overwrite: bool = False,
                          max_retries: int = 3,
                          **kwargs) -> Dict[str, bool]:
        """
        批量收集数据
        
        Args:
            stock_codes_path: 股票代码列表文件路径
            start_date: 开始日期
            end_date: 结束日期
            overwrite: 是否覆盖已存在的文件
            max_retries: 最大重试次数
            **kwargs: 额外参数
            
        Returns:
            每个股票代码的收集结果
        """
        stock_codes = self.load_stock_codes_from_json(stock_codes_path)
        results = {}
        total_count = len(stock_codes)
        
        logger.info(f"开始批量收集 {total_count} 个股票的{self.get_data_type_name()}...")
        if start_date and end_date:
            logger.info(f"日期范围: {start_date} 到 {end_date}")
        
        for i, ts_code in enumerate(stock_codes, 1):
            logger.info(f"\n进度: {i}/{total_count} - 正在处理股票: {ts_code}")
            
            retry_count = 0
            success = False
            
            while retry_count < max_retries and not success:
                try:
                    success = self.collect_single_stock_data(
                        downloader=downloader,
                        ts_code=ts_code,
                        start_date=start_date,
                        end_date=end_date,
                        trade_date=trade_date,
                        overwrite=overwrite,
                        **kwargs
                    )
                    
                    if success:
                        results[ts_code] = True
                    else:
                        retry_count += 1
                        if retry_count < max_retries:
                            logger.warning(f"✗ {ts_code} {self.get_data_type_name()}收集失败，正在重试 ({retry_count}/{max_retries})")
                        else:
                            results[ts_code] = False
                            logger.error(f"✗ {ts_code} {self.get_data_type_name()}收集失败，已达到最大重试次数")
                
                except Exception as e:
                    retry_count += 1
                    if retry_count < max_retries:
                        logger.warning(f"✗ {ts_code} {self.get_data_type_name()}收集发生异常，正在重试 ({retry_count}/{max_retries}): {str(e)}")
                    else:
                        results[ts_code] = False
                        logger.error(f"✗ {ts_code} {self.get_data_type_name()}收集发生异常，已达到最大重试次数: {str(e)}")
                
                time.sleep(0.2)
        
        # 统计结果
        success_count = sum(1 for success in results.values() if success)
        failed_count = total_count - success_count
        
        logger.info(f"\n批量{self.get_data_type_name()}收集完成:")
        logger.info(f"成功: {success_count} 个股票")
        logger.info(f"失败: {failed_count} 个股票")
        if failed_count > 0:
            failed_codes = [code for code, success in results.items() if not success]
            logger.warning(f"失败的股票代码: {failed_codes}")
        
        return results
    
    def process_and_import_to_clickhouse(self,
                                       stock_codes_path: str,
                                       table_name: str,
                                       batch_size: int = 1000,
                                       **kwargs) -> Dict[str, int]:
        """
        处理CSV文件并导入到ClickHouse数据库
        
        Args:
            stock_codes_path: 股票代码列表文件路径
            table_name: ClickHouse表名
            batch_size: 批量插入的大小
            **kwargs: 额外参数
            
        Returns:
            每个股票代码导入的记录数
        """
        stock_codes = self.load_stock_codes_from_json(stock_codes_path)

        if not self.importer:
            logger.error("错误: 未配置数据导入器")
            return {}
        
        results = {}
        total_count = len(stock_codes)
        
        logger.info(f"开始处理并导入 {total_count} 个股票的{self.get_data_type_name()}到ClickHouse...")

        # count = 0
        for i, ts_code in enumerate(stock_codes):
            logger.info(f"\n进度: {i+1}/{total_count} - 正在处理股票: {ts_code}")
            
            try:
                # 使用转换器处理数据
                processed_data = self.transformer.process_stock_data(ts_code, **kwargs)
                
                if not processed_data:
                    logger.warning(f"✗ 股票 {ts_code} 处理后{self.get_data_type_name()}为空")
                    results[ts_code] = 0
                    continue
                
                # 使用导入器批量插入数据
                imported_count = self.importer.batch_insert(processed_data, table_name, batch_size)
                results[ts_code] = imported_count

                # # for debug
                # # 导入到本地csv看看效果
                # _dir = os.path.join('/home/<USER>/SeekAlpha-Stock-Database/.cache/debug')
                # pd.DataFrame.from_records(processed_data).to_csv(f'{_dir}/{ts_code}_{self.get_data_type_name()}.csv', index=False)

                # count += 1
                # if count > 10:
                #     break

                logger.info(f"✓ {ts_code} 成功导入 {imported_count} 条{self.get_data_type_name()}记录")


            except Exception as e:
                logger.error(f"✗ 处理股票 {ts_code} {self.get_data_type_name()}时发生错误: {str(e)}")
                results[ts_code] = 0
            
        
        # 统计结果
        total_imported = sum(results.values())
        success_count = sum(1 for count in results.values() if count > 0)
        failed_count = total_count - success_count
        
        logger.info(f"\n{self.get_data_type_name()}导入完成:")
        logger.info(f"成功导入股票数: {success_count}")
        logger.info(f"失败股票数: {failed_count}")
        logger.info(f"总导入记录数: {total_imported}")
        
        return results
    
    def load_stock_codes_from_json(self, stock_codes_path: str) -> List[str]:
        """
        从JSON文件中加载股票代码列表
        
        Args:
            stock_codes_path: JSON文件路径
            
        Returns:
            股票代码列表
        """
        try:
            with open(stock_codes_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            stock_codes = list(set(data.get('stock_codes', [])))
            stock_codes.sort()
            logger.info(f"从 {stock_codes_path} 加载了 {len(stock_codes)} 个股票代码")
            return stock_codes
        except Exception as e:
            logger.error(f"加载股票代码JSON文件时发生错误: {str(e)}")
            return []