"""
ClickHouse导入器实现
负责将数据导入到ClickHouse数据库
"""

from typing import List, Dict, Any
from ..core.base_importer import BaseImporter
from utils.clickhouse_client import ClickHouseClient
from app.server.core.log import logger


class ClickHouseImporter(BaseImporter):
    """ClickHouse导入器 
    如果client参数为None，则使用config参数初始化ClickHouseClient实例。
    参数:
        client: ClickHouseClient实例
        config: 配置字典
    
    """
    
    def __init__(
        self, 
        config: Dict[str, Any] = None,
        client: ClickHouseClient = None
    ):
        super().__init__(config)

        if client:
            self.db_client = client
        else:
            if self.db_config:
                self.db_client = ClickHouseClient(**self.db_config)
            else:
                self.db_client = None
                logger.error("未配置ClickHouse数据库连接信息")
    
    def insert_data(self, data: List[Dict[str, Any]], table_name: str) -> bool:
        """
        插入数据到ClickHouse
        
        Args:
            data: 要插入的数据列表
            table_name: 表名
            
        Returns:
            是否成功
        """
        try:
            if not self.db_client:
                logger.error("ClickHouse客户端未初始化")
                return False
            
            if not data:
                logger.warning("没有数据需要插入")
                return True
            
            success = self.db_client.insert_many(table_name, data)
            return success
            
        except Exception as e:
            logger.error(f"插入数据到ClickHouse时发生错误: {str(e)}")
            return False