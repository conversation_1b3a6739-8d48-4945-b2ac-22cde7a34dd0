"""
收集器工厂类
负责创建不同类型的数据收集器
"""

from typing import Dict, Any
from ..collectors.market_collector import MarketCollector
from ..collectors.chip_collector import ChipCollector
from ..collectors.moneyflow_collector import MoneyflowCollector
from ..collectors.industry_collector import IndustryCollector
from ..collectors.index_collector import IndexCollector
from ..collectors.minutes_collector import MinutesCollector
from ..collectors.sector_collector import SectorCollector


class CollectorFactory:
    """收集器工厂类"""
    
    # 收集器映射
    COLLECTOR_MAP = {
        'market': MarketCollector,
        'chip': ChipCollector,
        'moneyflow': MoneyflowCollector,
        'industry': IndustryCollector,
        'index': IndexCollector,
        'minutes': MinutesCollector,
        'sector': SectorCollector
    }
    
    @classmethod
    def create_collector(cls, data_type: str, config: Dict[str, Any]):
        """
        创建指定类型的收集器
        
        Args:
            data_type: 数据类型 ('market', 'chip', 'moneyflow', 'industry', 'index', 'minutes', 'sector')
            config: 配置字典
            
        Returns:
            对应的收集器实例
            
        Raises:
            ValueError: 不支持的数据类型
        """
        if data_type not in cls.COLLECTOR_MAP:
            raise ValueError(f"不支持的数据类型: {data_type}. 支持的类型: {list(cls.COLLECTOR_MAP.keys())}")
        
        collector_class = cls.COLLECTOR_MAP[data_type]
        return collector_class(config)
    
    @classmethod
    def get_supported_types(cls) -> list:
        """
        获取支持的数据类型列表
        
        Returns:
            支持的数据类型列表
        """
        return list(cls.COLLECTOR_MAP.keys())