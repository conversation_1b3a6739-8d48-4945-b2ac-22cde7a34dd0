"""
股票数据收集器包
重构后的模块化架构
"""

from .core.base_collector import BaseCollector
from .collectors.market_collector import MarketCollector
from .collectors.chip_collector import ChipCollector
from .collectors.moneyflow_collector import MoneyflowCollector
from .collectors.industry_collector import IndustryCollector
from .collectors.index_collector import IndexCollector
from .collectors.minutes_collector import MinutesCollector
from .collectors.sector_collector import SectorCollector


__all__ = [
    'BaseCollector',
    'MarketCollector', 
    'ChipCollector',
    'MoneyflowCollector',
    'IndustryCollector',
    'IndexCollector',
    'MinutesCollector',
    'SectorCollector'
]