"""
市场数据收集器
负责收集股票市场数据，包括行情数据和复权因子数据
"""

from typing import Dict, Any, Literal, List
import time
import os

import pandas as pd

from ..core.base_collector import BaseCollector
from ..downloaders.tushare_downloader import <PERSON><PERSON><PERSON><PERSON>arketDownloader, TushareAdjFactorDownloader
from ..transformers.market_transformer import MarketTransformer
from ..importers.clickhouse_importer import ClickHouseImporter
from app.server.core.log import logger
from utils.clickhouse_client import ClickHouseClient

class MarketCollector(BaseCollector):
    """市场数据收集器"""
    
    def __init__(self, config: Dict[str, Any], db_client: ClickHouseClient = None):
        super().__init__(config, db_client)
        # 也创建复权因子下载器
        self.adj_factor_downloader = TushareAdjFactorDownloader(config)
    
    def _create_downloader(self):
        """创建市场数据下载器"""
        return TushareMarketDownloader(self.config)
    
    def _create_transformer(self):
        """创建市场数据转换器"""
        return MarketTransformer(self.config)
    
    def _create_importer(self):
        """创建ClickHouse导入器"""
        return ClickHouseImporter(config=self.config, client=self.db_client)
    
    def get_data_type_name(self) -> str:
        return "市场数据"
    
    def get_table_name(self) -> str:
        return "daily_quotes"
    
    def get_date_column_name(self) -> str:
        return "trade_date"
    
    def daily_update(
        self, 
        stock_codes: List[str], 
        latest_date_in_db: str, 
        end_date: str, 
        calendar_df: pd.DataFrame,
        data_type: str = 'share',
        **kwargs
    ) -> Dict[str, Any]:
        """
        每日更新市场数据
        
        Args:
            stock_codes: 股票代码列表
            latest_date_in_db: 数据库中最新日期
            end_date: 结束日期
            calendar_df: 交易日历数据
            data_type: 数据类型，'share'为股票，'index'为指数，'sector'为板块
            
        Returns:
            Dict[str, Any]: 更新结果统计
        """
        
        logger.info("=" * 80)
        logger.info("开始每日市场数据更新")
        logger.info("=" * 80)
        
        try:
            logger.info(f"加载目标股票: {len(stock_codes)} 支")
            
            missing_dates = self.get_missing_trade_dates(latest_date_in_db, end_date, calendar_df)
            
            if not missing_dates:
                logger.info("无需更新市场数据")
                return {"message": "无需更新", "imported_count": 0}
            
            logger.info(f"需要更新的交易日: {missing_dates}")
            
            # 获取数据（包含用于计算的前一日）
            fetch_dates, exclude_date = self._get_fetch_dates_with_prev(missing_dates, calendar_df)
            logger.info(f"需要排除入库的日期: {exclude_date}")
            
            if data_type == 'share':
                # 批量获取行情数据和复权因子数据
                market_df, adj_df = self._fetch_daily_data_batch(
                    trade_dates=fetch_dates, 
                    stock_codes=stock_codes
                )
                # 合并行情数据和复权因子数据
                merged_df = self._merge_market_and_adj_data(market_df, adj_df)
            else:
                # 指数和板块数据
                merged_df = self._fetch_daily_data_batch(
                    trade_dates=fetch_dates, 
                    stock_codes=stock_codes
                )
            
            # 处理数据（计算change和return）
            processed_data = self.transformer.transform_data(
                merged_df, 
                trade_calendar=calendar_df, 
                data_type=data_type,
                exclude_date=exclude_date
            )
            
            # 批量入库
            if processed_data:
                imported_count = self.importer.batch_insert(processed_data, self.get_table_name(), batch_size=1000)
                # # for debug
                # output_dir = '/home/<USER>/SeekAlpha-Stock-Database/.cache/debug'
                # output_file = os.path.join(output_dir, self.get_table_name() + '.csv')
                # pd.DataFrame.from_records(processed_data).to_csv(output_file, index=False)
                # imported_count = len(processed_data)
            else:
                imported_count = 0
                logger.warning("没有市场数据需要导入")
            
            result = {
                "missing_dates": missing_dates,
                "imported_count": imported_count,
                "exclude_date": exclude_date,
                "processed_stocks": len(set(item['instrument_id'] for item in processed_data)) if processed_data else 0
            }
            
            logger.info("=" * 80)
            logger.info("每日市场数据更新完成")
            logger.info("=" * 80)
            
            return result
            
        except Exception as e:
            logger.error(f"每日市场数据更新失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return {"error": str(e), "imported_count": 0}
    
    def _fetch_daily_data_batch(
        self, 
        trade_dates: List[str], 
        stock_codes: List[str]
    ) -> tuple[pd.DataFrame, pd.DataFrame]:
        """
        批量获取指定日期的行情数据和复权因子（市场数据特殊方法）
        
        Args:
            trade_dates: 交易日期列表
            stock_codes: 股票代码列表
            
        Returns:
            Tuple[pd.DataFrame, pd.DataFrame]: (行情数据, 复权因子数据)
        """
        
        all_market_data = []
        all_adj_data = []
        
        logger.info(f"开始获取 {len(trade_dates)} 个交易日的市场数据...")
        
        for i, trade_date in enumerate(trade_dates, 1):
            logger.info(f"正在获取 {trade_date} 的数据 ({i}/{len(trade_dates)})")
            
            try:
                # 获取该日期所有股票的行情数据
                daily_df = self.downloader.download_daily_data(trade_date=trade_date)
                time.sleep(0.3)  # 避免API限制
                
                # 获取该日期所有股票的复权因子数据
                adj_df = self.adj_factor_downloader.download_data(trade_date=trade_date)
                time.sleep(0.3)  # 避免API限制
                
                # 立即筛选目标股票
                if not daily_df.empty:
                    daily_filtered = self._filter_target_stocks(daily_df, stock_codes)
                    if not daily_filtered.empty:
                        all_market_data.append(daily_filtered)
                        logger.info(f"  获取到 {len(daily_filtered)} 只目标股票的行情数据")
                
                if not adj_df.empty:
                    adj_filtered = self._filter_target_stocks(adj_df, stock_codes)
                    if not adj_filtered.empty:
                        all_adj_data.append(adj_filtered)
                        logger.info(f"  获取到 {len(adj_filtered)} 只目标股票的复权因子数据")
                        
            except Exception as e:
                logger.error(f"获取 {trade_date} 数据时发生错误: {str(e)}")
                continue
        
        # 合并所有数据
        market_df = pd.concat(all_market_data, ignore_index=True) if all_market_data else pd.DataFrame()
        adj_df = pd.concat(all_adj_data, ignore_index=True) if all_adj_data else pd.DataFrame()
        
        logger.info(f"总计获取行情数据: {len(market_df)} 条")
        logger.info(f"总计获取复权因子数据: {len(adj_df)} 条")
        
        return market_df, adj_df
    
    def _merge_market_and_adj_data(self, market_df, adj_df):
        """
        合并行情数据和复权因子数据
        
        Args:
            market_df: 行情数据
            adj_df: 复权因子数据
            
        Returns:
            pd.DataFrame: 合并后的数据
        """
        
        if market_df.empty:
            logger.warning("行情数据为空")
            return pd.DataFrame()
        
        if adj_df.empty:
            logger.warning("复权因子数据为空")
            return market_df
        
        # 合并数据
        merged_df = pd.merge(
            market_df,
            adj_df[['ts_code', 'trade_date', 'adj_factor']],
            on=['ts_code', 'trade_date'],
            how='left'
        )
        
        logger.info(f"合并后数据: {len(merged_df)} 条")
        return merged_df
    
    def collect_stock_data(self, 
                          ts_code: str,
                          start_date: str = '',
                          end_date: str = '',
                          adj: Literal['qfq', 'hfq', None] = None,
                          freq: str = 'D',
                          ma: list = [],
                          overwrite: bool = False) -> bool:
        """
        收集指定股票的市场数据
        
        Args:
            ts_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            adj: 复权类型
            freq: 数据频度
            ma: 均线周期列表
            overwrite: 是否覆盖已存在的文件
            
        Returns:
            bool: 成功返回True，失败返回False
        """
        return self.collect_single_stock_data(
            downloader=self.downloader,
            ts_code=ts_code,
            start_date=start_date,
            end_date=end_date,
            overwrite=overwrite,
            adj=adj,
            freq=freq,
            ma=ma
        )
    
    def collect_adj_factor(self,
                          ts_code: str,
                          start_date: str,
                          end_date: str,
                          overwrite: bool = False) -> bool:
        """
        收集股票复权因子数据
        
        Args:
            ts_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            overwrite: 是否覆盖已存在的文件
            
        Returns:
            bool: 成功返回True，失败返回False
        """
        return self.collect_single_stock_data(
            downloader=self.adj_factor_downloader,
            ts_code=ts_code,
            start_date=start_date,
            end_date=end_date,
            overwrite=overwrite
        )
    
    def batch_collect_stock_data(self, 
                                stock_codes_path: str,
                                start_date: str,
                                end_date: str,
                                adj: Literal['qfq', 'hfq', None] = None,
                                freq: str = 'D',
                                overwrite: bool = False,
                                max_retries: int = 3) -> Dict[str, bool]:
        """
        批量收集股票数据
        
        Args:
            stock_codes_path: 股票代码列表文件路径
            start_date: 开始日期
            end_date: 结束日期
            adj: 复权类型
            freq: 数据频度
            overwrite: 是否覆盖已存在的文件
            max_retries: 最大重试次数
            
        Returns:
            每个股票代码的收集结果
        """
        logger.info(f"复权类型: {adj if adj else '不复权'}")
        
        return self.batch_collect_data(
            downloader=self.downloader,
            stock_codes_path=stock_codes_path,
            start_date=start_date,
            end_date=end_date,
            overwrite=overwrite,
            max_retries=max_retries,
            adj=adj,
            freq=freq
        )
    
    def batch_collect_adj_factors(self,
                                 stock_codes_path: str, 
                                 start_date: str,
                                 end_date: str,
                                 overwrite: bool = False,
                                 max_retries: int = 3) -> Dict[str, bool]:
        """
        批量收集复权因子数据
        
        Args:
            stock_codes_path: 股票代码列表文件路径
            start_date: 开始日期
            end_date: 结束日期
            overwrite: 是否覆盖已存在的文件
            max_retries: 最大重试次数
            
        Returns:
            每个股票代码的收集结果
        """
        return self.batch_collect_data(
            downloader=self.adj_factor_downloader,
            stock_codes_path=stock_codes_path,
            start_date=start_date,
            end_date=end_date,
            overwrite=overwrite,
            max_retries=max_retries
        )
    
    def process_and_import_to_clickhouse(
        self, 
        stock_codes_path: str,
        trade_calendar_path: str = None,
        table_name: str = "daily_quotes",
        batch_size: int = 1000,
        data_type: str = 'share'
    ) -> Dict[str, int]:
        """
        处理CSV文件并导入到ClickHouse数据库
        
        Args:
            stock_codes_path: 股票代码列表文件路径
            trade_calendar_path: 交易日历文件路径
            table_name: ClickHouse表名
            batch_size: 批量插入的大小
            data_type: 数据类型，'share'为股票，'index'为指数，'sector'为板块
            
        Returns:
            每个股票代码导入的记录数
        """
        if not trade_calendar_path or not trade_calendar_path.strip():
            logger.error("错误: 股票数据必须提供有效的交易日历文件路径")
            return {}
        
        # 加载交易日历
        trade_calendar = None

        logger.info(f"正在加载交易日历: {trade_calendar_path}")
        trade_calendar = pd.read_csv(trade_calendar_path)
        logger.info(f"成功加载交易日历，共 {len(trade_calendar)} 条记录")

        if trade_calendar.empty:
            raise ValueError("交易日历为空")

        return super().process_and_import_to_clickhouse(
            stock_codes_path=stock_codes_path,
            table_name=table_name,
            batch_size=batch_size,
            trade_calendar=trade_calendar,
            data_type=data_type
        )
        