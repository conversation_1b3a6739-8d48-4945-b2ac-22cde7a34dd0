"""
分钟线数据收集器
负责收集股票分钟线数据，使用BaoStock数据源
"""

from typing import Dict, Any, List
import time
import copy
import os
import pickle
from datetime import datetime
import traceback

import pandas as pd
from joblib import Parallel, delayed


from ..core.base_collector import BaseCollector
from ..downloaders.baostock_downloader import BaoStockMinutesDownloader
from ..transformers.minutes_transformer import MinutesTransformer
from ..importers.clickhouse_importer import ClickHouseImporter
from app.server.core.log import logger
from utils.clickhouse_client import ClickHouseClient


class MinutesCollector(BaseCollector):
    """分钟线数据收集器"""
    
    def __init__(self, config: Dict[str, Any], db_client: ClickHouseClient = None):
        super().__init__(config, db_client)
        self.frequency = config.get('frequency', '5')  # 默认5分钟
    
    def _create_downloader(self):
        """创建BaoStock分钟线下载器"""
        return BaoStockMinutesDownloader(self.config)
    
    def _create_transformer(self):
        """创建分钟线转换器"""
        return MinutesTransformer(self.config)
    
    def _create_importer(self):
        """创建ClickHouse导入器"""
        return ClickHouseImporter(config=self.config, client=self.db_client)
    
    def get_data_type_name(self) -> str:
        return f"{self.frequency}分钟线数据"
    
    def get_table_name(self) -> str:
        return "stock_5_min"  # 默认表名，可以根据频率调整
    
    def get_date_column_name(self) -> str:
        return "trade_date"
    
    def collect_minutes_data(self,
                           ts_code: str,
                           start_date: str,
                           end_date: str,
                           overwrite: bool = False,
                           frequency: str = None) -> bool:
        """
        收集单个股票的分钟线数据
        
        Args:
            ts_code: 股票代码
            start_date: 开始日期 (YYYY-MM-DD格式)
            end_date: 结束日期 (YYYY-MM-DD格式)
            overwrite: 是否覆盖已存在的文件
            frequency: 数据频率，如果不指定则使用默认频率
            
        Returns:
            bool: 成功返回True，失败返回False
        """
        # 设置频率参数
        kwargs = {}
        if frequency:
            kwargs['frequency'] = frequency
        
        # 使用基类的collect_single_stock_data方法
        return self.collect_single_stock_data(
            downloader=self.downloader,
            ts_code=ts_code,
            start_date=start_date,
            end_date=end_date,
            overwrite=overwrite,
            **kwargs
        )
    
    def batch_collect_minutes_data(self,
                                 stock_codes_path: str,
                                 start_date: str,
                                 end_date: str,
                                 overwrite: bool = False,
                                 max_retries: int = 3,
                                 frequency: str = None) -> Dict[str, bool]:
        """
        批量收集分钟线数据
        
        Args:
            stock_codes_path: 股票代码列表文件路径
            start_date: 开始日期 (YYYY-MM-DD格式)
            end_date: 结束日期 (YYYY-MM-DD格式)
            overwrite: 是否覆盖已存在的文件
            max_retries: 最大重试次数
            frequency: 数据频率
            
        Returns:
            Dict[str, bool]: 每个股票的下载结果
        """
        # 设置频率参数
        kwargs = {}
        if frequency:
            kwargs['frequency'] = frequency
        
        # 使用基类的batch_collect_data方法
        return self.batch_collect_data(
            downloader=self.downloader,
            stock_codes_path=stock_codes_path,
            start_date=start_date,
            end_date=end_date,
            overwrite=overwrite,
            max_retries=max_retries,
            **kwargs
        )
    
    def daily_update(self, 
                    stock_codes: List[str], 
                    latest_date_in_db: str, 
                    end_date: str, 
                    calendar_df: pd.DataFrame) -> Dict[str, Any]:
        """
        每日更新分钟行情数据
        
        Args:
            stock_codes: 股票代码列表
            latest_date_in_db: 数据库中最新日期
            end_date: 结束日期
            calendar_df: 交易日历数据
            
        Returns:
            Dict[str, Any]: 更新结果统计
        """
        
        logger.info("=" * 80)
        logger.info(f"开始每日{self.get_data_type_name()}更新")
        logger.info("=" * 80)
        
        try:
            logger.info(f"加载目标股票: {len(stock_codes)} 只")
            
            # 1. 确定需要更新的交易日期
            missing_dates = self.get_missing_trade_dates(latest_date_in_db, end_date, calendar_df)
            
            if not missing_dates:
                logger.info(f"无需更新{self.get_data_type_name()}")
                return {"message": "无需更新", "imported_count": 0}
            
            logger.info(f"需要更新的交易日: {missing_dates}")
            
            # 2. 按股票获取分钟数据
            all_minutes_data = []
            total_stocks = len(stock_codes)
            
            for i, stock_code in enumerate(stock_codes, 1):
                logger.info(f"正在处理股票 {stock_code} ({i}/{total_stocks})")
                
                try:
                    # 为每个股票获取所有缺失日期的分钟数据
                    stock_data = self._fetch_stock_minutes_data(
                        stock_code, 
                        missing_dates
                    )
                    
                    if not stock_data.empty:
                        all_minutes_data.append(stock_data)
                        logger.info(f"  获取到 {len(stock_data)} 条分钟数据")
                    else:
                        logger.warning(f"  股票 {stock_code} 无分钟数据")
                        
                except Exception as e:
                    logger.error(f"获取股票 {stock_code} 分钟数据时发生错误: {str(e)}")
                    continue
            
            # 3. 合并所有数据
            if all_minutes_data:
                combined_df = pd.concat(all_minutes_data, ignore_index=True)
                logger.info(f"合并后总计: {len(combined_df)} 条分钟数据")
            else:
                logger.warning("没有获取到任何分钟数据")
                return {"message": "没有数据", "imported_count": 0}
            
            # 4. 数据转换处理
            processed_data = self.transformer.transform_data(combined_df)
            
            # 5. 批量入库
            if processed_data:
                imported_count = self.importer.batch_insert(
                    processed_data, 
                    self.get_table_name(), 
                    batch_size=3000  # 分钟数据量大，增加批次大小
                )
                # # for debug
                # output_dir = '/home/<USER>/SeekAlpha-Stock-Database/.cache/debug'
                # output_file = os.path.join(output_dir, self.get_table_name() + '.csv')
                # pd.DataFrame.from_records(processed_data).to_csv(output_file, index=False)
                # imported_count = len(processed_data)
            else:
                imported_count = 0
                logger.warning("没有处理后的分钟数据需要导入")
            
            result = {
                "missing_dates": missing_dates,
                "imported_count": imported_count,
                "processed_stocks": len(set(item['instrument_id'] for item in processed_data)) if processed_data else 0,
                "total_minutes_records": len(combined_df) if all_minutes_data else 0
            }
            
            logger.info("=" * 80)
            logger.info(f"每日{self.get_data_type_name()}更新完成")
            logger.info("=" * 80)
            
            return result
            
        except Exception as e:
            logger.error(f"每日{self.get_data_type_name()}更新失败: {str(e)}")
            traceback.print_exc()
            return {"error": str(e), "imported_count": 0}

    def _fetch_stock_minutes_data(self, stock_code: str, trade_dates: List[str]) -> pd.DataFrame:
        """
        获取单个股票在指定日期范围内的分钟数据
        
        Args:
            stock_code: 股票代码
            trade_dates: 交易日期列表
            
        Returns:
            pd.DataFrame: 分钟数据
        """
        all_data = []
        
        try:
            # 将日期列表转换为起止日期
            # yyyymmdd转化为yyyy-mm-dd
            start_date = datetime.strptime(min(trade_dates), '%Y%m%d').strftime('%Y-%m-%d')
            end_date = datetime.strptime(max(trade_dates), '%Y%m%d').strftime('%Y-%m-%d')

            
            # 调用BaoStock下载器获取数据
            df = self.downloader.download_data(
                ts_code=stock_code,
                start_date=start_date,
                end_date=end_date,
                frequency=self.frequency
            )
            
            # API调用间隔控制
            time.sleep(0.1)
            
            if not df.empty:
                all_data.append(df)
            
            return pd.concat(all_data, ignore_index=True) if all_data else pd.DataFrame()
            
        except Exception as e:
            logger.error(f"获取股票 {stock_code} 分钟数据时发生错误: {str(e)}")
            return pd.DataFrame()
    
    def process_and_import_to_clickhouse_multi_process(
        self,
        stock_codes_path: str,
        table_name: str,
        batch_size: int = 1000,
        cache_dir: str = None,
        n_jobs: int = 20,
        **kwargs
    ) -> Dict[str, int]:
        """
        使用joblib多进程处理CSV文件并导入到ClickHouse数据库
            
        Returns:
            每个股票代码导入的记录数
        """
        stock_codes = self.load_stock_codes_from_json(stock_codes_path)
        if not self.importer:
            logger.error("错误: 未配置数据导入器")
            return {}
        
        os.makedirs(cache_dir, exist_ok=True)
        
        results = {}
        total_count = len(stock_codes)
        
        logger.info(f"开始处理并导入 {total_count} 个股票的{self.get_data_type_name()}到ClickHouse...")
        transformer = MinutesTransformer(copy.deepcopy(self.config))
        Parallel(n_jobs=n_jobs)(
            delayed(transformer.process_stock_data_and_save)(ts_code, cache_dir) for ts_code in stock_codes
        )

        failed_codes = []
        for i, ts_code in enumerate(stock_codes):
            logger.info(f"\n进度: {i+1}/{total_count} - 正在处理股票: {ts_code}")
            try:

                file_path = os.path.join(cache_dir, f"{ts_code}.pkl")
                if os.path.exists(file_path):
                    with open(file_path, 'rb') as f:
                        processed_data = pickle.load(f)
                    imported_count = self.importer.batch_insert(processed_data, table_name, batch_size)
                    results[ts_code] = imported_count
                    logger.info(f"✓ {ts_code} 成功导入 {imported_count} 条{self.get_data_type_name()}记录")

                else:
                    results[ts_code] = 0
                    failed_codes.append(ts_code)
                    logger.warning(f"✗ 股票 {ts_code} 的{self.get_data_type_name()}数据未处理")
            except Exception as e:
                logger.error(f"✗ 处理股票 {ts_code} {self.get_data_type_name()}时发生错误: {str(e)}")
                results[ts_code] = 0
                failed_codes.append(ts_code)
        
        # 统计结果
        total_imported = sum(results.values())
        success_count = sum(1 for count in results.values() if count > 0)
        failed_count = total_count - success_count
        
        logger.info(f"\n{self.get_data_type_name()}导入完成:")
        logger.info(f"成功导入股票数: {success_count}")
        logger.info(f"失败股票数: {failed_count}")
        logger.info(f"总导入记录数: {total_imported}")
        logger.info(f"失败股票数: {failed_codes}")
        
        return results