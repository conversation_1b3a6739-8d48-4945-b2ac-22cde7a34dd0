"""
资金流数据收集器
负责收集股票资金流数据
"""

from typing import Dict, Any, List
import os
import pandas as pd

from ..core.base_collector import BaseCollector
from ..downloaders.tushare_downloader import TushareMoneyflowDownloader
from ..transformers.moneyflow_transformer import MoneyflowTransformer
from ..importers.clickhouse_importer import ClickHouseImporter


from app.server.core.log import logger

class MoneyflowCollector(BaseCollector):
    """资金流数据收集器"""
    
    def _create_downloader(self):
        """创建资金流数据下载器"""
        return TushareMoneyflowDownloader(self.config)
    
    def _create_transformer(self):
        """创建资金流数据转换器"""
        return MoneyflowTransformer(self.config)
    
    def _create_importer(self):
        """创建ClickHouse导入器"""
        return ClickHouseImporter(config=self.config, client=self.db_client)
    
    def get_data_type_name(self) -> str:
        return "资金流数据"
    
    def get_table_name(self) -> str:
        return "daily_money_flow"
    
    def get_date_column_name(self) -> str:
        return "trade_date"
    
    def daily_update(
        self, 
        stock_codes: List[str], 
        latest_date_in_db: str, 
        end_date: str, 
        calendar_df: pd.DataFrame
    ) -> Dict[str, Any]:
        """
        每日更新资金流数据
        
        Args:
            stock_codes: 股票代码列表
            latest_date_in_db: 数据库中最新日期
            end_date: 结束日期
            calendar_df: 交易日历数据
            
        Returns:
            Dict[str, Any]: 更新结果统计
        """
        
        
        logger.info("=" * 80)
        logger.info("开始每日资金流数据更新")
        logger.info("=" * 80)
        
        try:
            logger.info(f"加载目标股票: {len(stock_codes)} 只")
            
            missing_dates = self.get_missing_trade_dates(latest_date_in_db, end_date, calendar_df)
            
            if not missing_dates:
                logger.info("无需更新资金流数据")
                return {"message": "无需更新", "imported_count": 0}
            
            logger.info(f"需要更新的交易日: {missing_dates}")
            
            # 获取资金流数据（资金流数据不需要前一日计算，直接获取缺失日期）
            moneyflow_df = self._fetch_daily_data_batch(
                trade_dates=missing_dates, 
                stock_codes=stock_codes, 
                download_func=self.downloader.download_data,
                sleep_time=0.3  # 资金流数据API限制较严格
            )
            
            # 处理数据
            processed_data = self.transformer.transform_data(moneyflow_df)
            
            # 批量入库
            if processed_data:
                imported_count = self.importer.batch_insert(processed_data, self.get_table_name(), batch_size=1000)
                # # for debug
                # output_dir = '/home/<USER>/SeekAlpha-Stock-Database/.cache/debug'
                # output_file = os.path.join(output_dir, self.get_table_name() + '.csv')
                # pd.DataFrame.from_records(processed_data).to_csv(output_file, index=False)
                # imported_count = len(processed_data)
            else:
                imported_count = 0
                logger.warning("没有资金流数据需要导入")
            
            result = {
                "missing_dates": missing_dates,
                "imported_count": imported_count,
                "processed_stocks": len(set(item['instrument_id'] for item in processed_data)) if processed_data else 0
            }
            
            logger.info("=" * 80)
            logger.info("每日资金流数据更新完成")
            logger.info("=" * 80)
            
            return result
            
        except Exception as e:
            logger.error(f"每日资金流数据更新失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return {"error": str(e), "imported_count": 0}
    
    def collect_moneyflow(self,
                         ts_code: str,
                         start_date: str,
                         end_date: str,
                         overwrite: bool = False) -> bool:
        """
        收集股票资金流向数据
        
        Args:
            ts_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            overwrite: 是否覆盖已存在的文件
            
        Returns:
            bool: 成功返回True，失败返回False
        """
        return self.collect_single_stock_data(
            downloader=self.downloader,
            ts_code=ts_code,
            start_date=start_date,
            end_date=end_date,
            overwrite=overwrite
        )
    
    def batch_collect_moneyflow(self,
                               stock_codes_path: str, 
                               start_date: str,
                               end_date: str,
                               overwrite: bool = False,
                               max_retries: int = 3) -> Dict[str, bool]:
        """
        批量收集资金流向数据
        
        Args:
            stock_codes_path: 股票代码列表文件路径
            start_date: 开始日期
            end_date: 结束日期
            overwrite: 是否覆盖已存在的文件
            max_retries: 最大重试次数
            
        Returns:
            每个股票代码的收集结果
        """
        return self.batch_collect_data(
            downloader=self.downloader,
            stock_codes_path=stock_codes_path,
            start_date=start_date,
            end_date=end_date,
            overwrite=overwrite,
            max_retries=max_retries
        )
    