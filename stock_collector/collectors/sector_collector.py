"""
板块数据收集器
负责收集板块行情数据，继承市场数据收集器
"""

import time
import os
from typing import Dict, Any, List

import pandas as pd

from ..core.base_collector import BaseCollector
from ..collectors.market_collector import MarketCollector
from ..downloaders.tushare_downloader import TushareSectorDownloader
from ..transformers.sector_transformer import SectorTransformer

from app.server.core.log import logger


class SectorCollector(MarketCollector):
    """板块数据收集器"""
    
    def _create_downloader(self):
        """创建板块数据下载器"""
        return TushareSectorDownloader(self.config)
    
    def _create_transformer(self):
        """创建板块数据转换器"""
        return SectorTransformer(self.config)
    
    def get_data_type_name(self) -> str:
        return "板块数据"
    
    def get_table_name(self) -> str:
        return "daily_sector_quotes"
    
    def daily_update(self, *args, **kwargs):
        """重写daily_update方法，确保传入正确的data_type参数"""
        kwargs['data_type'] = 'sector'
        return super().daily_update(*args, **kwargs)
    
    def process_and_import_to_clickhouse(self, *args, **kwargs):
        """重写process_and_import_to_clickhouse方法，确保传入正确的data_type参数"""
        kwargs['data_type'] = 'sector'
        return super().process_and_import_to_clickhouse(*args, **kwargs)
    
    def get_or_create_sector_codes(self, stock_codes_path: str, trade_date: str = None) -> List[str]:
        """
        获取或创建板块代码列表
        
        Args:
            trade_date: 指定交易日期
            
        Returns:
            板块代码列表
        """
        return self.downloader.get_all_sector_codes(stock_codes_path, trade_date)
    
    def batch_collect_sector_data(
        self,
        stock_codes_path: str,
        start_date: str = '',
        end_date: str = '',
        overwrite: bool = False,
        max_retries: int = 3
    ) -> Dict[str, bool]:
        """
        批量收集板块数据
        
        Args:
            stock_codes_path: 股票代码列表文件路径
            start_date: 开始日期
            end_date: 结束日期
            overwrite: 是否覆盖已存在的文件
            max_retries: 最大重试次数
            
        Returns:
            每个板块代码的收集结果
        """
        return self.batch_collect_data(
            downloader=self.downloader,
            stock_codes_path=stock_codes_path,
            start_date=start_date,
            end_date=end_date,
            overwrite=overwrite,
            max_retries=max_retries
        )

    def _fetch_daily_data_batch(
        self, 
        trade_dates: List[str], 
        stock_codes: List[str]
    ) -> pd.DataFrame:
        """
        批量获取指定日期的板块数据
        
        Args:
            trade_dates: 交易日期列表
            stock_codes: 板块代码列表
            
        Returns:
            pd.DataFrame: 板块数据
        """
        return BaseCollector._fetch_daily_data_batch(
            self, 
            trade_dates=trade_dates, 
            stock_codes=stock_codes,
            download_func=self.downloader.download_data,
            sleep_time=0.3
        )
    # def _fetch_daily_data_batch(
    #     self, 
    #     trade_dates: List[str], 
    #     stock_codes: List[str]
    # ) -> pd.DataFrame:
    #     """
    #     批量获取指定日期的板块数据
        
    #     Args:
    #         trade_dates: 交易日期列表
    #         stock_codes: 板块代码列表
            
    #     Returns:
    #         pd.DataFrame: 板块数据
    #     """
    #     all_sector_data = []
        
    #     logger.info(f"开始获取 {len(trade_dates)} 个交易日的板块数据...")
    #     for i, trade_date in enumerate(trade_dates, 1):
    #         logger.info(f"正在获取 {trade_date} 的板块数据 ({i}/{len(trade_dates)})")
            
    #         try:
    #             # 获取该日期所有板块的行情数据
    #             daily_df = self.downloader.download_data(trade_date=trade_date)
    #             time.sleep(0.3)  # 避免API限制
                
    #             # 立即筛选目标板块
    #             if not daily_df.empty:
    #                 daily_filtered = self._filter_target_stocks(daily_df, stock_codes)
    #                 if not daily_filtered.empty:
    #                     all_sector_data.append(daily_filtered)
    #                     logger.info(f"  获取到 {len(daily_filtered)} 个目标板块的行情数据")
                        
    #         except Exception as e:
    #             logger.error(f"获取 {trade_date} 板块数据时发生错误: {str(e)}")
    #             continue
        
    #     # 合并所有数据
    #     sector_df = pd.concat(all_sector_data, ignore_index=True) if all_sector_data else pd.DataFrame()
        
    #     logger.info(f"总计获取板块数据: {len(sector_df)} 条")
        
    #     return sector_df 