"""
行业数据收集器
负责收集股票行业数据
"""

from typing import Dict, Any
import os

import pandas as pd

from ..core.base_collector import BaseCollector
from ..downloaders.tushare_downloader import TushareIndustryDownloader
from ..transformers.industry_transformer import IndustryTransformer
from ..importers.clickhouse_importer import ClickHouseImporter
from app.server.core.log import logger


class IndustryCollector(BaseCollector):
    """行业数据收集器"""
    
    def _create_downloader(self):
        """创建行业数据下载器"""
        return TushareIndustryDownloader(self.config)
    
    def _create_transformer(self):
        """创建行业数据转换器"""
        return IndustryTransformer(self.config)
    
    def _create_importer(self):
        """创建ClickHouse导入器"""
        return ClickHouseImporter(config=self.config, client=self.db_client)
    
    def get_data_type_name(self) -> str:
        return "申万行业成分数据"
    
    def get_table_name(self) -> str:
        return "stock_industry"
    
    def get_date_column_name(self) -> str:
        return "trade_date"
    
    def daily_update(self, *args, **kwargs):
        """
        行业数据的每日更新方法
        注意：行业成分数据通常不需要按日期增量更新，而是全量重新获取
        """
        # 行业数据不支持增量更新，这里可以抛出异常或返回空结果
        logger.info("行业数据不支持每日增量更新，请使用全量下载模式")
        return {}
    
    def collect_industry(self,
                        ts_code: str,
                        overwrite: bool = False) -> bool:
        """
        收集股票申万行业成分数据
        
        Args:
            ts_code: 股票代码
            overwrite: 是否覆盖已存在的文件
            
        Returns:
            bool: 成功返回True，失败返回False
        """
        return self.collect_single_stock_data(
            downloader=self.downloader,
            ts_code=ts_code,
            start_date='',  # 行业数据不需要日期范围
            end_date='',
            overwrite=overwrite
        )
    
    def batch_collect_industry(self,
                              stock_codes_path: str, 
                              overwrite: bool = False,
                              max_retries: int = 3) -> Dict[str, bool]:
        """
        批量收集申万行业成分数据
        
        Args:
            stock_codes_path: 股票代码列表文件路径
            overwrite: 是否覆盖已存在的文件
            max_retries: 最大重试次数
            
        Returns:
            每个股票代码的收集结果
        """
        return self.batch_collect_data(
            downloader=self.downloader,
            stock_codes_path=stock_codes_path,
            start_date='',  # 行业数据不需要日期范围
            end_date='',
            overwrite=overwrite,
            max_retries=max_retries
        )
