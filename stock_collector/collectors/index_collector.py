"""
指数数据收集器
负责收集指数行情数据
"""

from typing import Dict, Any, List
import time
import os

import pandas as pd

from ..core.base_collector import BaseCollector
from ..collectors.market_collector import MarketCollector
from ..downloaders.tushare_downloader import TushareIndexDownloader
from ..transformers.index_transformer import IndexTransformer

from app.server.core.log import logger



class IndexCollector(MarketCollector):
    """指数数据收集器"""
    
    def _create_downloader(self):
        """创建指数数据下载器"""
        return TushareIndexDownloader(self.config)
    
    def _create_transformer(self):
        """创建指数数据转换器"""
        return IndexTransformer(self.config)
    
    def get_data_type_name(self) -> str:
        return "指数数据"
    
    def get_table_name(self) -> str:
        return "daily_index"
    
    def daily_update(self, *args, **kwargs):
        """重写daily_update方法，确保传入正确的data_type参数"""
        kwargs['data_type'] = 'index'
        return super().daily_update(*args, **kwargs)
    
    def process_and_import_to_clickhouse(self, *args, **kwargs):
        """重写process_and_import_to_clickhouse方法，确保传入正确的data_type参数"""
        kwargs['data_type'] = 'index'
        return super().process_and_import_to_clickhouse(*args, **kwargs)

    def _fetch_daily_data_batch(
        self, 
        trade_dates: List[str], 
        stock_codes: List[str]
    ) -> pd.DataFrame:
        """
        批量获取指定日期的指数数据
        
        Args:
            trade_dates: 交易日期列表
            stock_codes: 指数代码列表
            
        Returns:
            pd.DataFrame: 指数数据
        """
        all_index_data = []
        
        logger.info(f"开始获取 {len(trade_dates)} 个交易日的指数数据...")
        for i, trade_date in enumerate(trade_dates, 1):
            logger.info(f"正在获取 {trade_date} 的指数数据 ({i}/{len(trade_dates)})")
            
            try:
                for index_code in stock_codes:
                    # 获取该日期所有指数的行情数据
                    index_df = self.downloader.download_data(ts_code=index_code, trade_date=trade_date)
                    time.sleep(0.3)  # 避免API限制
                    
                    if not index_df.empty:
                        all_index_data.append(index_df)
                    else:
                        logger.warning(f"{index_code} 在 {trade_date} 没有数据")
                        
            except Exception as e:
                logger.error(f"获取 {trade_date} 指数数据时发生错误: {str(e)}")
                continue
        
        # 合并所有数据
        index_df = pd.concat(all_index_data, ignore_index=True) if all_index_data else pd.DataFrame()
        
        logger.info(f"总计获取指数数据: {len(index_df)} 条")
        
        return index_df

