# set env from .env_sh
source .env_sh

export PYTHONPATH=$PYTHONPATH:$(pwd)
export TUSHARE_TOKEN=$(echo $TUSHARE_TOKEN)

# get params from env
CLICKHOUSE_HOST=$(echo $CLICKHOUSE_HOST)
CLICKHOUSE_PORT=$(echo $CLICKHOUSE_PORT)
CLICKHOUSE_USER=$(echo $CLICKHOUSE_USER)
CLICKHOUSE_PASSWORD=$(echo $CLICKHOUSE_PASSWORD)
CLICKHOUSE_DATABASE=$(echo $CLICKHOUSE_DATABASE)

STOCK_DATA_DIR=$(echo $STOCK_DATA_DIR)
CACHE_DIR=$(echo $CACHE_DIR)
STOCK_CODES_PATH=$(echo $STOCK_CODES_PATH)

# 数据截止日期
END_DATE=20250722

echo "variables:"
echo "--------------------------------"
echo "CLICKHOUSE_HOST: $CLICKHOUSE_HOST"
echo "CLICKHOUSE_PORT: $CLICKHOUSE_PORT"
echo "CLICKHOUSE_USER: $CLICKHOUSE_USER"
echo "CLICKHOUSE_DATABASE: $CLICKHOUSE_DATABASE"
echo "STOCK_DATA_DIR: $STOCK_DATA_DIR"
echo "CACHE_DIR: $CACHE_DIR"
echo "STOCK_CODES_PATH: $STOCK_CODES_PATH"
echo "--------------------------------"

# create stock data directory if not exists
if [ ! -d "$STOCK_DATA_DIR" ]; then
    mkdir -p $STOCK_DATA_DIR
fi

# create cache directory if not exists
if [ ! -d "$CACHE_DIR" ]; then
    mkdir -p $CACHE_DIR
fi

# # create table daily_quotes
# python utils/execute_sql.py \
#    --sql_script_path schema/daily_quotes.sql \
#    --host $CLICKHOUSE_HOST \
#    --port $CLICKHOUSE_PORT \
#    --user $CLICKHOUSE_USER \
#    --password $CLICKHOUSE_PASSWORD \
#    --database $CLICKHOUSE_DATABASE

# # create table daily_chips
# python utils/execute_sql.py \
#    --sql_script_path schema/daily_chips.sql \
#    --host $CLICKHOUSE_HOST \
#    --port $CLICKHOUSE_PORT \
#    --user $CLICKHOUSE_USER \
#    --password $CLICKHOUSE_PASSWORD \
#    --database $CLICKHOUSE_DATABASE

# # create table daily_money_flow
# python utils/execute_sql.py \
#    --sql_script_path schema/daily_money_flow.sql \
#    --host $CLICKHOUSE_HOST \
#    --port $CLICKHOUSE_PORT \
#    --user $CLICKHOUSE_USER \
#    --password $CLICKHOUSE_PASSWORD \
#    --database $CLICKHOUSE_DATABASE

# # create table stock_industry
# python utils/execute_sql.py \
#    --sql_script_path schema/clickhouse/stock_industry.sql \
#    --host $CLICKHOUSE_HOST \
#    --port $CLICKHOUSE_PORT \
#    --user $CLICKHOUSE_USER \
#    --password $CLICKHOUSE_PASSWORD \
#    --database $CLICKHOUSE_DATABASE

# # create table daily_index
# python utils/execute_sql.py \
#     --sql_script_path schema/daily_index.sql \
#     --host $CLICKHOUSE_HOST \
#     --port $CLICKHOUSE_PORT \
#     --user $CLICKHOUSE_USER \
#     --password $CLICKHOUSE_PASSWORD \
#     --database $CLICKHOUSE_DATABASE

# # get zz1000 constituents
# python scripts/get_zz1000_stocks.py \
#     --index_code 000852.SH \
#     --start_date 20130101 \
#     --end_date $END_DATE \
#     --output_file $STOCK_CODES_PATH

# download trade calendar
# python scripts/get_sse_calendar.py \
#     --start_date 20121201 \
#     --exchange SSE \
#     --is_open 1 \
#     --output_file $CACHE_DIR/trade_calendar.csv

# # download zz500 market data
# python stock_collect_main.py \
#   --stage download_market \
#   --stock_codes_path $STOCK_CODES_PATH \
#   --data_dir $STOCK_DATA_DIR \
#   --start_date 20130101 \
#   --end_date $END_DATE


# # import zz500 market data
# python stock_collect_main.py \
#   --stage import \
#   --import_type market \
#   --stock_codes_path $STOCK_CODES_PATH \
#   --data_dir $STOCK_DATA_DIR \
#   --clickhouse_host $CLICKHOUSE_HOST \
#   --clickhouse_port $CLICKHOUSE_PORT \
#   --clickhouse_user $CLICKHOUSE_USER \
#   --clickhouse_password $CLICKHOUSE_PASSWORD \
#   --clickhouse_database $CLICKHOUSE_DATABASE \
#   --table_name daily_quotes \
#   --batch_size 1000 \
#   --trade_calendar_path $CACHE_DIR/trade_calendar.csv

# # download zz500 chips data
# python stock_collect_main.py \
#   --stage download_cyq_perf \
#   --stock_codes_path $STOCK_CODES_PATH \
#   --data_dir $STOCK_DATA_DIR \
#   --start_date 20130101 \
#   --end_date $END_DATE


# # import zz500 chips data
# python stock_collect_main.py \
#   --stage import \
#   --import_type chip \
#   --stock_codes_path $STOCK_CODES_PATH \
#   --data_dir $STOCK_DATA_DIR \
#   --clickhouse_host $CLICKHOUSE_HOST \
#   --clickhouse_port $CLICKHOUSE_PORT \
#   --clickhouse_user $CLICKHOUSE_USER \
#   --clickhouse_password $CLICKHOUSE_PASSWORD \
#   --clickhouse_database $CLICKHOUSE_DATABASE \
#   --table_name daily_chips \
#   --batch_size 1000 

# # download zz500 moneyflow data
# python stock_collect_main.py \
#   --stage download_moneyflow \
#   --stock_codes_path $STOCK_CODES_PATH \
#   --data_dir $STOCK_DATA_DIR \
#   --start_date 20130101 \
#   --end_date $END_DATE

# # import zz500 moneyflow data
# python stock_collect_main.py \
#   --stage import \
#   --import_type moneyflow \
#   --stock_codes_path $STOCK_CODES_PATH \
#   --data_dir $STOCK_DATA_DIR \
#   --clickhouse_host $CLICKHOUSE_HOST \
#   --clickhouse_port $CLICKHOUSE_PORT \
#   --clickhouse_user $CLICKHOUSE_USER \
#   --clickhouse_password $CLICKHOUSE_PASSWORD \
#   --clickhouse_database $CLICKHOUSE_DATABASE \
#   --table_name daily_money_flow \
#   --batch_size 1000 

# # download_industry
# python stock_collect_main.py \
#   --stage download_industry \
#   --stock_codes_path $STOCK_CODES_PATH \
#   --data_dir $STOCK_DATA_DIR

# import_industry
python stock_collect_main.py \
  --stage import \
  --import_type industry \
  --stock_codes_path $STOCK_CODES_PATH \
  --data_dir $STOCK_DATA_DIR \
  --clickhouse_host $CLICKHOUSE_HOST \
  --clickhouse_port $CLICKHOUSE_PORT \
  --clickhouse_user $CLICKHOUSE_USER \
  --clickhouse_password $CLICKHOUSE_PASSWORD \
  --clickhouse_database $CLICKHOUSE_DATABASE \
  --table_name stock_industry \
  --batch_size 1000

# # download index data
# python stock_collect_main.py \
#     --stage download_index \
#     --stock_codes_path $CACHE_DIR/index_codes.json \
#     --data_dir $STOCK_DATA_DIR \
#     --start_date 20130101 \
#     --end_date $END_DATE

# # import index data
# python stock_collect_main.py \
#     --stage import \
#     --import_type index \
#     --stock_codes_path $CACHE_DIR/index_codes.json \
#     --data_dir $STOCK_DATA_DIR \
#     --clickhouse_host $CLICKHOUSE_HOST \
#     --clickhouse_port $CLICKHOUSE_PORT \
#     --clickhouse_user $CLICKHOUSE_USER \
#     --clickhouse_password $CLICKHOUSE_PASSWORD \
#     --clickhouse_database $CLICKHOUSE_DATABASE \
#     --table_name daily_index \
#     --batch_size 1000 \
#     --trade_calendar_path $CACHE_DIR/trade_calendar.csv