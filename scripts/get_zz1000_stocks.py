import tushare as ts
import json
from datetime import datetime
import argparse
import subprocess
import os
from dotenv import load_dotenv
import shutil

load_dotenv()


def main(index_code: str, start_date: str, end_date: str, output_file: str):
    print("=" * 60)
    print("中证500+中证1000股票代码获取工具")
    print("=" * 60)
    
    # 步骤1：调用get_zz500_stocks.py获取中证500成分股
    print("步骤1: 获取中证500成分股...")
    zz500_output_file = '.cache/zz500_all_stocks_temp.json'
    temp_dir = '.cache/zz500_daily_data'
    
    try:
        # 构建调用命令
        cmd = [
            'python', 'scripts/get_zz500_stocks.py',
            '--start_date', start_date,
            '--end_date', end_date,
            '--data_dir', temp_dir,
            '--output_file', zz500_output_file
        ]
        
        print(f"执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode != 0:
            print(f"获取中证500数据失败: {result.stderr}")
            exit(1)
        else:
            print("中证500数据获取成功！")
            
    except Exception as e:
        print(f"调用get_zz500_stocks.py时出错: {e}")
        exit(1)
    
    # 步骤2：获取中证1000成分股
    print("\n步骤2: 获取中证1000成分股...")
    
    # 设置tushare token
    TOKEN = os.getenv('TUSHARE_TOKEN')

    if not TOKEN:
        print("请设置环境变量 TUSHARE_TOKEN")
        exit(1)
        
    pro = ts.pro_api(TOKEN)
    
    # 收集所有中证1000成分股代码
    zz1000_stocks = set()
    
    # 从开始年份到结束年份，每年的1月和7月
    start_year = int(args.start_date[:4])
    end_year = int(args.end_date[:4])
    
    for year in range(start_year, end_year + 1):
        # 1月份数据
        start_date_jan = f"{year}0101"
        end_date_jan = f"{year}0131"
        
        # 7月份数据
        start_date_jul = f"{year}0701"
        end_date_jul = f"{year}0731"
        
        try:
            # 获取1月份数据 - 中证1000指数代码是000852.SH
            print(f"获取{year}年1月中证1000数据...")
            df_jan = pro.index_weight(index_code=index_code, start_date=start_date_jan, end_date=end_date_jan)
            if not df_jan.empty:
                zz1000_stocks.update(df_jan['con_code'].tolist())
            
            # 获取7月份数据
            print(f"获取{year}年7月中证1000数据...")
            df_jul = pro.index_weight(index_code=index_code, start_date=start_date_jul, end_date=end_date_jul)
            if not df_jul.empty:
                zz1000_stocks.update(df_jul['con_code'].tolist())
                
        except Exception as e:
            print(f"获取{year}年中证1000数据时出错: {e}")
            continue
    
    print(f"中证1000成分股获取完成，共 {len(zz1000_stocks)} 只")
    
    # 步骤3：读取中证500数据并合并
    print("\n步骤3: 合并中证500和中证1000成分股...")
    
    try:
        # 读取中证500数据
        with open(zz500_output_file, 'r', encoding='utf-8') as f:
            zz500_data = json.load(f)
            zz500_stocks = set(zz500_data['stock_codes'])
            print(f"中证500成分股: {len(zz500_stocks)} 只")
    except Exception as e:
        print(f"读取中证500数据失败: {e}")
        exit(1)
    
    # 合并两个集合
    all_stocks = zz500_stocks.union(zz1000_stocks)
    stock_list = sorted(list(all_stocks))
    
    print(f"合并后总计: {len(stock_list)} 只股票")
    print(f"其中中证500独有: {len(zz500_stocks - zz1000_stocks)} 只")
    print(f"其中中证1000独有: {len(zz1000_stocks - zz500_stocks)} 只")
    print(f"两者重叠: {len(zz500_stocks & zz1000_stocks)} 只")
    
    # 步骤4：保存最终结果
    print(f"\n步骤4: 保存结果到 {args.output_file}...")
    
    output_data = {
        "description": f"中证500+中证1000成分股历史汇总 ({args.start_date} 到 {args.end_date})",
        "date_range": {
            "start": args.start_date,
            "end": args.end_date
        },
        "components": {
            "zz500_count": len(zz500_stocks),
            "zz1000_count": len(zz1000_stocks),
            "overlap_count": len(zz500_stocks & zz1000_stocks),
            "unique_zz500": len(zz500_stocks - zz1000_stocks),
            "unique_zz1000": len(zz1000_stocks - zz500_stocks)
        },
        "total_count": len(stock_list),
        "stock_codes": stock_list,
        "generated_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, ensure_ascii=False, indent=2)
    
    # # 清理临时文件
    # os.remove(zz500_output_file)

    # # 删除临时文件夹
    # shutil.rmtree(temp_dir)
    # print("已清理临时文件夹")

    
    print("=" * 60)
    print("全部完成！")
    print(f"结果已保存到: {args.output_file}")
    print(f"最终股票总数: {len(stock_list)}")
    print("=" * 60)

    
if __name__ == "__main__":
    # 命令行参数解析
    parser = argparse.ArgumentParser(description='获取中证500+中证1000股票代码并集')
    parser.add_argument('--index_code', type=str, default='000852.SH', help='指数代码')
    parser.add_argument('--start_date', type=str, default='20130101', help='开始日期')
    parser.add_argument('--end_date', type=str, default='20250704', help='结束日期')
    parser.add_argument('--output_file', type=str, default='zz500_zz1000_all_stocks.json', help='输出文件名')
    args = parser.parse_args()

    main(args.index_code, args.start_date, args.end_date, args.output_file)

