#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import json
import argparse
from datetime import datetime, UTC
from typing import List, Dict, Any, Optional
import pandas as pd
import numpy as np
from pathlib import Path

# 导入现有的数据收集器
from stock_collector import StockDataCollector
from app.server.core.log import logger


class StockDataToParquet:
    """股票数据转换为Parquet格式的处理器"""
    
    def __init__(self, data_dir: str, output_dir: str = None):
        """
        初始化数据转换器
        
        Args:
            data_dir: 原始CSV数据目录
            output_dir: Parquet文件输出目录，默认为data_dir下的parquet_data子目录
        """
        self.data_dir = data_dir
        if output_dir is None:
            self.output_dir = os.path.join(data_dir, "parquet_data")
        else:
            self.output_dir = output_dir
        
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 创建一个临时的StockDataCollector实例来重用其数据处理方法
        self.collector = StockDataCollector(token="dummy_token", data_dir=data_dir)
    
    def load_stock_codes_from_json(self, stock_codes_path: str) -> List[str]:
        """从JSON文件中加载股票代码列表"""
        return self.collector.load_stock_codes_from_json(stock_codes_path)
    
    def process_market_data_to_parquet(self, 
                                       stock_codes: List[str],
                                       trade_calendar: pd.DataFrame) -> bool:
        """处理行情数据并保存为Parquet文件"""
        
        total_count = len(stock_codes)
        all_processed_data = []  # 收集所有处理后的数据

        no_adj_dir = os.path.join(self.data_dir, "market", "no_adj", "D")
        adj_factor_dir = os.path.join(self.data_dir, "market", "adj_factor")
        
        logger.info(f"开始处理 {total_count} 个股票的行情数据...")
        
        for i, ts_code in enumerate(stock_codes):
            if (i + 1) % 50 == 0 or i == 0 or i == total_count - 1:
                logger.info(f"进度: {i+1}/{total_count} - 正在处理股票: {ts_code}")
            
            try:
                # 读取未复权行情数据
                no_adj_file = os.path.join(no_adj_dir, f"{ts_code.replace('.', '_')}.csv")
                if not os.path.exists(no_adj_file):
                    continue
                
                # 读取复权因子数据
                adj_factor_file = os.path.join(adj_factor_dir, f"{ts_code.replace('.', '_')}.csv")
                if not os.path.exists(adj_factor_file):
                    continue
                
                # 读取数据
                quotes_df = pd.read_csv(no_adj_file)
                adj_factor_df = pd.read_csv(adj_factor_file)
                
                # 合并数据
                merged_df = pd.merge(
                    quotes_df, 
                    adj_factor_df[['ts_code', 'trade_date', 'adj_factor']], 
                    on=['ts_code', 'trade_date'], 
                    how='left'
                )
                
                if merged_df.empty:
                    continue
                
                # 完全复用collector的处理逻辑
                processed_data = self.collector._process_market_data_for_clickhouse(merged_df, trade_calendar)
                
                if not processed_data:
                    continue
                
                # 将处理后的数据添加到总列表中（替代插入ClickHouse）
                all_processed_data.extend(processed_data)
                
            except Exception as e:
                logger.error(f"✗ 处理股票 {ts_code} 时发生错误: {str(e)}")
                continue
        
        # 将所有数据转换为DataFrame并保存为HDF5
        if all_processed_data:
            df = pd.DataFrame(all_processed_data)
            
            # 移除ingest_time字段
            if 'ingest_time' in df.columns:
                df = df.drop('ingest_time', axis=1)
            
            output_path = os.path.join(self.output_dir, "market_data.parquet")
            
            # 转换数据类型以优化存储
            df = self._optimize_dataframe_dtypes(df, 'market')
            
            # 保存为Parquet
            df.to_parquet(output_path, compression='gzip', index=False)
            
            logger.info(f"✅ 行情数据已保存到: {output_path}")
            logger.info(f"总记录数: {len(df)}")
            logger.info(f"文件大小: {self._get_file_size(output_path)}")
            return True
        else:
            logger.warning("⚠️  没有行情数据被处理")
            return False
    
    def process_cyq_data_to_parquet(self, stock_codes: List[str]) -> bool:
        """处理筹码数据并保存为Parquet文件"""
        
        total_count = len(stock_codes)
        all_processed_data = []  # 收集所有处理后的数据

        cyq_perf_dir = os.path.join(self.data_dir, "special", "cyq_perf")
        
        if not os.path.exists(cyq_perf_dir):
            logger.warning(f"⚠️  筹码数据目录不存在: {cyq_perf_dir}")
            return False
        
        logger.info(f"开始处理 {total_count} 个股票的筹码数据...")
        
        for i, ts_code in enumerate(stock_codes):
            if (i + 1) % 50 == 0 or i == 0 or i == total_count - 1:
                logger.info(f"进度: {i+1}/{total_count} - 正在处理股票: {ts_code}")
            
            try:
                # 读取筹码数据
                cyq_perf_file = os.path.join(cyq_perf_dir, f"{ts_code.replace('.', '_')}.csv")
                if not os.path.exists(cyq_perf_file):
                    continue
                
                # 读取数据
                cyq_df = pd.read_csv(cyq_perf_file)
                
                if cyq_df.empty:
                    continue
                
                # 完全复用collector的处理逻辑
                processed_data = self.collector._process_cyq_data_for_clickhouse(cyq_df)
                
                if not processed_data:
                    continue
                
                # 将处理后的数据添加到总列表中（替代插入ClickHouse）
                all_processed_data.extend(processed_data)
                
            except Exception as e:
                logger.error(f"✗ 处理股票 {ts_code} 筹码数据时发生错误: {str(e)}")
                continue
        
        # 将所有数据转换为DataFrame并保存为HDF5
        if all_processed_data:
            df = pd.DataFrame(all_processed_data)
            
            # 移除ingest_time字段
            if 'ingest_time' in df.columns:
                df = df.drop('ingest_time', axis=1)
            
            output_path = os.path.join(self.output_dir, "cyq_data.parquet")
            
            # 转换数据类型以优化存储
            df = self._optimize_dataframe_dtypes(df, 'cyq')
            
            # 保存为Parquet
            df.to_parquet(output_path, compression='gzip', index=False)
            
            logger.info(f"✅ 筹码数据已保存到: {output_path}")
            logger.info(f"总记录数: {len(df)}")
            logger.info(f"文件大小: {self._get_file_size(output_path)}")
            return True
        else:
            logger.warning("⚠️  没有筹码数据被处理")
            return False
    
    def process_moneyflow_data_to_parquet(self, stock_codes: List[str]) -> bool:
        """处理资金流向数据并保存为Parquet文件"""
        
        total_count = len(stock_codes)
        all_processed_data = []  # 收集所有处理后的数据

        moneyflow_dir = os.path.join(self.data_dir, "moneyflow", "moneyflow")
        
        if not os.path.exists(moneyflow_dir):
            logger.warning(f"⚠️  资金流向数据目录不存在: {moneyflow_dir}")
            return False
        
        logger.info(f"开始处理 {total_count} 个股票的资金流向数据...")
        
        for i, ts_code in enumerate(stock_codes):
            if (i + 1) % 50 == 0 or i == 0 or i == total_count - 1:
                logger.info(f"进度: {i+1}/{total_count} - 正在处理股票: {ts_code}")
            
            try:
                # 读取资金流向数据
                moneyflow_file = os.path.join(moneyflow_dir, f"{ts_code.replace('.', '_')}.csv")
                if not os.path.exists(moneyflow_file):
                    continue
                
                # 读取数据
                moneyflow_df = pd.read_csv(moneyflow_file)
                
                if moneyflow_df.empty:
                    continue
                
                # 完全复用collector的处理逻辑
                processed_data = self.collector._process_moneyflow_data_for_clickhouse(moneyflow_df)
                
                if not processed_data:
                    continue
                
                # 将处理后的数据添加到总列表中（替代插入ClickHouse）
                all_processed_data.extend(processed_data)
                
            except Exception as e:
                logger.error(f"✗ 处理股票 {ts_code} 资金流向数据时发生错误: {str(e)}")
                continue
        
        # 将所有数据转换为DataFrame并保存为HDF5
        if all_processed_data:
            df = pd.DataFrame(all_processed_data)
            
            # 移除ingest_time字段
            if 'ingest_time' in df.columns:
                df = df.drop('ingest_time', axis=1)
            
            output_path = os.path.join(self.output_dir, "moneyflow_data.parquet")
            
            # 转换数据类型以优化存储
            df = self._optimize_dataframe_dtypes(df, 'moneyflow')
            
            # 保存为Parquet
            df.to_parquet(output_path, compression='gzip', index=False)
            
            logger.info(f"✅ 资金流向数据已保存到: {output_path}")
            logger.info(f"总记录数: {len(df)}")
            logger.info(f"文件大小: {self._get_file_size(output_path)}")
            return True
        else:
            logger.warning("⚠️  没有资金流向数据被处理")
            return False
    
    def process_combined_data_to_parquet(self, 
                                        market_df: pd.DataFrame,
                                        cyq_df: pd.DataFrame = None,
                                        moneyflow_df: pd.DataFrame = None) -> bool:
        """处理综合数据（行情+筹码+资金流）并保存为Parquet文件"""
        
        logger.info("开始合并综合数据...")
        
        if market_df.empty:
            logger.warning("⚠️  行情数据为空，无法生成综合数据")
            return False
        
        # 以行情数据为基准进行合并
        combined_df = market_df.copy()
        
        # 合并筹码数据
        if cyq_df is not None and not cyq_df.empty:
            logger.info("合并筹码数据...")
            combined_df = combined_df.merge(cyq_df, on=['instrument_id', 'trade_date'], how='left')
            logger.info(f"合并后记录数: {len(combined_df)}")
        else:
            logger.warning("⚠️  筹码数据为空，跳过合并")
        
        # 合并资金流数据
        if moneyflow_df is not None and not moneyflow_df.empty:
            logger.info("合并资金流数据...")
            combined_df = combined_df.merge(moneyflow_df, on=['instrument_id', 'trade_date'], how='left')
            logger.info(f"合并后记录数: {len(combined_df)}")
        else:
            logger.warning("⚠️  资金流数据为空，跳过合并")
        
        # 保存综合数据
        output_path = os.path.join(self.output_dir, "combined_data.parquet")
        
        # 转换数据类型以优化存储
        combined_df = self._optimize_dataframe_dtypes(combined_df, 'combined')
        
        # 保存为Parquet
        combined_df.to_parquet(output_path, compression='gzip', index=False)
        
        logger.info(f"✅ 综合数据已保存到: {output_path}")
        logger.info(f"总记录数: {len(combined_df)}")
        logger.info(f"文件大小: {self._get_file_size(output_path)}")
        return True
    
    def process_all_data(self, stock_codes_path: str, trade_calendar_path: str) -> Dict[str, bool]:
        """处理所有数据并保存为Parquet文件"""
        
        # 加载股票代码
        stock_codes = self.load_stock_codes_from_json(stock_codes_path)
        logger.info(f"加载了 {len(stock_codes)} 个股票代码")
        
        # 加载交易日历
        if not trade_calendar_path or not os.path.exists(trade_calendar_path):
            logger.error("错误: 必须提供有效的交易日历文件路径")
            raise ValueError("计算change和return必须提供交易日历")
            
        try:
            logger.info(f"正在加载交易日历: {trade_calendar_path}")
            trade_calendar = pd.read_csv(trade_calendar_path)
            logger.info(f"成功加载交易日历，共 {len(trade_calendar)} 条记录")
        except Exception as e:
            logger.error(f"加载交易日历失败: {str(e)}")
            raise
        
        results = {}
        
        # 用于存储处理后的DataFrame，以便后续合并
        market_df = None
        cyq_df = None
        moneyflow_df = None
        
        # 处理行情数据
        logger.info("\n" + "=" * 50)
        logger.info("处理行情数据")
        logger.info("=" * 50)
        results['market'] = self.process_market_data_to_parquet(stock_codes, trade_calendar)
        
        # 读取刚生成的行情数据用于后续合并
        market_file = os.path.join(self.output_dir, "market_data.parquet")
        if os.path.exists(market_file):
            market_df = pd.read_parquet(market_file)
            logger.info(f"行情数据DataFrame准备完成: {market_df.shape}")
        
        # 处理筹码数据
        logger.info("\n" + "=" * 50)
        logger.info("处理筹码数据")
        logger.info("=" * 50)
        results['cyq'] = self.process_cyq_data_to_parquet(stock_codes)
        
        # 读取刚生成的筹码数据用于后续合并
        cyq_file = os.path.join(self.output_dir, "cyq_data.parquet")
        if os.path.exists(cyq_file):
            cyq_df = pd.read_parquet(cyq_file)
            logger.info(f"筹码数据DataFrame准备完成: {cyq_df.shape}")
        
        # 处理资金流向数据
        logger.info("\n" + "=" * 50)
        logger.info("处理资金流向数据")
        logger.info("=" * 50)
        results['moneyflow'] = self.process_moneyflow_data_to_parquet(stock_codes)
        
        # 读取刚生成的资金流数据用于后续合并
        moneyflow_file = os.path.join(self.output_dir, "moneyflow_data.parquet")
        if os.path.exists(moneyflow_file):
            moneyflow_df = pd.read_parquet(moneyflow_file)
            logger.info(f"资金流数据DataFrame准备完成: {moneyflow_df.shape}")
        
        # 处理综合数据
        logger.info("\n" + "=" * 50)
        logger.info("处理综合数据（行情+筹码+资金流）")
        logger.info("=" * 50)
        results['combined'] = self.process_combined_data_to_parquet(market_df, cyq_df, moneyflow_df)
        
        return results
    
    def _optimize_dataframe_dtypes(self, df: pd.DataFrame, data_type: str) -> pd.DataFrame:
        """按照ClickHouse schema严格转换DataFrame的数据类型"""
        df = df.copy()
        
        # 通用字段：所有表都有的字段
        if 'instrument_id' in df.columns:
            # FixedString(9) -> str
            df['instrument_id'] = df['instrument_id'].astype(str)
        
        if 'trade_date' in df.columns:
            # Date -> datetime64[ns]
            df['trade_date'] = pd.to_datetime(df['trade_date'])
        
        # 根据表类型按照schema进行转换
        if data_type == 'market':
            # daily_quotes表的schema
            # Decimal32(4) 类型字段 -> float32
            decimal32_4_cols = ['open', 'high', 'low', 'close']
            for col in decimal32_4_cols:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce').astype('float32')
            
            # UInt64 类型字段 -> uint64 (非nullable，用普通int64即可)
            if 'volume' in df.columns:
                df['volume'] = pd.to_numeric(df['volume'], errors='coerce').fillna(0).astype('int64')
            
            # Decimal64(2) 类型字段 -> float64
            if 'amount' in df.columns:
                df['amount'] = pd.to_numeric(df['amount'], errors='coerce').astype('float64')
            
            # Nullable(Decimal64(8)) 类型字段 -> float64 (支持NaN)
            if 'change' in df.columns:
                df['change'] = pd.to_numeric(df['change'], errors='coerce').astype('float64')
            
            # Nullable(Decimal32(4)) 类型字段 -> float32 (支持NaN)
            if 'pct_chg' in df.columns:
                df['pct_chg'] = pd.to_numeric(df['pct_chg'], errors='coerce').astype('float32')
            
            # Nullable(Decimal64(6)) 类型字段 -> float64 (支持NaN)
            if 'adj_factor' in df.columns:
                df['adj_factor'] = pd.to_numeric(df['adj_factor'], errors='coerce').astype('float64')
        
        elif data_type == 'cyq':
            # daily_chips表的schema
            # 所有价格字段都是 Nullable(Decimal32(4)) -> float32 (支持NaN)
            nullable_decimal32_4_cols = ['his_low', 'his_high', 'cost_5pct', 'cost_15pct', 
                                        'cost_50pct', 'cost_85pct', 'cost_95pct', 'weight_avg']
            for col in nullable_decimal32_4_cols:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce').astype('float32')
            
            # Nullable(Decimal32(2)) 类型字段 -> float32 (支持NaN)
            if 'winner_rate' in df.columns:
                df['winner_rate'] = pd.to_numeric(df['winner_rate'], errors='coerce').astype('float32')
        
        elif data_type == 'moneyflow':
            # daily_money_flow表的schema
            # Nullable(Int64) 类型字段 -> Int64 (pandas的nullable integer)
            nullable_int64_cols = ['buy_sm_vol', 'sell_sm_vol', 'buy_md_vol', 'sell_md_vol',
                                  'buy_lg_vol', 'sell_lg_vol', 'buy_elg_vol', 'sell_elg_vol', 'net_mf_vol']
            for col in nullable_int64_cols:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce').astype('Int64')
            
            # Nullable(Decimal64(2)) 类型字段 -> float64 (支持NaN)
            nullable_decimal64_2_cols = ['buy_sm_amount', 'sell_sm_amount', 'buy_md_amount', 'sell_md_amount',
                                        'buy_lg_amount', 'sell_lg_amount', 'buy_elg_amount', 'sell_elg_amount', 'net_mf_amount']
            for col in nullable_decimal64_2_cols:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce').astype('float64')
        
        elif data_type == 'combined':
            # 综合数据：递归调用各表的处理逻辑
            df = self._optimize_dataframe_dtypes(df, 'market')
            df = self._optimize_dataframe_dtypes(df, 'cyq') 
            df = self._optimize_dataframe_dtypes(df, 'moneyflow')
        
        return df
    
    def _get_file_size(self, filepath: str) -> str:
        """获取文件大小的可读格式"""
        size_bytes = os.path.getsize(filepath)
        if size_bytes == 0:
            return "0B"
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = int(np.floor(np.log(size_bytes) / np.log(1024)))
        p = np.power(1024, i)
        s = round(size_bytes / p, 2)
        return f"{s} {size_names[i]}"

   


def main():
    parser = argparse.ArgumentParser(description='将股票数据转换为Parquet格式')
    
    # 必需参数
    parser.add_argument('--stock_codes_path', type=str, required=True, 
                       help='股票代码列表文件路径')
    parser.add_argument('--data_dir', type=str, required=True, 
                       help='原始CSV数据目录（STOCK_DATA_DIR）')
    parser.add_argument('--trade_calendar_path', type=str, required=True, 
                       help='交易日历文件路径')
    
    # 可选参数
    parser.add_argument('--output_dir', type=str, 
                       help='Parquet文件输出目录，默认为数据目录下的parquet_data子目录')
    
    args = parser.parse_args()
    
    # 创建转换器实例
    converter = StockDataToParquet(data_dir=args.data_dir, output_dir=args.output_dir)
    
    logger.info("=" * 80)
    logger.info("股票数据转换为Parquet格式")
    logger.info("=" * 80)
    logger.info(f"数据目录: {args.data_dir}")
    logger.info(f"输出目录: {converter.output_dir}")
    logger.info(f"股票代码文件: {args.stock_codes_path}")
    logger.info(f"交易日历文件: {args.trade_calendar_path}")
    
    try:
        # 处理所有数据
        results = converter.process_all_data(args.stock_codes_path, args.trade_calendar_path)
        
        # 输出结果总结
        logger.info("\n" + "=" * 80)
        logger.info("数据转换完成！")
        logger.info("=" * 80)
        
        success_count = sum(1 for success in results.values() if success)
        total_count = len(results)
        
        for data_type, success in results.items():
            status = "✅ 成功" if success else "❌ 失败"
            filename = f"{data_type}_data.parquet"
            filepath = os.path.join(converter.output_dir, filename)
            if success and os.path.exists(filepath):
                file_size = converter._get_file_size(filepath)
                logger.info(f"{data_type.ljust(10)} : {status} - {filename} ({file_size})")
            else:
                logger.info(f"{data_type.ljust(10)} : {status}")
        
        logger.info(f"\n成功转换: {success_count}/{total_count} 种数据类型")
        logger.info(f"输出目录: {converter.output_dir}")
        
        if success_count > 0:
            logger.info("\n生成的文件:")
            for filename in ['market_data.parquet', 'cyq_data.parquet', 'moneyflow_data.parquet', 'combined_data.parquet']:
                filepath = os.path.join(converter.output_dir, filename)
                if os.path.exists(filepath):
                    logger.info(f"  - {filename}")
    
    except Exception as e:
        logger.error(f"❌ 执行过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main() 