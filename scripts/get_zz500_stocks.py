#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
获取2013-01-01到2025-06-16期间所有中证500股票代码的脚本
将股票代码格式转换并去重后保存为JSON文件
采用分步下载策略，增加容错性
"""

import baostock as bs
import pandas as pd
import json
import os
from datetime import datetime, timed<PERSON>ta
from typing import Set, List
import time


def convert_stock_code(original_code: str) -> str:
    """
    将股票代码从 'sh.600001' 格式转换为 '600001.SH' 格式
    
    Args:
        original_code: 原始代码格式，如 'sh.600001' 或 'sz.000001'
        
    Returns:
        转换后的代码格式，如 '600001.SH' 或 '000001.SZ'
    """
    if '.' not in original_code:
        return original_code
    
    exchange, code = original_code.split('.')
    
    if exchange.lower() == 'sh':
        return f"{code}.SH"
    elif exchange.lower() == 'sz':
        return f"{code}.SZ"
    else:
        return original_code


def get_zz500_stocks_by_date(date_str: str = None) -> List[str]:
    """
    获取指定日期的中证500成分股
    
    Args:
        date_str: 日期字符串，格式YYYY-MM-DD，为空时获取最新
        
    Returns:
        股票代码列表
    """
    try:
        # 查询中证500成分股
        rs = bs.query_zz500_stocks(date=date_str)
        
        if rs.error_code != '0':
            print(f"查询日期 {date_str} 时出错: {rs.error_msg}")
            return []
        
        # 收集数据
        zz500_stocks = []
        while rs.next():
            row_data = rs.get_row_data()
            if len(row_data) >= 2:  # 确保有足够的字段
                stock_code = row_data[1]  # code字段
                converted_code = convert_stock_code(stock_code)
                zz500_stocks.append(converted_code)
        
        print(f"日期 {date_str}: 获取到 {len(zz500_stocks)} 只股票")
        return zz500_stocks
        
    except Exception as e:
        print(f"获取日期 {date_str} 的数据时发生错误: {str(e)}")
        return []


def save_daily_data(date_str: str, stock_codes: List[str], data_dir: str = "zz500_daily_data"):
    """
    保存单日的股票代码数据到文件
    
    Args:
        date_str: 日期字符串
        stock_codes: 股票代码列表
        data_dir: 数据保存目录
    """
    os.makedirs(data_dir, exist_ok=True)
    
    # 文件名格式：zz500_YYYY-MM-DD.json
    filename = f"zz500_{date_str}.json"
    filepath = os.path.join(data_dir, filename)
    
    data = {
        "date": date_str,
        "count": len(stock_codes),
        "stock_codes": stock_codes,
        "downloaded_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    
    with open(filepath, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    
    print(f"  -> 已保存到: {filepath}")


def load_daily_data(data_dir: str = "zz500_daily_data") -> dict:
    """
    加载所有已下载的每日数据
    
    Args:
        data_dir: 数据目录
        
    Returns:
        dict: {date: [stock_codes]} 的字典
    """
    if not os.path.exists(data_dir):
        return {}
    
    daily_data = {}
    files = [f for f in os.listdir(data_dir) if f.startswith('zz500_') and f.endswith('.json')]
    
    for filename in sorted(files):
        filepath = os.path.join(data_dir, filename)
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
                daily_data[data['date']] = data['stock_codes']
        except Exception as e:
            print(f"读取文件 {filename} 时出错: {str(e)}")
    
    return daily_data


def generate_date_list(start_date: str, end_date: str, interval_days: int = 30) -> List[str]:
    """
    生成日期列表，用于查询历史数据
    
    Args:
        start_date: 开始日期，格式YYYY-MM-DD
        end_date: 结束日期，格式YYYY-MM-DD
        interval_days: 查询间隔天数，默认30天（确保不遗漏任何调整）
        
    Returns:
        日期字符串列表
    """
    start = datetime.strptime(start_date, '%Y-%m-%d')
    end = datetime.strptime(end_date, '%Y-%m-%d')
    
    dates = []
    current = start
    
    while current <= end:
        dates.append(current.strftime('%Y-%m-%d'))
        current += timedelta(days=interval_days)
    
    # 确保包含结束日期
    if dates[-1] != end_date:
        dates.append(end_date)
    
    return dates


def download_phase(start_date: str, end_date: str, interval_days: int, data_dir: str):
    """
    下载阶段：获取所有历史数据并分别保存
    
    Args:
        start_date: 开始日期，格式YYYYMMDD
        end_date: 结束日期，格式YYYYMMDD  
        interval_days: 查询间隔天数
        data_dir: 数据保存目录
    """
    print("=" * 60)
    print("阶段1: 下载历史数据")
    print("=" * 60)
    
    # 登录baostock
    lg = bs.login()
    if lg.error_code != '0':
        print(f"登录失败: {lg.error_msg}")
        return False
    
    print("baostock登录成功")
    
    try:
        # 生成查询日期列表
        start_date = datetime.strptime(start_date, '%Y%m%d').strftime('%Y-%m-%d')
        end_date = datetime.strptime(end_date, '%Y%m%d').strftime('%Y-%m-%d')
        
        query_dates = generate_date_list(start_date, end_date, interval_days)
        print(f"将查询 {len(query_dates)} 个时间点的数据")
        
        # 检查已下载的数据
        existing_data = load_daily_data(data_dir)
        print(f"已存在 {len(existing_data)} 个日期的数据文件")
        
        # 只下载未完成的日期
        remaining_dates = [date for date in query_dates if date not in existing_data]
        
        if not remaining_dates:
            print("所有日期的数据都已下载完成！")
            return True
        
        print(f"需要下载 {len(remaining_dates)} 个日期的数据")
        
        # 逐个日期下载并保存
        for i, date in enumerate(remaining_dates, 1):
            print(f"\n进度 {i}/{len(remaining_dates)}: 下载日期 {date}")
            
            try:
                stocks = get_zz500_stocks_by_date(date)
                if stocks:
                    save_daily_data(date, stocks, data_dir)
                else:
                    print(f"  -> 日期 {date} 未获取到数据，跳过")
                
                # 适当延时避免请求过于频繁
                time.sleep(0.2)
                
            except Exception as e:
                print(f"  -> 下载日期 {date} 时发生错误: {str(e)}")
                print("  -> 建议稍后重新运行脚本继续下载")
                
        return True
        
    except Exception as e:
        print(f"下载阶段发生错误: {str(e)}")
        return False
        
    finally:
        # 登出系统
        bs.logout()
        print("baostock已登出")


def merge_phase(data_dir: str, output_file: str):
    """
    合并阶段：将所有下载的数据合并去重
    
    Args:
        data_dir: 数据保存目录
        output_file: 输出文件路径
    """
    print("\n" + "=" * 60)
    print("阶段2: 合并数据")
    print("=" * 60)
    
    # 加载所有每日数据
    daily_data = load_daily_data(data_dir)
    
    if not daily_data:
        print("未找到任何下载的数据文件！")
        return False
    
    print(f"找到 {len(daily_data)} 个日期的数据文件")
    
    # 用集合存储所有股票代码（自动去重）
    all_stock_codes: Set[str] = set()
    
    # 合并所有数据
    for date, codes in daily_data.items():
        all_stock_codes.update(codes)
        print(f"日期 {date}: {len(codes)} 只股票，累计去重后: {len(all_stock_codes)} 只")
    
    # 转换为排序后的列表
    final_stock_list = sorted(list(all_stock_codes))
    
    print(f"\n合并完成!")
    print(f"覆盖日期范围: {min(daily_data.keys())} 到 {max(daily_data.keys())}")
    print(f"总计去重后股票数量: {len(final_stock_list)}")
    
    output_data = {
        "description": f"中证500成分股历史汇总 ({min(daily_data.keys())} 到 {max(daily_data.keys())})",
        "date_range": {
            "start": min(daily_data.keys()),
            "end": max(daily_data.keys())
        },
        "query_dates_count": len(daily_data),
        "total_count": len(final_stock_list),
        "stock_codes": final_stock_list,
        "generated_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n最终结果已保存到: {output_file}")
    
    # 显示部分结果预览
    print(f"\n股票代码预览（前20个）:")
    for i, code in enumerate(final_stock_list[:20], 1):
        print(f"  {i:2d}. {code}")
    
    if len(final_stock_list) > 20:
        print(f"  ... 还有 {len(final_stock_list) - 20} 个")
    
    return True


def main(args):
    """
    主函数
    
    Args:
        args: 命令行参数对象，包含start_date、end_date、interval_days、data_dir、output_file
    """
    print("中证500股票代码获取工具")
    print("采用分阶段下载策略，增加容错性")
    print(f"参数配置：开始日期={args.start_date}, 结束日期={args.end_date}, 间隔={args.interval_days}天")
    
    # 阶段1：下载数据
    if not download_phase(args.start_date, args.end_date, args.interval_days, args.data_dir):
        print("\n下载阶段失败，程序退出")
        return
    
    # 阶段2：合并数据
    if not merge_phase(args.data_dir, args.output_file):
        print("\n合并阶段失败，程序退出")
        return
    
    print("\n" + "=" * 60)
    print("全部完成！")
    print("=" * 60)


if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description='获取中证500股票代码')
    parser.add_argument('--start_date', type=str, default='20130101', help='开始日期')
    parser.add_argument('--end_date', type=str, default='20250616', help='结束日期')
    parser.add_argument('--interval_days', type=int, default=30, help='查询间隔天数')
    parser.add_argument('--data_dir', type=str, default='zz500_daily_data', help='数据保存目录')
    parser.add_argument('--output_file', type=str, default='zz500_all_stocks_2013_2025.json', help='输出文件名')
    args = parser.parse_args()
    # 打印参数
    print(f"参数配置：开始日期={args.start_date}, 结束日期={args.end_date}, 间隔={args.interval_days}天")
    print(f"数据保存目录: {args.data_dir}")
    print(f"输出文件名: {args.output_file}")
    
    main(args) 
