# set env from .env_sh
source .env_sh

export PYTHONPATH=$PYTHONPATH:$(pwd)
export TUSHARE_TOKEN=$(echo $TUSHARE_TOKEN)

# get params from env
CLICKHOUSE_HOST=$(echo $CLICKHOUSE_HOST)
CLICKHOUSE_PORT=$(echo $CLICKHOUSE_PORT)
CLICKHOUSE_USER=$(echo $CLICKHOUSE_USER)
CLICKHOUSE_PASSWORD=$(echo $CLICKHOUSE_PASSWORD)
CLICKHOUSE_DATABASE=$(echo $CLICKHOUSE_DATABASE)

STOCK_DATA_DIR=$(echo $STOCK_DATA_DIR)
CACHE_DIR=$(echo $CACHE_DIR)
STOCK_CODES_PATH=$(echo $STOCK_CODES_PATH)

END_DATE=20250722

echo "variables:"
echo "--------------------------------"
echo "CLICKHOUSE_HOST: $CLICKHOUSE_HOST"
echo "CLICKHOUSE_PORT: $CLICKHOUSE_PORT"
echo "CLICKHOUSE_USER: $CLICKHOUSE_USER"
echo "CLICKHOUSE_DATABASE: $CLICKHOUSE_DATABASE"
echo "STOCK_DATA_DIR: $STOCK_DATA_DIR"
echo "CACHE_DIR: $CACHE_DIR"
echo "STOCK_CODES_PATH: $STOCK_CODES_PATH"
echo "--------------------------------"

# create stock data directory if not exists
if [ ! -d "$STOCK_DATA_DIR" ]; then
    mkdir -p $STOCK_DATA_DIR
fi

# create cache directory if not exists
if [ ! -d "$CACHE_DIR" ]; then
    mkdir -p $CACHE_DIR
fi



# # create table daily_quotes
# python utils/execute_sql.py \
#    --sql_script_path schema/stock_5_min.sql \
#    --host $CLICKHOUSE_HOST \
#    --port $CLICKHOUSE_PORT \
#    --user $CLICKHOUSE_USER \
#    --password $CLICKHOUSE_PASSWORD \
#    --database $CLICKHOUSE_DATABASE

# # download zz1000 5 min market data
# python stock_collect_main.py \
#   --stage download_minutes \
#   --frequency 5 \
#   --stock_codes_path $STOCK_CODES_PATH \
#   --data_dir $STOCK_DATA_DIR \
#   --start_date 20180101 \
#   --end_date $END_DATE


# import zz1000 5 min market data
python stock_collect_main.py \
  --stage import \
  --import_type minutes \
  --stock_codes_path $STOCK_CODES_PATH \
  --data_dir $STOCK_DATA_DIR \
  --clickhouse_host $CLICKHOUSE_HOST \
  --clickhouse_port $CLICKHOUSE_PORT \
  --clickhouse_user $CLICKHOUSE_USER \
  --clickhouse_password $CLICKHOUSE_PASSWORD \
  --clickhouse_database $CLICKHOUSE_DATABASE \
  --table_name stock_5_min \
  --batch_size 3000

 