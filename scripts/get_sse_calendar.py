#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
上交所交易日历获取脚本
使用tushare pro获取交易日历数据
"""

import argparse
import os
import sys
import pandas as pd
import tushare as ts
from datetime import datetime, timedelta, UTC

from dotenv import load_dotenv
import pytz
from utils.sse_calendar import get_trade_calendar

# 加载环境变量
load_dotenv()


def get_beijing_yesterday() -> str:
    """
    获取北京时间前一天，格式YYYYMMDD
    
    Returns:
        str: 前一天日期，格式YYYYMMDD
    """
    try:
        
        # 获取北京时间
        beijing_tz = pytz.timezone('Asia/Shanghai')
        beijing_now = datetime.now(beijing_tz)
    except ImportError:
        # 如果没有pytz，使用UTC+8小时近似
        beijing_now = datetime.now(UTC) + timedelta(hours=8)
    
    # 获取前一天
    yesterday = beijing_now - timedelta(days=1)
    
    return yesterday.strftime('%Y%m%d')

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='获取上交所交易日历数据',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 获取2024年全年交易日历
  python get_sse_calendar.py --start_date 20240101 --end_date 20241231 --output_file calendar_2024.csv
  
  # 获取最近一年的交易日历
  python get_sse_calendar.py --output_file recent_calendar.csv
  
  # 获取深交所交易日历
  python get_sse_calendar.py --exchange SZSE --start_date 20240101 --end_date 20241231
        """
    )
    
    # 添加参数
    parser.add_argument(
        '--exchange',
        type=str,
        default='SSE',
        choices=['SSE', 'SZSE', 'CFFEX', 'SHFE', 'CZCE', 'DCE', 'INE'],
        help='交易所代码 (默认: SSE上交所)'
    )
    
    parser.add_argument(
        '--start_date',
        type=str,
        help='开始日期，格式YYYYMMDD (默认: 一年前)'
    )
    
    parser.add_argument(
        '--end_date',
        type=str,
        help='结束日期，格式YYYYMMDD (默认: 昨天)'
    )

    parser.add_argument(
        '--is_open',
        type=str,
        default='1',
        help='是否交易，1表示交易，0表示不交易，默认1'
    )
    
    parser.add_argument(
        '--output_file',
        type=str,
        help='输出CSV文件路径'
    )
    
    # 解析参数
    args = parser.parse_args()
    
    # 设置默认日期范围（最近一年）
    if not args.start_date:
        start_date = (datetime.now() - timedelta(days=365)).strftime('%Y%m%d')
    else:
        start_date = args.start_date
        
    if not args.end_date:
        end_date = get_beijing_yesterday()
    else:
        end_date = args.end_date
    
    # 验证日期格式
    try:
        datetime.strptime(start_date, '%Y%m%d')
        datetime.strptime(end_date, '%Y%m%d')
    except ValueError:
        print("错误: 日期格式不正确，请使用YYYYMMDD格式")
        sys.exit(1)
    
    # 设置默认输出文件
    output_file = args.output_file
    if not output_file:
        output_file = f"trade_calendar_{args.exchange}_{start_date}_{end_date}.csv"
        print(f"未指定输出文件，将保存为: {output_file}")
    
    # 获取数据
    df = get_trade_calendar(
        exchange=args.exchange,
        start_date=start_date,
        end_date=end_date,
        is_open=args.is_open,
        output_file=output_file
    )
    
    if df is not None:
        print("\n✅ 任务完成")
    else:
        print("\n❌ 任务失败")
        sys.exit(1)


if __name__ == '__main__':
    main()
