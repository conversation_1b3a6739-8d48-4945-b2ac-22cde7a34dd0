# set env from .env_sh
source .env_sh

export PYTHONPATH=$PYTHONPATH:$(pwd)
export TUSHARE_TOKEN=$(echo $TUSHARE_TOKEN)

# get params from env
CLICKHOUSE_HOST=$(echo $CLICKHOUSE_HOST)
CLICKHOUSE_PORT=$(echo $CLICKHOUSE_PORT)
CLICKHOUSE_USER=$(echo $CLICKHOUSE_USER)
CLICKHOUSE_PASSWORD=$(echo $CLICKHOUSE_PASSWORD)
CLICKHOUSE_DATABASE=$(echo $CLICKHOUSE_DATABASE)

STOCK_DATA_DIR=$(echo $STOCK_DATA_DIR)
CACHE_DIR=$(echo $CACHE_DIR)
STOCK_CODES_PATH=$(echo $STOCK_CODES_PATH)

echo "variables:"
echo "--------------------------------"
echo "CLICKHOUSE_HOST: $CLICKHOUSE_HOST"
echo "CLICKHOUSE_PORT: $CLICKHOUSE_PORT"
echo "CLICKHOUSE_USER: $CLICKHOUSE_USER"
echo "CLICKHOUSE_DATABASE: $CLICKHOUSE_DATABASE"
echo "STOCK_DATA_DIR: $STOCK_DATA_DIR"
echo "CACHE_DIR: $CACHE_DIR"
echo "STOCK_CODES_PATH: $STOCK_CODES_PATH"
echo "--------------------------------"

# create stock data directory if not exists
if [ ! -d "$STOCK_DATA_DIR" ]; then
    mkdir -p $STOCK_DATA_DIR
fi

# create cache directory if not exists
if [ ! -d "$CACHE_DIR" ]; then
    mkdir -p $CACHE_DIR
fi

END_DATE=20250722

# # create table daily_quotes
# python utils/execute_sql.py \
#    --sql_script_path schema/clickhouse/daily_sector_quotes.sql \
#    --host $CLICKHOUSE_HOST \
#    --port $CLICKHOUSE_PORT \
#    --user $CLICKHOUSE_USER \
#    --password $CLICKHOUSE_PASSWORD \
#    --database $CLICKHOUSE_DATABASE


# # download trade calendar
# python scripts/get_sse_calendar.py \
#     --start_date 20121201 \
#     --exchange SSE \
#     --is_open 1 \
#     --output_file $CACHE_DIR/trade_calendar.csv

# # download ths sectors data
# python stock_collect_main.py \
#   --stage download_sector \
#   --data_dir $STOCK_DATA_DIR \
#   --start_date 20130101 \
#   --end_date $END_DATE

# import zz500 market data
python stock_collect_main.py \
  --stage import \
  --import_type sector \
  --stock_codes_path $CACHE_DIR/sector_codes.json \
  --data_dir $STOCK_DATA_DIR \
  --clickhouse_host $CLICKHOUSE_HOST \
  --clickhouse_port $CLICKHOUSE_PORT \
  --clickhouse_user $CLICKHOUSE_USER \
  --clickhouse_password $CLICKHOUSE_PASSWORD \
  --clickhouse_database $CLICKHOUSE_DATABASE \
  --table_name daily_sector_quotes \
  --batch_size 1000 \
  --trade_calendar_path $CACHE_DIR/trade_calendar.csv