import json
import baostock as bs
import pandas as pd
from tqdm import tqdm
import os
from datetime import datetime
from typing import List
import logging
import numpy as np
import pandas as pd


[
       {
            'id': 2,
            'strategy_id': 10,
            'trade_date': '2025-07-22',
            'generated_time': '2025-07-22 00:12:20',
            'stock_code': '603938.SH',
            'order_type': 23,
            'order_volume': 200,
            'last_close_price': 38.45
        }
]

[
    {
        'strategy_name': '策略1',
        'timestamp': '2025-07-22 00:00:00',
        'generated_time': '2025-07-22 00:12:20',
        'stock_code': '603938.SH',
        'order_type': 23,
        'order_volume': 500,
        'last_close_price': 38.45
    }
]

[
    {
        'strategy_name': '策略1',
        'strategy_desc': '策略1的描述',
        'factor_names': ['中小单净流入-筹码因子'],
        'factor_expressions': ["RANK(($buy_sm_vol + $buy_md_vol - $sell_sm_vol - $sell_md_vol) / ($amount + 1e-8))"],
        'extra_params': {
            'start_cash': 1000000,
            'update_freq': 5
        }
    }
]

[
    {
        'id': 10,
        'strategy_name': '策略1',
        'strategy_desc': '策略1的描述',
        'factor_names': 'this is a string',
        'factor_expressions': 'this is a string',
        'extra_params': 'this is a string'
    }
]