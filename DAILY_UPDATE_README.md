# 每日数据更新功能使用说明

## 功能概述

每日数据更新功能可以自动获取最新的股票行情数据并导入到ClickHouse数据库中。该功能会：

1. 自动获取北京时间前一天的日期
2. 查询数据库中的最新数据日期
3. 确定需要更新的交易日期
4. 从tushare获取行情数据和复权因子
5. 计算涨跌额和涨跌幅
6. 直接导入到ClickHouse数据库

## 环境变量设置

使用前需要设置以下环境变量：

```bash
# Tushare Pro Token
export TUSHARE_TOKEN="your_tushare_token"

# 股票代码列表文件路径
export STOCK_CODES_PATH="path/to/stock_codes.json"
```

## 使用方法

### 1. 命令行方式

```bash
python stock_collector.py --stage daily_update \
  --data_dir ./stock_data \
  --clickhouse_host localhost \
  --clickhouse_port 8123 \
  --clickhouse_user default \
  --clickhouse_password "" \
  --clickhouse_database default
```

### 2. 代码方式

```python
from stock_collector import StockDataCollector, DailyUpdateManager

# 创建收集器实例（需要数据库配置）
db_config = {
    "host": "localhost",
    "port": 8123,
    "user": "default", 
    "password": "",
    "database": "default"
}

collector = StockDataCollector(
    token="your_tushare_token", 
    data_dir="./stock_data",
    db_config=db_config
)

# 创建每日更新管理器
daily_manager = DailyUpdateManager(collector)

# 执行每日更新
result = daily_manager.daily_update_market_data()
print(result)
```

### 3. 测试脚本

```bash
python test_daily_update.py
```

## 关键特性

### 智能增量更新
- 自动检测数据库中的最新日期
- 只获取和导入缺失的交易日数据
- 避免重复导入

### 涨跌幅计算
- 使用交易日历确保准确的前一交易日
- 自动计算涨跌额(change)和涨跌幅(pct_chg)
- 对于没有前一日数据的记录，change和pct_chg为None

### 数据质量保证
- 获取数据后立即按目标股票列表筛选
- 严格的数据质量检查
- 跳过不完整或异常的数据

### 性能优化
- 批量获取多日数据
- 使用multi-index进行高效计算
- 批量导入数据库

## 输出结果

成功执行后会返回包含以下信息的字典：

```python
{
    "missing_dates": ["20250613", "20250616"],  # 需要更新的交易日
    "imported_count": 1000,                     # 成功导入的记录数
    "exclude_date": "20250612",                 # 用于计算但未入库的日期
    "processed_stocks": 500                     # 涉及的股票数量
}
```

## 注意事项

1. **环境变量**: 确保正确设置TUSHARE_TOKEN和STOCK_CODES_PATH
2. **数据库连接**: 确保ClickHouse数据库可连接且表结构正确
3. **API限制**: 注意tushare API的调用频率限制
4. **交易日历**: 会自动获取并缓存交易日历到`.cache/trade_calendar.csv`
5. **时区处理**: 自动处理北京时间，如无pytz库会使用UTC+8近似

## 错误处理

- 如果无新数据需要更新，会返回"无需更新"消息
- API调用失败会自动重试下一个日期
- 数据质量不符合要求的记录会被跳过
- 详细的错误日志会记录到日志文件中 