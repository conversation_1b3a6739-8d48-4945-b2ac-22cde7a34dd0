 #!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试合并股票数据功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.src.manager.stock_data_manager import StockDataManager
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_combined_data():
    """测试合并数据功能"""
    
    # 获取StockDataManager实例
    manager = StockDataManager.get_instance()
    
    # 测试小范围数据（避免查询过多数据）
    print("=== 测试1: 小范围数据查询 ===")
    try:
        df = manager.get_combined_stock_data(
            start_date='2024-01-01',
            end_date='2024-01-31',
            stock_list=['000001.SZ', '000002.SZ'],  # 只查询2只股票
            batch_years=1,
            include_quotes=True,
            include_chips=True,
            include_money_flow=True
        )
        
        print(f"查询结果形状: {df.shape}")
        print(f"列名: {df.columns.tolist()}")
        print(f"索引: {df.index.names}")
        print("前5行数据:")
        print(df.head())
        
        # 检查数据类型
        print("\n数据类型:")
        print(df.dtypes)
        
    except Exception as e:
        print(f"测试1失败: {e}")
    
    # 测试跨年查询
    print("\n=== 测试2: 跨年查询（包含可能没有筹码数据的年份） ===")
    try:
        df = manager.get_combined_stock_data(
            start_date='2017-01-01',
            end_date='2019-01-31',
            stock_list=['000001.SZ'],  # 只查询1只股票
            batch_years=1,
            include_quotes=True,
            include_chips=True,
            include_money_flow=False  # 不包含资金流
        )
        
        print(f"查询结果形状: {df.shape}")
        print("前5行数据:")
        print(df.head())
        
        # 检查筹码数据是否有NaN值
        chips_columns = [col for col in df.columns if col.startswith(('cost_', 'his_', 'winner_', 'weight_', 'chip_'))]
        print(f"\n筹码相关列: {chips_columns}")
        
        if chips_columns:
            print("筹码数据空值情况:")
            for col in chips_columns:
                null_count = df[col].isnull().sum()
                total_count = len(df)
                print(f"  {col}: {null_count}/{total_count} ({null_count/total_count*100:.1f}% 为空)")
        
    except Exception as e:
        print(f"测试2失败: {e}")

    # 测试只查询行情数据
    print("\n=== 测试3: 只查询行情数据 ===")
    try:
        df = manager.get_combined_stock_data(
            start_date='2024-01-01',
            end_date='2024-01-05',
            stock_list=['000001.SZ'],
            batch_years=1,
            include_quotes=True,
            include_chips=False,
            include_money_flow=False
        )
        
        print(f"查询结果形状: {df.shape}")
        print(f"列名: {df.columns.tolist()}")
        print("数据:")
        print(df)
        
    except Exception as e:
        print(f"测试3失败: {e}")

if __name__ == "__main__":
    test_combined_data()