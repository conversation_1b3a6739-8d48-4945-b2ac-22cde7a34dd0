import threading
import json
from datetime import datetime, UTC
from typing import Dict, List, Optional, Any, Union
from decimal import Decimal
import traceback
from pydantic import ValidationError

from utils.mysql_client import MySQLClient
from app.server.settings import settings
from app.server.core.log import logger
from app.src.manager.trade_schema import (
    TradeSignalSchema, 
    AccountSummarySchema, 
    AccountPositionSchema, 
    TradeStrategySchema,
    validate_signals_batch
)


class TradeManager:
    """交易管理器 - 负责处理所有交易相关的数据库操作"""
    
    _instance = None
    _instance_lock = threading.Lock()
    
    def __new__(cls):
        """单例模式"""
        if cls._instance is None:
            with cls._instance_lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, '_initialized'):
            self.mysql_client = MySQLClient.get_instance(
                host=settings.mysql_host,
                port=settings.mysql_port,
                user=settings.mysql_user,
                password=settings.mysql_password,
                database=settings.mysql_database
            )
            
            # 表名常量
            self.trade_strategies_table = "trade_strategies"
            self.trade_signals_table = "trade_signals"
            self.account_summary_table = "account_summary"
            self.account_positions_table = "account_positions"
            
            # 策略名称到ID的缓存
            self._strategy_cache = {}
            self._cache_lock = threading.Lock()
            
            self._initialized = True
            logger.info("TradeManager initialized successfully")
    
    # ==================== 工具函数 ====================
    
    def generate_timestamp(self) -> str:
        """生成当前时间戳"""
        return datetime.now(UTC).strftime('%Y-%m-%d %H:%M:%S')
    
    # ==================== 策略管理 ====================
    
    def get_strategy_id_by_name(self, strategy_name: str) -> Optional[int]:
        """根据策略名称获取策略ID"""
        try:
            # 先检查缓存
            with self._cache_lock:
                if strategy_name in self._strategy_cache:
                    return self._strategy_cache[strategy_name]
            
            # 从数据库查询
            result = self.mysql_client.select_one(
                table=self.trade_strategies_table,
                columns=['id'],
                where={'strategy_name': strategy_name}
            )
            
            if result:
                strategy_id = result['id']
                # 更新缓存
                with self._cache_lock:
                    self._strategy_cache[strategy_name] = strategy_id
                return strategy_id
            
            return None
        except Exception as e:
            logger.error(f"Error getting strategy ID by name '{strategy_name}': {e}")
            return None
    
    def batch_get_strategy_ids(self, strategy_names: List[str]) -> Dict[str, Optional[int]]:
        """批量获取策略ID"""
        try:
            result_dict = {}
            uncached_names = []
            
            # 先从缓存获取
            with self._cache_lock:
                for name in strategy_names:
                    if name in self._strategy_cache:
                        result_dict[name] = self._strategy_cache[name]
                    else:
                        uncached_names.append(name)
            
            # 批量查询未缓存的策略
            if uncached_names:
                strategies = self.mysql_client.select_in(
                    table=self.trade_strategies_table,
                    column='strategy_name',
                    values=uncached_names,
                    columns=['id', 'strategy_name']
                )
                
                # 更新结果和缓存
                with self._cache_lock:
                    for strategy in strategies:
                        name = strategy['strategy_name']
                        strategy_id = strategy['id']
                        result_dict[name] = strategy_id
                        self._strategy_cache[name] = strategy_id
                
                # 对于没有找到的策略，设置为None
                for name in uncached_names:
                    if name not in result_dict:
                        result_dict[name] = None
            
            return result_dict
        except Exception as e:
            logger.error(f"Error batch getting strategy IDs: {e}")
            return {name: None for name in strategy_names}
    
    def validate_strategy_exists(self, strategy_name: str) -> bool:
        """验证策略是否存在"""
        return self.get_strategy_id_by_name(strategy_name) is not None
    
    # ==================== 数据验证（使用Pydantic模型）====================
    
    # ==================== 通用查询工具函数 ====================
    
    def _calculate_pagination(self, page: Optional[int], page_size: Optional[int]) -> tuple:
        """计算分页参数，如果page或page_size为None则返回None表示不分页"""
        if page is None or page_size is None:
            return None, None
        offset = (page - 1) * page_size
        return offset, page_size
    
    def _build_paginated_response(self, data: List[dict], total: int, page: Optional[int], page_size: Optional[int]) -> dict:
        """构建分页响应"""
        if page is None or page_size is None:
            # 无分页情况
            return {
                'success': True,
                'data': data,
                'pagination': {
                    'total': total,
                    'page': None,
                    'page_size': None,
                    'total_pages': 1,
                    'is_paginated': False
                }
            }
        else:
            # 有分页情况
            total_pages = (total + page_size - 1) // page_size if total > 0 else 0
            return {
                'success': True,
                'data': data,
                'pagination': {
                    'total': total,
                    'page': page,
                    'page_size': page_size,
                    'total_pages': total_pages,
                    'is_paginated': True
                }
            }
    
    def _count_records(self, table: str, where: dict = None) -> int:
        """获取记录总数"""
        try:
            result = self.mysql_client.select_one(table, columns=['COUNT(*)'], where=where)
            return list(result.values())[0] if result else 0
        except Exception as e:
            logger.error(f"Error counting records in table {table}: {e}")
            return 0
    
    # ==================== 查询功能 ====================
    
    def get_strategy_by_name(self, strategy_name: str) -> dict:
        """根据策略名称查询策略"""
        try:
            where_conditions = {'strategy_name': strategy_name}
            
            # 查询数据
            data = self.mysql_client.select_one(
                table=self.trade_strategies_table,
                where=where_conditions
            )
            if data:
                for key in ['factor_names', 'factor_expressions', 'extra_params']:
                    if key in data and data[key] is not None:
                        data[key] = json.loads(data[key])
            
            return data
            
        except Exception as e:
            logger.error(f"Error querying strategy by name '{strategy_name}': {e}")
            return {
                'success': False,
                'message': f'Error querying strategy: {str(e)}'
            }
    
    def get_strategy_by_id(self, strategy_id: int) -> dict:
        """根据策略ID查询策略"""
        try:
            where_conditions = {'id': strategy_id}
            
            # 查询数据
            data = self.mysql_client.select_one(
                table=self.trade_strategies_table,
                where=where_conditions
            )
            
            if data:
                for key in ['factor_names', 'factor_expressions', 'extra_params']:
                    if key in data and data[key] is not None:
                        data[key] = json.loads(data[key])
            
            return data
            
        except Exception as e:
            logger.error(f"Error querying strategy by ID {strategy_id}: {e}")
            return {
                'success': False,
                'message': f'Error querying strategy: {str(e)}'
            }
    
    def get_signals_by_strategy(self, strategy_name: str, page: Optional[int] = 1, page_size: Optional[int] = 20) -> dict:
        """根据策略名称查询信号（按generated_time排序）"""
        try:
            # 先获取策略ID
            strategy_id = self.get_strategy_id_by_name(strategy_name)
            if strategy_id is None:
                return {
                    'success': False,
                    'message': f'Strategy not found: {strategy_name}'
                }
            
            offset, limit = self._calculate_pagination(page, page_size)
            where_conditions = {'strategy_id': strategy_id}
            
            # 获取总数
            total = self._count_records(self.trade_signals_table, where_conditions)
            
            # 查询数据
            query_params = {
                'table': self.trade_signals_table,
                'where': where_conditions,
                'order_by': [('generated_time', 'DESC')]
            }

            # 只有在需要分页时才添加limit和offset参数
            if limit is not None and offset is not None:
                query_params['limit'] = limit
                query_params['offset'] = offset

            data = self.mysql_client.select(**query_params)

            logger.info(f"Query signals by strategy '{strategy_name}': found {len(data)} records")
            return self._build_paginated_response(data, total, page, page_size)

        except Exception as e:
            logger.error(f"Error querying signals by strategy '{strategy_name}': {e}")
            return {
                'success': False,
                'message': f'Error querying signals: {str(e)}'
            }
    
    def get_signals_by_strategy_and_date(self, strategy_name: str, trade_date: str, page: Optional[int] = 1, page_size: Optional[int] = 20) -> dict:
        """根据策略名称和交易日期查询信号（按generated_time排序）"""
        try:
            # 先获取策略ID
            strategy_id = self.get_strategy_id_by_name(strategy_name)
            if strategy_id is None:
                return {
                    'success': False,
                    'message': f'Strategy not found: {strategy_name}'
                }

            offset, limit = self._calculate_pagination(page, page_size)
            where_conditions = {
                'strategy_id': strategy_id,
                'trade_date': trade_date
            }

            # 获取总数
            total = self._count_records(self.trade_signals_table, where_conditions)

            # 查询数据
            query_params = {
                'table': self.trade_signals_table,
                'where': where_conditions,
                'order_by': [('generated_time', 'DESC')]
            }

            # 只有在需要分页时才添加limit和offset参数
            if limit is not None and offset is not None:
                query_params['limit'] = limit
                query_params['offset'] = offset

            data = self.mysql_client.select(**query_params)

            logger.info(f"Query signals by strategy '{strategy_name}' and date '{trade_date}': found {len(data)} records")
            return self._build_paginated_response(data, total, page, page_size)

        except Exception as e:
            logger.error(f"Error querying signals by strategy '{strategy_name}' and date '{trade_date}': {e}")
            return {
                'success': False,
                'message': f'Error querying signals: {str(e)}'
            }
    
    def get_account_summary(self, account_id: str, date: str = None, page: int = 1, page_size: int = 20) -> dict:
        """根据账户ID查询账户概览，按datetime排序"""
        try:
            offset, limit = self._calculate_pagination(page, page_size)
            where_conditions = {'account_id': account_id}
            
            # 如果指定了日期，添加日期过滤
            if date:
                where_conditions['date'] = date
            
            # 获取总数
            total = self._count_records(self.account_summary_table, where_conditions)
            
            # 查询数据
            data = self.mysql_client.select(
                table=self.account_summary_table,
                where=where_conditions,
                order_by=[('datetime', 'DESC')],
                limit=limit,
                offset=offset
            )
            
            logger.info(f"Query account summary for account '{account_id}'{f' on date {date}' if date else ''}: found {len(data)} records")
            return self._build_paginated_response(data, total, page, page_size)
            
        except Exception as e:
            logger.error(f"Error querying account summary for account '{account_id}': {e}")
            return {
                'success': False,
                'message': f'Error querying account summary: {str(e)}'
            }
    
    def get_account_positions(self, account_id: str, date: str = None, page: int = 1, page_size: int = 20) -> dict:
        """根据账户ID查询账户持仓，按datetime排序"""
        try:
            offset, limit = self._calculate_pagination(page, page_size)
            where_conditions = {'account_id': account_id}
            
            # 如果指定了日期，添加日期过滤
            if date:
                where_conditions['date'] = date
            
            # 获取总数
            total = self._count_records(self.account_positions_table, where_conditions)
            
            # 查询数据
            data = self.mysql_client.select(
                table=self.account_positions_table,
                where=where_conditions,
                order_by=[('datetime', 'DESC')],
                limit=limit,
                offset=offset
            )
            
            logger.info(f"Query account positions for account '{account_id}'{f' on date {date}' if date else ''}: found {len(data)} records")
            return self._build_paginated_response(data, total, page, page_size)
            
        except Exception as e:
            logger.error(f"Error querying account positions for account '{account_id}': {e}")
            return {
                'success': False,
                'message': f'Error querying account positions: {str(e)}'
            }
    
    # ==================== 插入功能 ====================
    
    
    def insert_trade_signals(self, signals_list: List[dict]) -> dict:
        """批量插入交易信号"""
        try:
            if not signals_list:
                return {
                    'success': False,
                    'message': 'No signals provided'
                }
            
            # 使用Pydantic模型批量验证
            valid_signals, validation_errors = validate_signals_batch(signals_list)
            
            # 批量获取策略ID
            strategy_names = [signal.strategy_name for signal in valid_signals]
            strategy_ids = self.batch_get_strategy_ids(strategy_names)
            
            # 转换为数据库格式
            db_data_list = []
            conversion_errors = []
            
            for i, signal in enumerate(valid_signals):
                strategy_id = strategy_ids.get(signal.strategy_name)
                if strategy_id is None:
                    conversion_errors.append(f"Signal {i}: Strategy not found: {signal.strategy_name}")
                    continue
                
                db_data = signal.to_db_dict(strategy_id)
                db_data_list.append(db_data)

            # 合并所有错误
            all_errors = validation_errors + conversion_errors

            if len(all_errors) > 0:
                return {
                    'success': False,
                    'message': f'Data validation failed\n{json.dumps(all_errors, ensure_ascii=False)}'
                }
            
            # 插入有效的信号
            inserted_count = 0
            if db_data_list:
                inserted_count = self.mysql_client.insert_many(self.trade_signals_table, db_data_list)
            
            logger.info(f"Batch insert completed: {inserted_count} signals inserted, {len(all_errors)} errors")
            
            return {
                'success': True,
                'message': f'Batch insert completed',
                'total_provided': len(signals_list),
                'successful_inserts': inserted_count
            }
        except Exception as e:
            logger.error(f"Error in batch insert trade signals: {e}")
            return {
                'success': False,
                'message': f'Error in batch insert: {str(e)}'
            }
    

    def convert_datetime_to_date(self, datatime: str) -> str:
        """将时间戳转换为日期"""
        date = datetime.strptime(datatime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
        return date
    
    def insert_account_summary(self, summary_data: dict) -> dict:
        """插入账户概览数据"""
        try:
            
            datatime = summary_data.pop('timestamp')
            date = self.convert_datetime_to_date(datatime)
            summary_data['date'] = date
            summary_data['datetime'] = datatime

            # 使用Pydantic模型验证数据
            summary_schema = AccountSummarySchema(**summary_data)
            
            # 转换为数据库格式
            prepared_data = summary_schema.to_db_dict()
            
            # 插入数据库
            insert_id = self.mysql_client.insert(self.account_summary_table, prepared_data)
            
            if insert_id:
                logger.info(f"Successfully inserted account summary with ID: {insert_id}")
                return {
                    'success': True,
                    'message': 'Account summary inserted successfully',
                    'summary_id': insert_id
                }
            else:
                return {
                    'success': False,
                    'message': 'Failed to insert account summary'
                }
        except ValidationError as e:
            # Pydantic验证错误
            errors = [f"{err['loc'][0]}: {err['msg']}" for err in e.errors()]
            return {
                'success': False,
                'message': 'Data validation failed',
                'errors': errors
            }
        except Exception as e:
            logger.error(f"Error inserting account summary: {e}")
            return {
                'success': False,
                'message': f'Error inserting account summary: {str(e)}'
            }
    
    def insert_account_positions(self, request_data: dict) -> dict:
        """批量插入账户持仓数据"""
        try:
            account_id = request_data['account_id']
            positions = request_data['positions']
            timestamp = request_data['timestamp']
            
            # 处理时间戳
            date = self.convert_datetime_to_date(timestamp)
            
            # 准备批量插入数据
            db_data_list = []
            validation_errors = []
            
            for i, position in enumerate(positions):

                    # 添加账户ID和时间信息
                    position_data = {
                        'account_id': account_id,
                        'date': date,
                        'datetime': timestamp,
                        **position
                    }
                    
                    # 使用Pydantic模型验证数据
                    position_schema = AccountPositionSchema(**position_data)
                    
                    # 转换为数据库格式
                    prepared_data = position_schema.to_db_dict()
                    db_data_list.append(prepared_data)
                
            # 批量插入数据库
            inserted_count = 0
            if db_data_list:
                inserted_count = self.mysql_client.insert_many(self.account_positions_table, db_data_list)
            
            if inserted_count > 0:
                logger.info(f"Successfully inserted {inserted_count} account positions for account {account_id}")
                return {
                    'success': True,
                    'message': f'Successfully inserted {inserted_count} account positions',
                    'total_provided': len(positions),
                    'successful_inserts': inserted_count
                }
            else:
                return {
                    'success': False,
                    'message': 'Failed to insert account positions'
                }
                
        except Exception as e:
            logger.error(f"Error inserting account positions: {e}")
            logger.error(f"{traceback.format_exc()}")
            return {
                'success': False,
                'message': f'Error inserting account positions: {str(e)}'
            }
    
    def insert_trade_strategy(self, strategy_data: dict) -> dict:
        """插入交易策略"""
        try:
            # 使用Pydantic模型验证数据
            strategy_schema = TradeStrategySchema(**strategy_data)
            
            # 检查策略名称是否已存在
            if self.validate_strategy_exists(strategy_schema.strategy_name):
                return {
                    'success': False,
                    'message': f'Strategy name already exists: {strategy_schema.strategy_name}'
                }
            
            # 转换为数据库格式
            prepared_data = strategy_schema.to_db_dict(self.generate_timestamp())
            
            # 插入数据库
            insert_id = self.mysql_client.insert(self.trade_strategies_table, prepared_data)
            
            if insert_id:
                # 清除缓存，因为新增了策略
                with self._cache_lock:
                    self._strategy_cache.clear()
                
                logger.info(f"Successfully inserted trade strategy with ID: {insert_id}")
                return {
                    'success': True,
                    'message': 'Trade strategy inserted successfully',
                    'strategy_id': insert_id
                }
            else:
                return {
                    'success': False,
                    'message': 'Failed to insert trade strategy'
                }
        except ValidationError as e:
            # Pydantic验证错误
            errors = [f"{err['loc'][0]}: {err['msg']}" for err in e.errors()]
            return {
                'success': False,
                'message': 'Data validation failed',
                'errors': errors
            }
        except Exception as e:
            logger.error(f"Error inserting trade strategy: {e}")
            return {
                'success': False,
                'message': f'Error inserting trade strategy: {str(e)}'
            }
