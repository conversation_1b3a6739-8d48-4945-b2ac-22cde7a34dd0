from __future__ import annotations
import threading
import traceback
from typing import List, Dict, Optional, Union, Any, Tuple
import time
from datetime import datetime, timedelta
import dateutil.relativedelta as rd

import pandas as pd
import numpy as np

from utils.clickhouse_client import ClickHouseClient
from utils.sql_builder import SQLBuilder

from app.server.settings import settings
from app.server.core.log import logger


class CacheInfo:
    """通用缓存信息类"""
    def __init__(self, data: pd.DataFrame, start_date: str, end_date: str, item_list: List[str]):
        self.data = data
        self.start_date = start_date
        self.end_date = end_date
        self.item_list = item_list

class DataCacheManager:
    """通用数据缓存管理器"""
    def __init__(self):
        self.caches = {
            'stock': None,     # CacheInfo for stock data
            'index': None,     # CacheInfo for index data
        }
        self.locks = {
            'stock': threading.Lock(),
            'index': threading.Lock()
        }
    
    def is_cache_valid(
        self,
        cache_type: str,
        start_date: Optional[str],
        end_date: Optional[str],
        item_list: Optional[List[str]]
    ) -> bool:
        """检查缓存是否有效"""
        cache = self.caches.get(cache_type)
        if cache is None:
            logger.debug(f"{cache_type}缓存检查：缓存为空")
            return False
        
        logger.debug(f"{cache_type}缓存检查：缓存范围={cache.start_date}到{cache.end_date}, 缓存数量={len(cache.item_list)}个")
        logger.debug(f"{cache_type}缓存检查：请求范围={start_date}到{end_date}, 请求列表={item_list}")
        
        # 检查日期范围：请求的范围必须完全在缓存范围内
        if start_date is not None and start_date < cache.start_date:
            logger.debug(f"{cache_type}缓存检查：开始日期超出范围 {start_date} < {cache.start_date}")
            return False
        if end_date is not None and end_date > cache.end_date:
            logger.debug(f"{cache_type}缓存检查：结束日期超出范围 {end_date} > {cache.end_date}")
            return False
        
        if not item_list:
            logger.debug(f"{cache_type}缓存检查：列表为空")
            return False

        # 检查列表：请求的项目必须完全在缓存的项目中
        if not set(item_list).issubset(set(cache.item_list)):
            logger.debug(f"{cache_type}缓存检查：项目不在缓存中 {set(item_list) - set(cache.item_list)}")
            return False
        
        logger.debug(f"{cache_type}缓存检查：命中")
        return True

    def get_from_cache(
        self,
        cache_type: str,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        item_list: Optional[List[str]] = None,
        columns: Optional[List[str]] = None,
    ) -> pd.DataFrame:
        """从缓存中获取数据"""
        cache = self.caches.get(cache_type)
        if cache is None:
            raise ValueError(f"{cache_type}缓存为空")
        
        # 直接在缓存的DataFrame上进行过滤
        df = cache.data
        df = df.loc[(slice(start_date, end_date), item_list), :]
        
        # 过滤列
        if columns is not None:
            # 只选择存在的列
            available_cols = list(set([col for col in columns if col in df.columns]))
            df = df[available_cols]
        
        # 排序（与数据库查询保持一致）
        df = df.sort_values(['trade_date', 'instrument_id'], ascending=[False, True])
        df = df.reset_index()
        
        return df

    def cache_data(
        self,
        cache_type: str,
        data: pd.DataFrame,
        start_date: str,
        end_date: str,
        item_list: List[str]
    ) -> None:
        """缓存数据"""
        # 从实际数据中提取真实的日期范围和列表
        actual_start_date = data.index.get_level_values('trade_date').min().strftime('%Y-%m-%d')
        actual_end_date = data.index.get_level_values('trade_date').max().strftime('%Y-%m-%d')
        actual_item_list = data.index.get_level_values('instrument_id').unique().tolist()
        
        # 创建缓存信息
        cache_info = CacheInfo(data, actual_start_date, actual_end_date, actual_item_list)
        
        # 原子性更新缓存
        with self.locks[cache_type]:
            self.caches[cache_type] = cache_info
        
        logger.info(f"{cache_type}缓存成功 - 请求范围: {start_date} 到 {end_date}")
        logger.info(f"实际缓存范围: {actual_start_date} 到 {actual_end_date}")
        logger.info(f"缓存数量: {len(actual_item_list)}, 数据形状: {data.shape}")

    def clear_cache(self, cache_type: str) -> None:
        """清空指定类型的缓存"""
        with self.locks[cache_type]:
            self.caches[cache_type] = None
        logger.info(f"{cache_type}缓存已清空")

    def get_cache_info(self, cache_type: str) -> Optional[Dict[str, Any]]:
        """获取缓存信息（用于调试）"""
        cache = self.caches.get(cache_type)
        if cache is None:
            return None
        
        return {
            "start_date": cache.start_date,
            "end_date": cache.end_date,
            "item_count": len(cache.item_list),
            "data_shape": cache.data.shape,
            "columns": cache.data.columns.tolist()
        }

class StockDataManager:
    _instance_lock = threading.Lock()
    
    # 统一缓存管理器
    _cache_manager: Optional[DataCacheManager] = None
    
    # 统一的字段定义 - 集中管理所有表的字段
    TABLE_FIELDS = {
        'daily_quotes': {
            'base_fields': [
                "instrument_id", "trade_date", "open", "high", "low", "close",
                "volume", "amount", "change", "pct_chg", "adj_factor"
            ],
            'calculated_fields': {
                "adj_open": "open * adj_factor AS adj_open",
                "adj_high": "high * adj_factor AS adj_high", 
                "adj_low": "low * adj_factor AS adj_low",
                "adj_close": "close * adj_factor AS adj_close"
            }
        },
        'daily_chips': {
            'base_fields': [
                "instrument_id", "trade_date", "his_low", "his_high",
                "cost_5pct", "cost_15pct", "cost_50pct", "cost_85pct", "cost_95pct",
                "weight_avg", "winner_rate"
            ],
            'calculated_fields': {
                "chip_conct_90": "toFloat64(toFloat64(cost_95pct) - toFloat64(cost_5pct)) / nullIf(toFloat64(cost_95pct) + toFloat64(cost_5pct), 0) * 100 AS chip_conct_90",
                "chip_conct_70": "toFloat64(toFloat64(cost_85pct) - toFloat64(cost_15pct)) / nullIf(toFloat64(cost_85pct) + toFloat64(cost_15pct), 0) * 100 AS chip_conct_70"
            }
        },
        'daily_money_flow': {
            'base_fields': [
                "instrument_id", "trade_date", "buy_sm_vol", "buy_sm_amount", 
                "sell_sm_vol", "sell_sm_amount", "buy_md_vol", "buy_md_amount", 
                "sell_md_vol", "sell_md_amount", "buy_lg_vol", "buy_lg_amount", 
                "sell_lg_vol", "sell_lg_amount", "buy_elg_vol", "buy_elg_amount", 
                "sell_elg_vol", "sell_elg_amount", "net_mf_vol", "net_mf_amount"
            ],
            'calculated_fields': {}
        },
        'daily_index': {
            'base_fields': [
                "instrument_id", "trade_date", "open", "high", "low", "close",
                "volume", "amount", "change", "pct_chg"
            ],
            'calculated_fields': {}
        },
        'stock_5_min': {
            'base_fields': [
                "instrument_id", "trade_time", "open", "high", "low", "close",
                "volume", "amount", "trade_date"
            ],
            'calculated_fields': {}
        },
        'daily_sector_quotes': {
            'base_fields': [
                "instrument_id", "trade_date", "open", "high", "low", "close",
                "volume", "avg_price", "change", "pct_chg", "turnover_rate",
                "total_mv", "float_mv"
            ],
            'calculated_fields': {}
        }
    }
    
    @classmethod
    def get_table_all_fields(cls, table: str) -> List[str]:
        """获取表的所有字段（基础字段 + 计算字段，不包括排除的字段）"""
        if table not in cls.TABLE_FIELDS:
            raise ValueError(f"未知的表名: {table}")
        
        config = cls.TABLE_FIELDS[table]
        all_fields = config['base_fields'].copy()
        all_fields.extend(config['calculated_fields'].keys())
        return all_fields
    
    @classmethod
    def get_table_base_fields(cls, table: str) -> List[str]:
        """获取表的基础字段"""
        if table not in cls.TABLE_FIELDS:
            raise ValueError(f"未知的表名: {table}")
        return cls.TABLE_FIELDS[table]['base_fields'].copy()
    
    @classmethod
    def get_table_calculated_fields(cls, table: str) -> Dict[str, str]:
        """获取表的计算字段映射"""
        if table not in cls.TABLE_FIELDS:
            raise ValueError(f"未知的表名: {table}")
        return cls.TABLE_FIELDS[table]['calculated_fields'].copy()
    

    def __init__(self):
        self.clickhouse_client = ClickHouseClient.get_instance(
            host=settings.clickhouse_host,
            port=settings.clickhouse_port,
            user=settings.clickhouse_user,
            password=settings.clickhouse_password,
            database=settings.clickhouse_database
        )
        self.sql_builder = SQLBuilder()
        self._schema_cache = {}  # 缓存表结构信息
        self.latest_date_in_db = self.get_latest_date_from_db('daily_quotes')
        
        # 初始化缓存管理器
        if StockDataManager._cache_manager is None:
            StockDataManager._cache_manager = DataCacheManager()
        
        logger.info(f"数据库最新日期: {self.latest_date_in_db}")

    @classmethod
    def get_instance(cls) -> StockDataManager:
        if not hasattr(cls, "_instance"):
            """Get cls instance."""
            with cls._instance_lock:
                if not hasattr(cls, "_instance"):
                    cls._instance = cls()
        return cls._instance
    
    def update_latest_date_in_db(self):
        """
        更新数据库最新日期
        """
        latest_date = self.get_latest_date_from_db('daily_quotes')
        if latest_date:
            self.latest_date_in_db = latest_date
            logger.info(f"更新数据库最新日期: {latest_date}")
        else:
            self.latest_date_in_db = None
            logger.error("获取数据库最新日期失败")


    def get_latest_date_from_db(self, table_name: str, date_column: str = 'trade_date') -> Optional[str]:
        """
        从数据库获取指定表的最新日期
        
        Args:
            table_name: 表名
            date_column: 日期列名
            
        Returns:
            str: 最新日期，格式YYYYMMDD，如果表为空则返回None
        """
        try:
            query = f"SELECT MAX({date_column}) as max_date FROM {table_name}"
            result = self.clickhouse_client.execute(query)
            
            if result and len(result) > 0 and result[0]['max_date'] is not None:
                max_date = result[0]['max_date']
                if isinstance(max_date, str):
                    return max_date
                else:
                    return max_date.strftime('%Y-%m-%d')
            return None
        except Exception as e:
            logger.error(f"查询数据库最新日期失败: {str(e)}")
            return None

    def get_data_count_by_date(self, table_name: str, date: str, date_column: str = 'trade_date') -> int:
        """
        获取指定表中等于指定日期的数据条数
        
        Args:
            table_name: 表名
            date: 查询日期，格式为'YYYY-MM-DD'
            date_column: 日期列名，默认为'trade_date'
            
        Returns:
            int: 数据条数，查询失败时返回0
        """
        try:
            query = f"SELECT COUNT(*) as count FROM {table_name} WHERE {date_column} = toDate(%(date)s)"
            result = self.clickhouse_client.execute(query, {'date': date})
            
            if result and len(result) > 0:
                return result[0]['count']
            return 0
        except Exception as e:
            logger.error(f"查询表 {table_name} 日期 {date} 的数据条数失败: {str(e)}")
            return 0

    def _query_time_series_data(
        self,
        table: str,
        stock_list: Optional[List[str]] = None, 
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        columns: Optional[List[str]] = None,
        page: Optional[int] = None,
        page_size: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        查询时间序列数据的通用方法
        
        Args:
            table: 表名
            stock_list: 股票代码列表，为None表示查询所有股票
            start_date: 开始日期，格式为'YYYY-MM-DD'，为None表示不限制开始日期
            end_date: 结束日期，格式为'YYYY-MM-DD'，为None表示不限制结束日期
            columns: 需要的列，为None表示查询所有列
            page: 页码，从1开始，为None表示不分页（查询全部）
            page_size: 每页大小，为None表示不分页（查询全部）
            
        Returns:
            包含data、total、page、page_size信息的字典
            
        Raises:
            ValueError: 当stock_list和日期范围都为None时抛出
        """

        # page 和 page_size 不能其中之一为None，除非同时为None或者都为None
        # 要用简便的写法来判断
        if page is not None and page_size is None:
            raise ValueError("page_size不能为None，除非page和page_size同时为None")
        if page is None and page_size is not None:
            raise ValueError("page不能为None，除非page和page_size同时为None")

        if stock_list is None and (start_date is None and end_date is None):
            raise ValueError("股票列表和日期范围不能同时为空")
            
        # 检查分页参数
        if page is not None and page < 1:
            raise ValueError("页码必须大于0")
        if page_size is not None and page_size < 1:
            raise ValueError("每页大小必须大于0")
            
        complex_conditions = []
        
        # 添加股票代码条件
        if stock_list:
            complex_conditions.append({
                "column": "instrument_id",
                "operator": "IN",
                "value": stock_list
            })
        
        # 添加日期范围条件 - 使用 BETWEEN AND
        if start_date and end_date:
            complex_conditions.append({
                "column": "trade_date",
                "operator": "BETWEEN",
                "value": [start_date, end_date]
            })
        elif start_date:
            complex_conditions.append({
                "column": "trade_date",
                "operator": ">=",
                "value": start_date
            })
        elif end_date:
            complex_conditions.append({
                "column": "trade_date",
                "operator": "<=",
                "value": end_date
            })
            
        # 排序条件
        order_by = [("instrument_id", "ASC"), ("trade_date", "DESC")]
        
        try:
            # 计算分页参数
            offset = None if page is None or page_size is None else (page - 1) * page_size
            limit = page_size
            
            # 统一使用带计数的查询
            data, total = self.clickhouse_client.select(
                table=table,
                columns=columns,
                complex_conditions=complex_conditions,
                order_by=order_by,
                limit=limit,
                offset=offset,
                return_count=True
            )
            
            # 根据是否分页设置返回值
            if page is None or page_size is None:
                return {
                    "data": data,
                    "total": total,
                    "page": 1,
                    "page_size": total
                }
            else:
                return {
                    "data": data,
                    "total": total,
                    "page": page,
                    "page_size": page_size
                }
        except Exception as e:
            logger.error(f"查询{table}数据失败: {e}")
            return {
                "data": [],
                "total": 0,
                "page": page or 1,
                "page_size": page_size or 0
            }

    def _validate_pagination_params(self, page: Optional[int], page_size: Optional[int]):
        """统一的分页参数验证"""
        if page is not None and page_size is None:
            raise ValueError("page_size不能为None，除非page和page_size同时为None")
        if page is None and page_size is not None:
            raise ValueError("page不能为None，除非page和page_size同时为None")
        if page is not None and page < 1:
            raise ValueError("页码必须大于0")
        if page_size is not None and page_size < 1:
            raise ValueError("每页大小必须大于0")

    def _build_where_conditions_and_params(
        self, 
        stock_list: Optional[List[str]], 
        start_date: Optional[str], 
        end_date: Optional[str]
    ) -> Tuple[List[str], Dict[str, Any]]:
        """构建 WHERE 条件和参数"""
        where_conditions = []
        params = {}
        
        # 添加股票代码条件
        if stock_list:
            placeholders = []
            for i, stock in enumerate(stock_list):
                param_name = f"stock_{i}"
                placeholders.append(f"%({param_name})s")
                params[param_name] = stock
            where_conditions.append(f"instrument_id IN ({', '.join(placeholders)})")
        
        # 添加日期范围条件 - 使用 BETWEEN AND
        if start_date and end_date:
            where_conditions.append("trade_date BETWEEN toDate(%(start_date)s) AND toDate(%(end_date)s)")
            params["start_date"] = start_date
            params["end_date"] = end_date
        elif start_date:
            where_conditions.append("trade_date >= toDate(%(start_date)s)")
            params["start_date"] = start_date
        elif end_date:
            where_conditions.append("trade_date <= toDate(%(end_date)s)")
            params["end_date"] = end_date
            
        return where_conditions, params

    def _query_with_calculated_fields(
        self,
        table: str,
        base_fields: List[str],
        calculated_field_mapping: Dict[str, str],
        stock_list: Optional[List[str]] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        columns: Optional[List[str]] = None,
        page: Optional[int] = None,
        page_size: Optional[int] = None,
        additional_conditions: Optional[List[str]] = None,
        order_by: Optional[List[Tuple[str, str]]] = None,
        return_df: bool = False
    ) -> Union[Dict[str, Any], pd.DataFrame]:
        """
        统一的带计算字段的查询方法
        
        Args:
            table: 表名
            base_fields: 基础字段列表
            calculated_field_mapping: 计算字段映射
            stock_list: 股票代码列表，为None表示查询所有股票
            start_date: 开始日期，格式为'YYYY-MM-DD'，为None表示不限制开始日期
            end_date: 结束日期，格式为'YYYY-MM-DD'，为None表示不限制结束日期
            columns: 需要的列，为None表示查询所有列
            page: 页码，从1开始，为None表示不分页
            page_size: 每页大小，为None表示不分页
            additional_conditions: 额外的WHERE条件
            order_by: 排序条件，默认为按股票代码和日期排序
            return_df: 是否返回DataFrame，默认False返回字典格式
            
        Returns:
            包含数据和分页信息的字典，或DataFrame（当return_df=True时）
        """
        # 参数验证
        self._validate_pagination_params(page, page_size)
        
        if not stock_list and (not start_date and not end_date):
            raise ValueError("股票列表和日期范围不能同时为空")
        
        try:
            # 构建条件和参数
            where_conditions, params = self._build_where_conditions_and_params(
                stock_list, start_date, end_date
            )
            
            # 添加额外条件
            if additional_conditions:
                where_conditions.extend(additional_conditions)
            
            # 默认排序
            if order_by is None:
                order_by = [("instrument_id", "ASC"), ("trade_date", "DESC")]
            
            # 生成 SQL 类型转换映射
            sql_cast_mapping = self._generate_sql_cast_mapping(table)
            
            # 使用 SQL Builder 构建查询
            data_query, count_query = self.sql_builder.build_calculated_field_query(
                table=table,
                base_fields=base_fields,
                calculated_field_mapping=calculated_field_mapping,
                columns=columns,
                where_conditions=where_conditions,
                order_by=order_by,
                limit=page_size if page is not None else None,
                offset=(page - 1) * page_size if page is not None and page_size is not None else None,
                sql_type_cast=sql_cast_mapping
            )
            
            # 执行查询
            count_result = self.clickhouse_client.execute(count_query, params)
            total = count_result[0]['total_count'] if count_result else 0
            
            # 直接获取DataFrame，类型已在SQL层面转换
            data_df = self.clickhouse_client.execute(data_query, params, return_df=True)
            
            # 返回结果
            if return_df:
                # 直接返回DataFrame，忽略分页信息
                return data_df
            else:
                # 转换为字典列表以保持API兼容性
                data = data_df.to_dict('records') if not data_df.empty else []
                # 转换为字典列表以保持API兼容性
                # 返回原有的字典格式
                if page is None or page_size is None:
                    return {
                        "data": data,
                        "total": total,
                        "page": 1,
                        "page_size": total
                    }
                else:
                    return {
                        "data": data,
                        "total": total,
                        "page": page,
                        "page_size": page_size
                    }
                
        except Exception as e:
            logger.error(f"查询{table}数据失败: {e}")
            return {
                "data": [],
                "total": 0,
                "page": page or 1,
                "page_size": page_size or 0
            }

    def get_daily_quotes(
        self,
        stock_list: Optional[List[str]] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        columns: Optional[List[str]] = None,
        page: Optional[int] = None,
        page_size: Optional[int] = None,
        return_df: bool = False
    ) -> Union[Dict[str, Any], pd.DataFrame]:
        """
        获取股票日线行情数据
        
        Args:
            stock_list: 股票代码列表，为None表示查询所有股票
            start_date: 开始日期，格式为'YYYY-MM-DD'，为None表示不限制开始日期
            end_date: 结束日期，格式为'YYYY-MM-DD'，为None表示不限制结束日期
            columns: 需要的行情数据列，为None表示查询所有列
                    支持的列包括: instrument_id, trade_date, open, high, low, close, 
                    volume, amount, change, pct_chg, adj_factor,
                    adj_open, adj_high, adj_low, adj_close (后复权价格)
            page: 页码，从1开始，为None表示不分页
            page_size: 每页大小，为None表示不分页
            return_df: 是否返回DataFrame，默认False返回字典格式
            
        Returns:
            包含行情数据和分页信息的字典，或DataFrame（当return_df=True时）
        """
        # 检查缓存是否有效
        if self._cache_manager.is_cache_valid('stock', start_date, end_date, stock_list):
            logger.info("使用缓存获取日线行情数据")
            
            # 过滤行情相关字段
            quotes_columns = None
            if columns is not None:
                # 获取行情表的所有字段
                all_quotes_fields = self.get_table_all_fields('daily_quotes')
                # 只保留行情相关的字段
                quotes_columns = list(set([col for col in columns if col in all_quotes_fields or col in ['instrument_id', 'trade_date']]))
            else:
                quotes_columns = list(set(self.get_table_all_fields('daily_quotes')))
            
            if page is not None or page_size is not None or not return_df:
                raise ValueError("page and page_size are not supported for cache, and return_df must be True")
            
            return self._cache_manager.get_from_cache(
                cache_type='stock',
                start_date=start_date,
                end_date=end_date,
                item_list=stock_list,
                columns=quotes_columns
            )
        
        # 缓存未命中，从数据库查询
        logger.info("缓存未命中，从数据库获取日线行情数据")
        
        # 使用统一的字段管理
        base_fields = self.get_table_base_fields('daily_quotes')
        calculated_field_mapping = self.get_table_calculated_fields('daily_quotes')
        # # debug: 看看不需要计算的话， 时间多久
        # calculated_field_mapping = {}
        
        return self._query_with_calculated_fields(
            table="daily_quotes",
            base_fields=base_fields,
            calculated_field_mapping=calculated_field_mapping,
            stock_list=stock_list,
            start_date=start_date,
            end_date=end_date,
            columns=columns,
            page=page,
            page_size=page_size,
            return_df=return_df
        )

    def get_daily_chips(
        self,
        stock_list: Optional[List[str]] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        columns: Optional[List[str]] = None,
        page: Optional[int] = None,
        page_size: Optional[int] = None,
        return_df: bool = False
    ) -> Union[Dict[str, Any], pd.DataFrame]:
        """
        获取股票日线筹码数据
        
        Args:
            stock_list: 股票代码列表，为None表示查询所有股票
            start_date: 开始日期，格式为'YYYY-MM-DD'，为None表示不限制开始日期
            end_date: 结束日期，格式为'YYYY-MM-DD'，为None表示不限制结束日期
            columns: 需要的筹码数据列，为None表示查询所有列
                    支持的列包括: instrument_id, trade_date, his_low, his_high,
                    cost_5pct, cost_15pct, cost_50pct, cost_85pct, cost_95pct,
                    weight_avg, winner_rate,
                    chip_conct_90 (筹码集中度，计算字段)
                    chip_conct_70 (筹码集中度，计算字段)
            page: 页码，从1开始，为None表示不分页
            page_size: 每页大小，为None表示不分页
            return_df: 是否返回DataFrame，默认False返回字典格式
            
        Returns:
            包含筹码数据和分页信息的字典，或DataFrame（当return_df=True时）
        """
        # 检查缓存是否有效
        if self._cache_manager.is_cache_valid('stock', start_date, end_date, stock_list):
            logger.info("使用缓存获取日线筹码数据")
            
            # 过滤筹码相关字段
            chips_columns = None
            if columns is not None:
                # 获取筹码表的所有字段
                all_chips_fields = self.get_table_all_fields('daily_chips')
                # 只保留筹码相关的字段
                chips_columns = list(set([col for col in columns if col in all_chips_fields or col in ['instrument_id', 'trade_date']]))
            else:
                chips_columns = list(set(self.get_table_all_fields('daily_chips')))
            
            if page is not None or page_size is not None or not return_df:
                raise ValueError("page and page_size are not supported for cache, and return_df must be True")
            
            return self._cache_manager.get_from_cache(
                cache_type='stock',
                start_date=start_date,
                end_date=end_date,
                item_list=stock_list,
                columns=chips_columns
            )
        
        # 缓存未命中，从数据库查询
        logger.info("缓存未命中，从数据库获取日线筹码数据")
        
        # 使用统一的字段管理
        base_fields = self.get_table_base_fields('daily_chips')
        calculated_field_mapping = self.get_table_calculated_fields('daily_chips')
        
        return self._query_with_calculated_fields(
            table="daily_chips",
            base_fields=base_fields,
            calculated_field_mapping=calculated_field_mapping,
            stock_list=stock_list,
            start_date=start_date,
            end_date=end_date,
            columns=columns,
            page=page,
            page_size=page_size,
            return_df=return_df
        )

    def get_daily_money_flow(
        self,
        stock_list: Optional[List[str]] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        columns: Optional[List[str]] = None,
        page: Optional[int] = None,
        page_size: Optional[int] = None,
        return_df: bool = False
    ) -> Union[Dict[str, Any], pd.DataFrame]:
        """
        获取股票日线资金流数据
        
        Args:
            stock_list: 股票代码列表，为None表示查询所有股票
            start_date: 开始日期，格式为'YYYY-MM-DD'，为None表示不限制开始日期
            end_date: 结束日期，格式为'YYYY-MM-DD'，为None表示不限制结束日期
            columns: 需要的资金流数据列，为None表示查询所有列
            page: 页码，从1开始，为None表示不分页
            page_size: 每页大小，为None表示不分页
            return_df: 是否返回DataFrame，默认False返回字典格式
            
        Returns:
            包含资金流数据和分页信息的字典，或DataFrame（当return_df=True时）
        """
        # 检查缓存是否有效
        if self._cache_manager.is_cache_valid('stock', start_date, end_date, stock_list):
            logger.info("使用缓存获取日线资金流数据")
            
            # 过滤资金流相关字段
            money_flow_columns = None
            if columns is not None:
                # 获取资金流表的所有字段
                all_money_flow_fields = self.get_table_all_fields('daily_money_flow')
                # 只保留资金流相关的字段
                money_flow_columns = list(set([col for col in columns if col in all_money_flow_fields or col in ['instrument_id', 'trade_date']]))
            else:
                money_flow_columns = list(set(self.get_table_all_fields('daily_money_flow')))
            
            if page is not None or page_size is not None or not return_df:
                raise ValueError("page and page_size are not supported for cache, and return_df must be True")
            
            return self._cache_manager.get_from_cache(
                cache_type='stock',
                start_date=start_date,
                end_date=end_date,
                item_list=stock_list,
                columns=money_flow_columns
            )
        
        # 缓存未命中，从数据库查询
        logger.info("缓存未命中，从数据库获取日线资金流数据")
        
        # 使用统一的查询方法，资金流数据没有计算字段
        base_fields = self.get_table_base_fields('daily_money_flow')
        calculated_field_mapping = self.get_table_calculated_fields('daily_money_flow')
        
        return self._query_with_calculated_fields(
            table="daily_money_flow",
            base_fields=base_fields,
            calculated_field_mapping=calculated_field_mapping,
            stock_list=stock_list,
            start_date=start_date,
            end_date=end_date,
            columns=columns,
            page=page,
            page_size=page_size,
            return_df=return_df
        )

    def get_daily_index(
        self,
        index_list: Optional[List[str]] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        columns: Optional[List[str]] = None,
        page: Optional[int] = None,
        page_size: Optional[int] = None,
        return_df: bool = False
    ) -> Union[Dict[str, Any], pd.DataFrame]:
        """
        获取指数日线数据
        
        Args:
            index_list: 指数代码列表，为None表示查询所有指数
            start_date: 开始日期，格式为'YYYY-MM-DD'，为None表示不限制开始日期
            end_date: 结束日期，格式为'YYYY-MM-DD'，为None表示不限制结束日期
            columns: 需要的指数数据列，为None表示查询所有列
                    支持的列包括: instrument_id, trade_date, open, high, low, close, 
                    volume, amount, change, pct_chg
            page: 页码，从1开始，为None表示不分页
            page_size: 每页大小，为None表示不分页
            return_df: 是否返回DataFrame，默认False返回字典格式
            
        Returns:
            包含指数数据和分页信息的字典，或DataFrame（当return_df=True时）
        """
        # 检查指数缓存是否有效
        if self._cache_manager.is_cache_valid('index', start_date, end_date, index_list):
            logger.info("使用缓存获取指数数据")
            
            if page is not None or page_size is not None or not return_df:
                raise ValueError("page and page_size are not supported for cache, and return_df must be True")
            
            return self._cache_manager.get_from_cache(
                cache_type='index',
                start_date=start_date,
                end_date=end_date,
                item_list=index_list,
                columns=columns
            )
        
        # 缓存未命中，从数据库查询
        logger.info("缓存未命中，从数据库获取指数数据")
        
        # 使用统一的字段管理
        base_fields = self.get_table_base_fields('daily_index')
        calculated_field_mapping = self.get_table_calculated_fields('daily_index')
        
        return self._query_with_calculated_fields(
            table="daily_index",
            base_fields=base_fields,
            calculated_field_mapping=calculated_field_mapping,
            stock_list=index_list,  # 复用stock_list参数名，实际传入指数列表
            start_date=start_date,
            end_date=end_date,
            columns=columns,
            page=page,
            page_size=page_size,
            return_df=return_df
        )

    def get_daily_sector_quotes(
        self,
        stock_list: Optional[List[str]] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        columns: Optional[List[str]] = None,
        page: Optional[int] = None,
        page_size: Optional[int] = None,
        return_df: bool = False
    ) -> Union[Dict[str, Any], pd.DataFrame]:
        """
        获取板块日线数据
        
        Args:
            stock_list: 板块代码列表，为None表示查询所有板块
            start_date: 开始日期，格式为'YYYY-MM-DD'，为None表示不限制开始日期
            end_date: 结束日期，格式为'YYYY-MM-DD'，为None表示不限制结束日期
            columns: 需要的板块数据列，为None表示查询所有列
            page: 页码，从1开始，为None表示不分页
            page_size: 每页大小，为None表示不分页
            return_df: 是否返回DataFrame，默认False返回字典格式
            
        Returns:
            包含板块数据和分页信息的字典，或DataFrame（当return_df=True时）
        """
        # 使用统一的字段管理
        base_fields = self.get_table_base_fields('daily_sector_quotes')
        calculated_field_mapping = self.get_table_calculated_fields('daily_sector_quotes')
        
        return self._query_with_calculated_fields(
            table="daily_sector_quotes",
            base_fields=base_fields,
            calculated_field_mapping=calculated_field_mapping,
            stock_list=stock_list,  # 板块代码列表
            start_date=start_date,
            end_date=end_date,
            columns=columns,
            page=page,
            page_size=page_size,
            return_df=return_df
        )

    def get_stock_5min_data(
        self,
        stock_code: str,
        start_date: str,
        end_date: str,
        columns: Optional[List[str]] = None,
        page: Optional[int] = None,
        page_size: Optional[int] = None,
        return_df: bool = False
    ) -> Union[Dict[str, Any], pd.DataFrame]:
        """
        获取股票5分钟K线数据
        
        Args:
            stock_code: 股票代码，如 "600519.SH"
            start_date: 开始日期，格式为'YYYY-MM-DD'
            end_date: 结束日期，格式为'YYYY-MM-DD'
            columns: 需要的5分钟数据列，为None表示查询所有列
                    支持的列包括: instrument_id, trade_time, open, high, low, close,
                    volume, amount, trade_date
            page: 页码，从1开始，为None表示不分页
            page_size: 每页大小，为None表示不分页
            return_df: 是否返回DataFrame，默认False返回字典格式
            
        Returns:
            包含5分钟K线数据和分页信息的字典，或DataFrame（当return_df=True时）
        """
        # 参数验证
        self._validate_pagination_params(page, page_size)
        
        if not stock_code:
            raise ValueError("股票代码不能为空")
        
        if not start_date or not end_date:
            raise ValueError("开始日期和结束日期不能为空")
        
        try:
            # 构建时间范围条件：将日期转换为具体的时间范围
            # start_date 09:30:00 到 end_date 15:00:00
            start_datetime = f"{start_date} 09:30:00"
            end_datetime = f"{end_date} 15:00:00"
            
            # 构建条件和参数 - 使用trade_time而不是trade_date
            where_conditions = []
            params = {}
            
            # 添加股票代码条件
            where_conditions.append("instrument_id = %(stock_code)s")
            params["stock_code"] = stock_code
            
            # 添加时间范围条件 - 使用trade_time
            where_conditions.append("trade_time BETWEEN parseDateTime64BestEffort(%(start_datetime)s) AND parseDateTime64BestEffort(%(end_datetime)s)")
            params["start_datetime"] = start_datetime
            params["end_datetime"] = end_datetime
            
            # 使用统一的字段管理
            base_fields = self.get_table_base_fields('stock_5_min')
            calculated_field_mapping = self.get_table_calculated_fields('stock_5_min')
            
            # 自定义排序：按交易时间正序排列
            order_by = [("trade_time", "ASC")]
            
            # 生成 SQL 类型转换映射
            sql_cast_mapping = self._generate_sql_cast_mapping('stock_5_min')
            
            # 使用 SQL Builder 构建查询
            data_query, count_query = self.sql_builder.build_calculated_field_query(
                table='stock_5_min',
                base_fields=base_fields,
                calculated_field_mapping=calculated_field_mapping,
                columns=columns,
                where_conditions=where_conditions,
                order_by=order_by,
                limit=page_size if page is not None else None,
                offset=(page - 1) * page_size if page is not None and page_size is not None else None,
                sql_type_cast=sql_cast_mapping
            )
            
            # 执行查询
            count_result = self.clickhouse_client.execute(count_query, params)
            total = count_result[0]['total_count'] if count_result else 0
            
            # 直接获取DataFrame，类型已在SQL层面转换
            data_df = self.clickhouse_client.execute(data_query, params, return_df=True)
            
            # 返回结果
            if return_df:
                # 直接返回DataFrame
                return data_df
            else:
                # 转换为字典列表以保持API兼容性
                data = data_df.to_dict('records') if not data_df.empty else []
                
                # 返回原有的字典格式
                if page is None or page_size is None:
                    return {
                        "data": data,
                        "total": total,
                        "page": 1,
                        "page_size": total
                    }
                else:
                    return {
                        "data": data,
                        "total": total,
                        "page": page,
                        "page_size": page_size
                    }
                
        except Exception as e:
            logger.error(f"查询股票{stock_code}的5分钟数据失败: {e}")
            return {
                "data": [],
                "total": 0,
                "page": page or 1,
                "page_size": page_size or 0
            }

    def get_stock_industry(
        self,
        stock_list: Optional[List[str]] = None,
        columns: Optional[List[str]] = None,
        page: Optional[int] = None,
        page_size: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        获取股票行业分类数据（最新数据）
        
        Args:
            stock_list: 股票代码列表，为None表示查询所有股票
            columns: 需要的行业数据列，为None表示查询所有列
            page: 页码，从1开始，为None表示不分页
            page_size: 每页大小，为None表示不分页
            
        Returns:
            包含行业分类数据和分页信息的字典
        """
        # 使用统一的分页参数验证
        self._validate_pagination_params(page, page_size)
            
        try:
            # 构建查询条件
            complex_conditions = []
            
            # 添加股票代码条件
            if stock_list:
                complex_conditions.append({
                    "column": "instrument_id",
                    "operator": "IN",
                    "value": stock_list
                })
            
            # 默认查询最新的行业分类数据
            complex_conditions.append({
                "column": "is_new",
                "operator": "=",
                "value": 1
            })
            
            # 排序条件
            order_by = [("instrument_id", "ASC")]
            
            # 计算分页参数
            offset = None if page is None or page_size is None else (page - 1) * page_size
            limit = page_size
            
            # 统一使用带计数的查询
            data, total = self.clickhouse_client.select(
                table="stock_industry",
                columns=columns,
                complex_conditions=complex_conditions,
                order_by=order_by,
                limit=limit,
                offset=offset,
                return_count=True
            )
            
            # 根据是否分页设置返回值
            if page is None or page_size is None:
                return {
                    "data": data,
                    "total": total,
                    "page": 1,
                    "page_size": total
                }
            else:
                return {
                    "data": data,
                    "total": total,
                    "page": page,
                    "page_size": page_size
                }
            
        except Exception as e:
            logger.error(f"查询行业数据失败: {e}")
            return {
                "data": [],
                "total": 0,
                "page": page or 1,
                "page_size": page_size or 0
            }

    def get_stock_industry_l1(
        self,
        stock_list: List[str]
    ) -> Dict[str, Any]:
        """
        根据股票列表获取一级行业信息（l1_code和l1_name）
        
        Args:
            stock_list: 股票代码列表
            
        Returns:
            包含股票代码、l1_code、l1_name的字典
        """
        if not stock_list:
            raise ValueError("股票列表不能为空")
            
        try:
            # 只查询需要的字段：股票代码、一级行业代码、一级行业名称
            columns = ["instrument_id", "l1_code", "l1_name"]
            
            # 调用已有的方法
            result = self.get_stock_industry(
                stock_list=stock_list,
                columns=columns,
                page=None,  # 不分页，返回所有数据
                page_size=None
            )
            
            return result
            
        except Exception as e:
            logger.error(f"查询股票一级行业信息失败: {e}")
            return {
                "data": [],
                "total": 0,
                "page": 1,
                "page_size": 0
            }

    def get_stock_quote(
        self,
        stock_code: Optional[str] = None,
        trade_date: Optional[str] = None,
        page: Optional[int] = None,
        page_size: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        获取股票行情数据
        
        Args:
            stock_code: 股票代码，为None表示查询所有股票
            trade_date: 交易日期，为None表示查询所有日期
            page: 页码，从1开始，为None表示不分页
            page_size: 每页大小，为None表示不分页
            
        Returns:
            包含行情数据和分页信息的字典
        """
        if not stock_code and not trade_date:
            raise ValueError("股票代码和交易日期不能同时为空")
        
        # 使用统一的分页参数验证
        self._validate_pagination_params(page, page_size)
            
        complex_conditions = []
        
        if stock_code:
            complex_conditions.append({
                "column": "instrument_id",
                "operator": "=",
                "value": stock_code
            })
        
        if trade_date:
            complex_conditions.append({
                "column": "trade_date",
                "operator": "=",
                "value": trade_date
            })
            
        # 排序条件
        order_by = [("instrument_id", "ASC"), ("trade_date", "DESC")]
        
        try:
            # 计算分页参数
            offset = None if page is None or page_size is None else (page - 1) * page_size
            limit = page_size
            
            # 统一使用带计数的查询
            data, total = self.clickhouse_client.select(
                table="daily_quotes",
                complex_conditions=complex_conditions,
                order_by=order_by,
                limit=limit,
                offset=offset,
                return_count=True
            )
            
            # 根据是否分页设置返回值
            if page is None or page_size is None:
                return {
                    "data": data,
                    "total": total,
                    "page": 1,
                    "page_size": total
                }
            else:
                return {
                    "data": data,
                    "total": total,
                    "page": page,
                    "page_size": page_size
                }
        except Exception as e:
            logger.error(f"查询股票行情数据失败: {e}")
            return {
                "data": [],
                "total": 0,
                "page": page or 1,
                "page_size": page_size or 0
            }

    def get_latest_quotes(self, stock_list: List[str]) -> List[Dict]:
        """
        获取股票列表的最新行情
        
        Args:
            stock_list: 股票代码列表
            
        Returns:
            最新行情数据字典列表
        """
        if not stock_list:
            raise ValueError("股票列表不能为空")
            
        try:
            # 这里使用原始SQL执行，因为需要使用窗口函数或子查询
            stock_placeholders = ", ".join([f"'{s}'" for s in stock_list])
            query = f"""
            SELECT *
            FROM (
                SELECT *,
                       row_number() OVER (PARTITION BY instrument_id ORDER BY trade_date DESC) as rn
                FROM daily_quotes
                WHERE instrument_id IN ({stock_placeholders})
            ) AS ranked
            WHERE rn = 1
            ORDER BY instrument_id
            """
            
            return self.clickhouse_client.execute(query)
        except Exception as e:
            logger.error(f"查询最新行情数据失败: {e}")
            return []

    def _get_table_schema(self, table: str) -> Dict[str, str]:
        """
        获取表的列类型信息（带缓存）
        
        Args:
            table: 表名
            
        Returns:
            {column_name: column_type} 的映射
        """
        if table not in self._schema_cache:
            try:
                query = """
                SELECT name, type 
                FROM system.columns 
                WHERE database = %(database)s AND table = %(table)s
                """
                
                result = self.clickhouse_client.execute(query, {
                    'database': self.clickhouse_client.connection_params['database'],
                    'table': table
                })
                
                self._schema_cache[table] = {row['name']: row['type'] for row in result}
                
            except Exception as e:
                logger.error(f"Error getting table schema for {table}: {e}")
                self._schema_cache[table] = {}
        
        return self._schema_cache[table]

    def _generate_sql_cast_mapping(self, table: str) -> Dict[str, str]:
        """
        基于现有类型推断逻辑，生成 SQL CAST 映射
        
        Args:
            table: 表名
            
        Returns:
            {column_name: "CAST(...) AS column_name"} 的映射
        """
        schema = self._get_table_schema(table)
        cast_mapping = {}
        
        for column, clickhouse_type in schema.items():

            # 根据 ClickHouse 类型生成相应的 CAST 语句
            if clickhouse_type.startswith('FixedString'):
                # FixedString(n) -> String
                cast_mapping[column] = f"CAST({column} AS String) AS {column}"
                
            elif clickhouse_type == 'Date':
                # Date -> 保持原样，pandas 会自动识别
                cast_mapping[column] = column
                
            elif clickhouse_type.startswith('Decimal') or clickhouse_type.startswith('Nullable(Decimal'):
                # Decimal32(4), Decimal64(2), Nullable(Decimal32(4)) etc. -> Float64
                if clickhouse_type.startswith('Nullable('):
                    cast_mapping[column] = f"CAST({column} AS Nullable(Float64)) AS {column}"
                else:
                    cast_mapping[column] = f"CAST({column} AS Float64) AS {column}"
                
            elif clickhouse_type in ['UInt32', 'UInt16', 'UInt8'] or clickhouse_type.startswith('Nullable(UInt'):
                # UInt*, Nullable(UInt*) -> Int64
                if clickhouse_type.startswith('Nullable('):
                    cast_mapping[column] = f"CAST({column} AS Nullable(Int64)) AS {column}"
                else:
                    cast_mapping[column] = f"CAST({column} AS Int64) AS {column}"
                
            elif clickhouse_type == 'DateTime':
                # DateTime -> 保持原样，pandas 会自动识别
                cast_mapping[column] = column
                
            else:
                # 其他类型保持原样
                cast_mapping[column] = column
        
        return cast_mapping

    def _get_and_convert_quotes(self, stock_list: Optional[List[str]], start_date: str, end_date: str) -> pd.DataFrame:
        """获取行情数据并返回DataFrame（类型已在SQL层面转换）"""
        # 直接使用带优化的get_daily_quotes方法
        df = self.get_daily_quotes(
            stock_list=stock_list,
            start_date=start_date,
            end_date=end_date,
            return_df=True
        )
        
        return df if isinstance(df, pd.DataFrame) else pd.DataFrame()

    def _get_and_convert_chips(self, stock_list: Optional[List[str]], start_date: str, end_date: str) -> pd.DataFrame:
        """获取筹码数据并返回DataFrame（类型已在SQL层面转换）"""
        # 直接使用带优化的get_daily_chips方法
        df = self.get_daily_chips(
            stock_list=stock_list,
            start_date=start_date,
            end_date=end_date,
            return_df=True
        )
        
        return df if isinstance(df, pd.DataFrame) else pd.DataFrame()

    def _get_and_convert_money_flow(self, stock_list: Optional[List[str]], start_date: str, end_date: str) -> pd.DataFrame:
        """获取资金流数据并返回DataFrame（类型已在SQL层面转换）"""
        # 直接使用带优化的get_daily_money_flow方法
        df = self.get_daily_money_flow(
            stock_list=stock_list,
            start_date=start_date,
            end_date=end_date,
            return_df=True
        )
        
        return df if isinstance(df, pd.DataFrame) else pd.DataFrame()

    def _split_date_range(self, start_date: str, end_date: str, batch_years: int) -> List[Tuple[str, str]]:
        """
        将大的日期范围分割成多个小的查询区间
        
        Args:
            start_date: 开始日期，格式为'YYYY-MM-DD'
            end_date: 结束日期，格式为'YYYY-MM-DD'
            batch_years: 分批查询的年数间隔
            
        Returns:
            [(start1, end1), (start2, end2), ...] 日期区间列表
        """
        try:
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')
            
            date_ranges = []
            current_start = start_dt
            
            while current_start <= end_dt:
                # 计算当前批次的结束日期
                current_end = current_start + rd.relativedelta(years=batch_years) - timedelta(days=1)
                if current_end > end_dt:
                    current_end = end_dt
                
                date_ranges.append((
                    current_start.strftime('%Y-%m-%d'),
                    current_end.strftime('%Y-%m-%d')
                ))
                
                # 下一个批次的开始日期
                current_start = current_start + rd.relativedelta(years=batch_years)
            
            return date_ranges
            
        except Exception as e:
            logger.error(f"Error splitting date range: {e}")
            # 如果分割失败，返回原始日期范围
            return [(start_date, end_date)]

    def _get_batch_data(
        self,
        stock_list: Optional[List[str]],
        start_date: str,
        end_date: str,
        include_quotes: bool = True,
        include_chips: bool = True,
        include_money_flow: bool = True
    ) -> pd.DataFrame:
        """
        获取单个批次的合并数据
        
        Args:
            stock_list: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期
            include_quotes: 是否包含行情数据
            include_chips: 是否包含筹码数据
            include_money_flow: 是否包含资金流数据
            
        Returns:
            合并后的DataFrame
        """
        # 1. 获取行情数据作为基准
        if not include_quotes:
            raise ValueError("行情数据是必需的，不能设置 include_quotes=False")
        
        start_time = time.time()
        quotes_df = self._get_and_convert_quotes(stock_list, start_date, end_date)
        logger.info(f"行情数据获取和转换时间: {time.time() - start_time} 秒")
        if quotes_df.empty:
            return pd.DataFrame()
        
        result_df = quotes_df
        
        
        # 2. 合并筹码数据 - 始终保持列结构一致性
        if include_chips:
            start_time = time.time()
            chips_df = self._get_and_convert_chips(stock_list, start_date, end_date)
            logger.info(f"筹码数据获取和转换时间: {time.time() - start_time} 秒")

            start_time = time.time()
            if not chips_df.empty:
                # 有筹码数据时正常合并
                result_df = result_df.merge(chips_df, on=['instrument_id', 'trade_date'], how='left')
            else:
                # 没有筹码数据时，添加空的筹码列以保持结构一致性
                chips_columns = self.get_table_all_fields('daily_chips')
                # 排除已经存在的基础字段和排除字段
                excluded = ['instrument_id', 'trade_date']
                chips_columns = [col for col in chips_columns if col not in excluded]
                for col in chips_columns:
                    # 使用适当的数据类型初始化，而不是None
                    if col in ['his_low', 'his_high', 'cost_5pct', 'cost_15pct', 'cost_50pct', 
                               'cost_85pct', 'cost_95pct', 'weight_avg', 'winner_rate', 'chip_conct_90', 'chip_conct_70']:
                        result_df[col] = pd.Series(dtype='float64')
                    else:
                        result_df[col] = None
            logger.info(f"筹码数据合并时间: {time.time() - start_time} 秒")
        
        # 3. 合并资金流数据 - 始终保持列结构一致性
        if include_money_flow:
            start_time = time.time()
            money_flow_df = self._get_and_convert_money_flow(stock_list, start_date, end_date)
            logger.info(f"资金流数据获取和转换时间: {time.time() - start_time} 秒")

            start_time = time.time()
            if not money_flow_df.empty:
                # 有资金流数据时正常合并
                result_df = result_df.merge(money_flow_df, on=['instrument_id', 'trade_date'], how='left')
            else:
                # 没有资金流数据时，添加空的资金流列以保持结构一致性
                money_flow_columns = self.get_table_all_fields('daily_money_flow')
                # 排除已经存在的基础字段和排除字段
                excluded = ['instrument_id', 'trade_date']
                money_flow_columns = [col for col in money_flow_columns if col not in excluded]
                for col in money_flow_columns:
                    # 使用适当的数据类型初始化
                    if col.endswith('_vol'):
                        result_df[col] = pd.Series(dtype='Int64')  # 成交量用整数
                    elif col.endswith('_amount'):
                        result_df[col] = pd.Series(dtype='float64')  # 金额用浮点数
                    else:
                        result_df[col] = None
            logger.info(f"资金流数据合并时间: {time.time() - start_time} 秒")
        
        return result_df

    def get_combined_stock_data(
        self,
        start_date: str,
        end_date: str,
        stock_list: Optional[List[str]] = None,
        batch_years: int = 3,
        include_quotes: bool = True,
        include_chips: bool = True,
        include_money_flow: bool = True
    ) -> pd.DataFrame:
        """
        获取合并的股票数据（行情+筹码+资金流）
        
        Args:
            start_date: 开始日期，格式为'YYYY-MM-DD'
            end_date: 结束日期，格式为'YYYY-MM-DD'
            stock_list: 股票代码列表，为None表示查询所有股票
            batch_years: 分批查询的年数间隔，默认3年
            include_quotes: 是否包含行情数据，默认True（必需）
            include_chips: 是否包含筹码数据，默认True
            include_money_flow: 是否包含资金流数据，默认True
            
        Returns:
            合并后的DataFrame，索引为[trade_date, instrument_id]
            
        Raises:
            ValueError: 当参数无效时抛出
        """
        if not include_quotes:
            raise ValueError("行情数据是必需的，不能设置 include_quotes=False")
        
        if not start_date or not end_date:
            raise ValueError("start_date 和 end_date 不能为空")
        
        try:
            if self._cache_manager.is_cache_valid('stock', start_date, end_date, stock_list) and include_quotes and include_chips and include_money_flow:
                logger.info("从缓存中获取数据")
                return self._cache_manager.get_from_cache('stock', start_date, end_date, stock_list)
            
            # 1. 分割日期范围
            date_ranges = self._split_date_range(start_date, end_date, batch_years)
            logger.info(f"将查询分为 {len(date_ranges)} 个批次: {date_ranges}")
            
            # 2. 分批获取数据
            all_batches = []
            for i, (batch_start, batch_end) in enumerate(date_ranges):
                logger.info(f"正在处理批次 {i+1}/{len(date_ranges)}: {batch_start} 到 {batch_end}")
            
                batch_df = self._get_batch_data(
                    stock_list=stock_list,
                    start_date=batch_start,
                    end_date=batch_end,
                    include_quotes=include_quotes,
                    include_chips=include_chips,
                    include_money_flow=include_money_flow
                )
            
                if not batch_df.empty:
                    all_batches.append(batch_df)
                    logger.info(f"批次 {i+1} 获取到 {len(batch_df)} 条记录")
                else:
                    logger.info(f"批次 {i+1} 没有数据")
            
            # 3. 合并所有批次数据
            if not all_batches:
                logger.warning("所有批次都没有数据")
                return pd.DataFrame()
            
            logger.info(f"正在合并 {len(all_batches)} 个批次的数据...")
            # 过滤掉空的DataFrame，避免pandas concat警告
            non_empty_batches = [df for df in all_batches if not df.empty]
            
            if not non_empty_batches:
                logger.warning("所有批次的数据都为空")
                return pd.DataFrame()
            
            # 合并非空的批次数据，指定数据类型以避免警告
            final_df = pd.concat(non_empty_batches, ignore_index=True, sort=False)
                    
            logger.info(f"合并完成，总共 {len(final_df)} 条记录")
            
            return final_df
            
        except Exception as e:
            logger.error(f"获取合并股票数据失败: {e}")
            logger.error(traceback.format_exc())
            raise

    def cache_combined_data(
        self,
        start_date: str,
        end_date: str,
        stock_list: Optional[List[str]] = None,
        batch_years: int = 3
    ) -> None:
        """
        缓存合并的股票数据到内存
        
        Args:
            start_date: 开始日期，格式为'YYYY-MM-DD'
            end_date: 结束日期，格式为'YYYY-MM-DD'
            stock_list: 股票代码列表，为None表示查询所有股票
            batch_years: 分批查询的年数间隔，默认3年
        """
        try:
            if not start_date or not end_date:
                raise ValueError("缓存的 start_date 和 end_date 不能为空")
            
            logger.info(f"开始缓存合并数据: {start_date} 到 {end_date}, 股票数量: {len(stock_list)} 只")

            # 获取合并数据
            df = self.get_combined_stock_data(
                start_date=start_date,
                end_date=end_date,
                stock_list=stock_list,
                batch_years=batch_years,
                include_quotes=True,
                include_chips=True,
                include_money_flow=True
            )
            
            df = df.set_index(['trade_date', 'instrument_id']).sort_index()
            
            if df.empty:
                logger.warning("没有数据可缓存")
                return
            

            # # 重置索引，让trade_date和instrument_id变成普通列，便于后续过滤
            # df = df.reset_index()

            # 使用缓存管理器缓存数据
            self._cache_manager.cache_data(
                cache_type='stock',
                data=df,
                start_date=start_date,
                end_date=end_date,
                item_list=stock_list
            )
            
        except Exception as e:
            logger.error(f"缓存数据失败: {e}")
            raise

    def clear_cache(self, cache_type: str = 'stock') -> None:
        """清空指定类型的缓存"""
        self._cache_manager.clear_cache(cache_type)

    def get_cache_info(self, cache_type: str = 'stock') -> Optional[Dict[str, Any]]:
        """获取缓存信息（用于调试）"""
        return self._cache_manager.get_cache_info(cache_type)

    def cache_index_data(
        self,
        start_date: str,
        end_date: str,
        index_list: Optional[List[str]] = None
    ) -> None:
        """
        缓存指数数据到内存
        
        Args:
            start_date: 开始日期，格式为'YYYY-MM-DD'
            end_date: 结束日期，格式为'YYYY-MM-DD'
            index_list: 指数代码列表，为None表示查询所有指数
        """
        try:
            if not start_date or not end_date:
                raise ValueError("缓存的 start_date 和 end_date 不能为空")
            
            logger.info(f"开始缓存指数数据: {start_date} 到 {end_date}, 指数数量: {len(index_list) if index_list else '全部'} 个")

            # 获取指数数据
            df = self.get_daily_index(
                index_list=index_list,
                start_date=start_date,
                end_date=end_date,
                return_df=True
            )
            
            if df.empty:
                logger.warning("没有指数数据可缓存")
                return
            
            df = df.set_index(['trade_date', 'instrument_id']).sort_index()
            
            # 使用缓存管理器缓存指数数据
            self._cache_manager.cache_data(
                cache_type='index',
                data=df,
                start_date=start_date,
                end_date=end_date,
                item_list=index_list
            )
            
        except Exception as e:
            logger.error(f"缓存指数数据失败: {e}")
            raise