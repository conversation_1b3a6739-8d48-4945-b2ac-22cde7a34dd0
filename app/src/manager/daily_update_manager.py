from __future__ import annotations
import sys
import time
import os
import json
from datetime import datetime, timedelta, UTC
import pytz
from typing import Optional, Literal, List, Dict, Any, Callable
import pathlib
import threading
import traceback

import pandas as pd
from utils.sse_calendar import get_trade_calendar
from utils.clickhouse_client import ClickHouseClient

from stock_collector import (
    MarketCollector, 
    ChipCollector, 
    MoneyflowCollector, 
    IndexCollector, 
    MinutesCollector,
    SectorCollector
)

from app.server.core.log import logger
from app.server.settings import settings


class DailyUpdateManager:
    """每日数据更新管理器"""
    _instance_lock = threading.Lock()
    
    def __init__(self, db_client: ClickHouseClient):
        """
        初始化每日更新管理器
        
        Args:
            db_client: ClickHouse数据库客户端
        """
        self.db_client = db_client
        if not self.db_client:
            raise ValueError("DailyUpdateManager需要配置ClickHouse数据库连接")
        
        # 构建collector配置
        config = {
            'token': settings.tushare_token,
            'data_dir': None,
            'db_config': {
                'host': settings.clickhouse_host,
                'port': settings.clickhouse_port,
                'user': settings.clickhouse_user,
                'password': settings.clickhouse_password,
                'database': settings.clickhouse_database
            }
        }
        
        # 初始化各种数据收集器
        self.market_collector = MarketCollector(config, db_client)
        self.chip_collector = ChipCollector(config, db_client)
        self.moneyflow_collector = MoneyflowCollector(config, db_client)
        self.index_collector = IndexCollector(config, db_client)
        self.minutes_collector = MinutesCollector(config, db_client)
        self.sector_collector = SectorCollector(config, db_client)
    
    @classmethod
    def get_instance(cls, db_client: ClickHouseClient = None) -> DailyUpdateManager:
        if not hasattr(cls, "_instance"):
            """Get cls instance."""
            with cls._instance_lock:
                if not hasattr(cls, "_instance"):
                    cls._instance = cls(db_client)
        return cls._instance

    def get_beijing_yesterday(self) -> str:
        """
        获取北京时间前一天，格式YYYYMMDD
        
        Returns:
            str: 前一天日期，格式YYYYMMDD
        """
        try:
            # 获取北京时间
            beijing_tz = pytz.timezone('Asia/Shanghai')
            beijing_now = datetime.now(beijing_tz)
        except ImportError:
            # 如果没有pytz，使用UTC+8小时近似
            beijing_now = datetime.now(UTC) + timedelta(hours=8)
        
        # 获取前一天
        yesterday = beijing_now - timedelta(days=1)
        
        return yesterday.strftime('%Y%m%d')
    
    def get_or_update_trade_calendar(self, end_date: str) -> pd.DataFrame:
        """
        获取或更新交易日历数据
        
        Args:
            end_date: 结束日期，格式YYYYMMDD
            
        Returns:
            pd.DataFrame: 交易日历数据
        """
        calendar_file = str(pathlib.Path(__file__).parent.parent.parent.parent / '.cache/trade_calendar.csv')
        
        # 检查是否需要更新交易日历
        need_update = True
        
        # 如果文件存在，检查最新日期
        if os.path.exists(calendar_file):
            existing_df = pd.read_csv(calendar_file)
            if not existing_df.empty:
                # 获取最新的cal_date（数据按日期降序排列，第一行是最新的）
                latest_cal_date = str(int(existing_df.iloc[0]['cal_date']))
                if latest_cal_date == end_date:
                    need_update = False
                    return existing_df
                else:
                    logger.info(f"交易日历数据需要更新（当前最新日期: {latest_cal_date}，目标日期: {end_date}）")
        else:
            logger.info("交易日历文件不存在，将重新获取")
    
        if need_update:
            logger.info("更新交易日历数据...")
            calendar_df = get_trade_calendar(
                exchange='SSE',
                start_date='20121201',
                end_date=end_date,
                output_file=calendar_file,
                is_open='',
                token=settings.tushare_token
            )
            
            if calendar_df is None or calendar_df.empty:
                raise ValueError("无法获取交易日历数据")
            return calendar_df
    
    def daily_update_market_data(
        self, 
        stock_codes_path: str, 
        latest_trade_date: str, 
        latest_date_in_db: str = None, 
        calendar_df: pd.DataFrame = None,
    ) -> Dict[str, Any]:
        """
        每日更新股票行情数据
        
        Args:
            stock_codes_path: 股票代码列表文件路径
            latest_trade_date: 最新交易日期
            latest_date_in_db: 数据库中最新日期，如果为None则自动查询
            calendar_df: 交易日历数据，如果为None则自动获取
            table_name: 目标表名
        
        Returns:
            Dict[str, Any]: 更新结果统计
        """
        try:
            # 加载股票代码
            stock_codes = self.market_collector.load_stock_codes_from_json(stock_codes_path)
                 
            # 调用市场数据收集器的每日更新方法
            return self.market_collector.daily_update(
                stock_codes=stock_codes,
                latest_date_in_db=latest_date_in_db,
                end_date=latest_trade_date,
                calendar_df=calendar_df
            )
            
        except Exception as e:
            logger.error(f"每日市场数据更新失败: {str(e)}")
            traceback.print_exc()
            return {"error": str(e), "imported_count": 0}
    
    def daily_update_cyq_data(
        self, 
        stock_codes_path: str, 
        latest_trade_date: str, 
        latest_date_in_db: str = None, 
        calendar_df: pd.DataFrame = None
    ) -> Dict[str, Any]:
        """
        每日更新筹码数据
        
        Args:
            stock_codes_path: 股票代码列表文件路径
            latest_trade_date: 最新交易日期
            latest_date_in_db: 数据库中最新日期，如果为None则自动查询
            calendar_df: 交易日历数据，如果为None则自动获取
            
        Returns:
            Dict[str, Any]: 更新结果统计
        """
        try:
            # 加载股票代码
            stock_codes = self.chip_collector.load_stock_codes_from_json(stock_codes_path)

            
            # 调用筹码数据收集器的每日更新方法
            return self.chip_collector.daily_update(
                stock_codes=stock_codes,
                latest_date_in_db=latest_date_in_db,
                end_date=latest_trade_date,
                calendar_df=calendar_df
            )
            
        except Exception as e:
            logger.error(f"每日筹码数据更新失败: {str(e)}")
            traceback.print_exc()
            return {"error": str(e), "imported_count": 0}
    
    def daily_update_moneyflow_data(
        self, 
        stock_codes_path: str, 
        latest_trade_date: str, 
        latest_date_in_db: str = None, 
        calendar_df: pd.DataFrame = None
    ) -> Dict[str, Any]:
        """
        每日更新资金流数据
        
        Args:
            stock_codes_path: 股票代码列表文件路径
            latest_trade_date: 最新交易日期
            latest_date_in_db: 数据库中最新日期，如果为None则自动查询
            calendar_df: 交易日历数据，如果为None则自动获取
            
        Returns:
            Dict[str, Any]: 更新结果统计
        """
        try:
            # 加载股票代码
            stock_codes = self.moneyflow_collector.load_stock_codes_from_json(stock_codes_path)
            
            # 调用资金流数据收集器的每日更新方法
            return self.moneyflow_collector.daily_update(
                stock_codes=stock_codes,
                latest_date_in_db=latest_date_in_db,
                end_date=latest_trade_date,
                calendar_df=calendar_df
            )
            
        except Exception as e:
            logger.error(f"每日资金流数据更新失败: {str(e)}")
            traceback.print_exc()
            return {"error": str(e), "imported_count": 0}
    
    def daily_update_minutes_data(
        self, 
        stock_codes_path: str, 
        latest_trade_date: str, 
        latest_date_in_db: str = None, 
        calendar_df: pd.DataFrame = None
    ) -> Dict[str, Any]:
        """
        每日更新分钟数据
        """
        try:
            # 加载股票代码
            stock_codes = self.minutes_collector.load_stock_codes_from_json(stock_codes_path)
            
            # 调用分钟数据收集器的每日更新方法
            return self.minutes_collector.daily_update(
                stock_codes=stock_codes,
                latest_date_in_db=latest_date_in_db,
                end_date=latest_trade_date,
                calendar_df=calendar_df
            )
            
        except Exception as e:  
            logger.error(f"每日分钟数据更新失败: {str(e)}")
            traceback.print_exc()
            return {"error": str(e), "imported_count": 0}
    
    def daily_update_index_data(
        self, 
        index_codes_path: str, 
        latest_trade_date: str, 
        latest_date_in_db: str = None, 
        calendar_df: pd.DataFrame = None,
    ) -> Dict[str, Any]:
        """
        每日更新指数数据
        
        Args:
            index_codes_path: 指数代码列表文件路径
            latest_trade_date: 最新交易日期
            latest_date_in_db: 数据库中最新日期，如果为None则自动查询
            calendar_df: 交易日历数据，如果为None则自动获取
            
        Returns:
            Dict[str, Any]: 更新结果统计
        """
        try:
            # 加载指数代码
            index_codes = self.index_collector.load_stock_codes_from_json(index_codes_path)
            
            # 调用指数数据收集器的每日更新方法
            return self.index_collector.daily_update(
                stock_codes=index_codes,
                latest_date_in_db=latest_date_in_db,
                end_date=latest_trade_date,
                calendar_df=calendar_df,
            )
            
        except Exception as e:
            logger.error(f"每日指数数据更新失败: {str(e)}")
            traceback.print_exc()
            return {"error": str(e), "imported_count": 0}

    def daily_update_sector_data(
        self, 
        sector_codes_path: str, 
        latest_trade_date: str, 
        latest_date_in_db: str = None, 
        calendar_df: pd.DataFrame = None,
    ) -> Dict[str, Any]:
        """
        每日更新指数数据
        
        Args:
            sector_codes_path: 指数代码列表文件路径
            latest_trade_date: 最新交易日期
            latest_date_in_db: 数据库中最新日期，如果为None则自动查询
            calendar_df: 交易日历数据，如果为None则自动获取
            
        Returns:
            Dict[str, Any]: 更新结果统计
        """
        try:
            # 加载指数代码
            sector_codes = self.sector_collector.load_stock_codes_from_json(sector_codes_path)
            
            # 调用指数数据收集器的每日更新方法
            return self.sector_collector.daily_update(
                stock_codes=sector_codes,
                latest_date_in_db=latest_date_in_db,
                end_date=latest_trade_date,
                calendar_df=calendar_df
            )
            
        except Exception as e:
            logger.error(f"每日指数数据更新失败: {str(e)}")
            traceback.print_exc()
            return {"error": str(e), "imported_count": 0}
    
    def daily_update_all_data(
        self, 
        stock_codes_path: str,
        index_codes_path: str,
        sector_codes_path: str,
        latest_trade_date: str, 
        latest_date_in_db: str, 
        calendar_df: pd.DataFrame
    ) -> Dict[str, Any]:
        """
        每日更新所有数据的主入口
        
        Args:
            stock_codes_path: 股票代码列表文件路径
            index_codes_path: 指数代码列表文件路径
            latest_trade_date: 最新交易日期，如果为None则使用昨天
            
        Returns:
            Dict[str, Any]: 所有数据类型的更新结果
        """
        

        self.daily_update_market_data(
            stock_codes_path, latest_trade_date, latest_date_in_db, calendar_df=calendar_df
        )
        self.daily_update_cyq_data(
            stock_codes_path, latest_trade_date, latest_date_in_db, calendar_df=calendar_df
        )
        self.daily_update_moneyflow_data(
            stock_codes_path, latest_trade_date, latest_date_in_db, calendar_df=calendar_df
        )
        self.daily_update_index_data(
            index_codes_path, latest_trade_date, latest_date_in_db, calendar_df=calendar_df
        )
        self.daily_update_minutes_data(
            stock_codes_path, latest_trade_date, latest_date_in_db, calendar_df=calendar_df
        )
        self.daily_update_sector_data(
            sector_codes_path, latest_trade_date, latest_date_in_db, calendar_df=calendar_df
        )
        
        
        