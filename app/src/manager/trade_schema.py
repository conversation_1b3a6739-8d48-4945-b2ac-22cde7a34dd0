from pydantic import BaseModel, validator, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from decimal import Decimal
import re
import json


class TradeSignalSchema(BaseModel):
    """交易信号验证模型"""
    strategy_name: str = Field(..., min_length=1, max_length=200, description="策略名称")
    timestamp: str = Field(..., description="时间戳，格式为'YYYY-MM-DD HH:MM:SS'")
    generated_time: str = Field(..., description="信号生成时间，格式为'YYYY-MM-DD HH:MM:SS'")
    stock_code: str = Field(..., min_length=1, max_length=20, description="股票代码，如'603938.SH'")
    order_type: int = Field(..., description="订单类型，必须为正整数")
    order_volume: int = Field(..., gt=0, description="订单数量，必须为正整数")
    last_close_price: float = Field(..., gt=0, description="上一交易日收盘价，必须为正数")

    @validator('stock_code')
    def validate_stock_code(cls, v):
        """验证股票代码格式"""
        if not v:
            raise ValueError('股票代码不能为空')
        
        # 基本格式验证：数字.交易所
        pattern = r'^[0-9a-zA-Z]+\.(SH|SZ|BJ)$'
        if not re.match(pattern, v.upper()):
            raise ValueError(f'股票代码格式不正确: {v}，正确格式如: 603938.SH')
        
        return v.upper()

    @validator('timestamp', 'generated_time')
    def validate_datetime_format(cls, v):
        """验证日期时间格式"""
        try:
            if ':' in v:
                datetime.strptime(v, '%Y-%m-%d %H:%M:%S')
            else:
                datetime.strptime(v, '%Y-%m-%d')
            return v
        except ValueError:
            raise ValueError(f'日期时间格式不正确: {v}，正确格式: YYYY-MM-DD HH:MM:SS 或 YYYY-MM-DD')

    @validator('last_close_price')
    def validate_price(cls, v):
        """验证价格合理性"""
        if v <= 0:
            raise ValueError('价格必须为正数')
        if v > 10000:  # 假设股价不会超过10000
            raise ValueError('价格不能超过10000')
        return round(v, 2)

    @validator('order_volume')
    def validate_volume(cls, v):
        """验证数量合理性"""
        if v <= 0:
            raise ValueError('订单数量必须为正整数')
        if v > 1000000:  # 假设单次订单量不超过100万股
            raise ValueError('订单数量不能超过1000000')
        return v

    def to_db_dict(self, strategy_id: int) -> dict:
        """转换为数据库插入格式"""
        # 提取交易日期
        dt = datetime.strptime(self.timestamp, '%Y-%m-%d %H:%M:%S' if ':' in self.timestamp else '%Y-%m-%d')
        trade_date = dt.strftime('%Y-%m-%d')
        
        return {
            'strategy_id': strategy_id,
            'trade_date': trade_date,
            'generated_time': self.generated_time,
            'stock_code': self.stock_code,
            'order_type': self.order_type,
            'order_volume': self.order_volume,
            'last_close_price': self.last_close_price
        }


class AccountSummarySchema(BaseModel):
    """账户概览验证模型"""
    account_id: str = Field(..., min_length=1, max_length=50, description="账户ID")
    total_asset: float = Field(..., ge=0, description="总资产，必须非负")
    market_value: float = Field(..., ge=0, description="持仓总市值，必须非负")
    cash: float = Field(..., ge=0, description="可用现金，必须非负")
    date: str = Field(..., description="数据快照日期，格式为'YYYY-MM-DD'")
    datetime: str = Field(..., description="数据快照时间，格式为'YYYY-MM-DD HH:MM:SS'")


    @validator('total_asset', 'market_value', 'cash')
    def validate_amounts(cls, v):
        """验证金额合理性"""
        if v < 0:
            raise ValueError('金额不能为负数')
        return round(v, 2)


    def to_db_dict(self) -> dict:
        """转换为数据库插入格式"""
        return {
            'account_id': self.account_id,
            'total_asset': Decimal(str(self.total_asset)),
            'market_value': Decimal(str(self.market_value)),
            'cash': Decimal(str(self.cash)),
            'date': self.date,
            'datetime': self.datetime
        }


class AccountPositionSchema(BaseModel):
    """账户持仓验证模型"""
    account_id: str = Field(..., min_length=1, max_length=50, description="账户ID")
    stock_code: str = Field(..., min_length=1, max_length=20, description="股票代码")
    volume: int = Field(..., ge=0, description="持仓股票数量，必须非负")
    can_use_volume: int = Field(..., ge=0, description="可用股票数量，必须非负")
    frozen_volume: int = Field(..., ge=0, description="冻结股票数量，必须非负")
    open_price: float = Field(..., gt=0, description="成本价，必须为正数")
    avg_price: float = Field(..., gt=0, description="平均成本价，必须为正数")
    market_value: float = Field(..., ge=0, description="持仓市值，必须非负")
    date: str = Field(..., description="数据快照日期，格式为'YYYY-MM-DD'")
    datetime: str = Field(..., description="数据快照时间，格式为'YYYY-MM-DD HH:MM:SS'")

    @validator('stock_code')
    def validate_stock_code(cls, v):
        """验证股票代码格式"""
        pattern = r'^[0-9]+\.(SH|SZ|BJ)$'
        if not re.match(pattern, v.upper()):
            raise ValueError(f'股票代码格式不正确: {v}，正确格式如: 603938.SH')
        return v.upper()

    @validator('volume')
    def validate_volume_consistency(cls, v, values):
        """验证持仓数量的一致性"""
        if 'can_use_volume' in values and 'frozen_volume' in values:
            expected_volume = values['can_use_volume'] + values['frozen_volume']
            if v != expected_volume:
                raise ValueError(f'持仓数量({v})应等于可用数量({values["can_use_volume"]}) + 冻结数量({values["frozen_volume"]})')
        return v

    @validator('open_price', 'avg_price')
    def validate_prices(cls, v):
        """验证价格合理性"""
        if v <= 0:
            raise ValueError('价格必须为正数')
        if v > 10000:
            raise ValueError('价格不能超过10000')
        return round(v, 2)

    def to_db_dict(self) -> dict:
        """转换为数据库插入格式"""
        return {
            'account_id': self.account_id,
            'stock_code': self.stock_code,
            'volume': self.volume,
            'can_use_volume': self.can_use_volume,
            'frozen_volume': self.frozen_volume,
            'open_price': Decimal(str(self.open_price)),
            'avg_price': Decimal(str(self.avg_price)),
            'market_value': Decimal(str(self.market_value)),
            'date': self.date,
            'datetime': self.datetime
        }


class TradeStrategySchema(BaseModel):
    """交易策略验证模型"""
    strategy_name: str = Field(..., min_length=1, max_length=200, description="策略名称")
    strategy_desc: Optional[str] = Field(None, max_length=1000, description="策略描述")
    factor_names: Optional[List[str]] = Field(None, description="因子名称列表")
    factor_expressions: Optional[List[str]] = Field(None, description="因子表达式列表")
    extra_params: Optional[Dict[str, Any]] = Field(None, description="额外参数")

    @validator('strategy_name')
    def validate_strategy_name(cls, v):
        """验证策略名称"""
        if not v or not v.strip():
            raise ValueError('策略名称不能为空')
        return v.strip()

    @validator('factor_expressions')
    def validate_factor_count_consistency(cls, v, values):
        """验证因子名称和表达式数量一致性"""
        if v is not None and 'factor_names' in values and values['factor_names'] is not None:
            if len(v) != len(values['factor_names']):
                raise ValueError(f'因子表达式数量({len(v)})必须与因子名称数量({len(values["factor_names"])})一致')
        return v

    def to_db_dict(self, current_time: str) -> dict:
        """转换为数据库插入格式"""
        
        return {
            'strategy_name': self.strategy_name,
            'strategy_desc': self.strategy_desc,
            'factor_names': json.dumps(self.factor_names, ensure_ascii=False) if self.factor_names else None,
            'factor_expressions': json.dumps(self.factor_expressions, ensure_ascii=False) if self.factor_expressions else None,
            'extra_params': json.dumps(self.extra_params, ensure_ascii=False) if self.extra_params else None,
            'created_at': current_time,
            'updated_at': None
        }


# 批量验证辅助函数
def validate_signals_batch(signals_data: List[dict]) -> tuple[List[TradeSignalSchema], List[str]]:
    """批量验证信号数据"""
    valid_signals = []
    errors = []
    
    for i, signal_data in enumerate(signals_data):
        try:
            signal_schema = TradeSignalSchema(**signal_data)
            valid_signals.append(signal_schema)
        except Exception as e:
            errors.append(f"Signal {i}: {str(e)}")
    
    return valid_signals, errors
