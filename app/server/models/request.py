from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Union


class CombinedDataRequest(BaseModel):
    stock_list: List[str]
    start_date: str
    end_date: str
    include_quotes: bool = True
    include_chips: bool = True
    include_money_flow: bool = True

class StockIndustryRequest(BaseModel):
    stock_list: List[str]  # 股票代码列表

class IndexDataRequest(BaseModel):
    index_list: List[str]  # 指数代码列表
    start_date: str  # 开始日期，格式为'YYYY-MM-DD'
    end_date: str  # 结束日期，格式为'YYYY-MM-DD'

class Stock5MinRequest(BaseModel):
    stock_code: str  # 股票代码，如 "600519.SH"
    start_date: str  # 开始日期，格式为'YYYY-MM-DD'
    end_date: str  # 结束日期，格式为'YYYY-MM-DD'

class SectorDataRequest(BaseModel):
    stock_list: Optional[List[str]] = None  # 板块代码列表，为None表示查询所有板块
    start_date: str  # 开始日期，格式为'YYYY-MM-DD'
    end_date: str  # 结束日期，格式为'YYYY-MM-DD'

# === 交易相关请求模型 ===

class InsertSignalRequest(BaseModel):
    """单条交易信号插入请求"""
    strategy_name: str  # 策略名称
    timestamp: str  # 时间戳，格式为'YYYY-MM-DD HH:MM:SS'
    generated_time: str  # 信号生成时间，格式为'YYYY-MM-DD HH:MM:SS'
    stock_code: str  # 股票代码，如'603938.SH'
    order_type: int  # 订单类型
    order_volume: int  # 订单数量
    last_close_price: float  # 上一交易日收盘价

class BatchInsertSignalsRequest(BaseModel):
    """批量交易信号插入请求"""
    signals: List[InsertSignalRequest]  # 信号列表

class InsertSummaryRequest(BaseModel):
    """账户概览插入请求"""
    account_id: str  # 账户ID
    total_asset: float  # 总资产
    market_value: float  # 持仓总市值
    cash: float  # 可用现金
    timestamp: str

class PositionDetail(BaseModel):
    """单个持仓详情"""
    stock_code: str  # 股票代码
    volume: int  # 持仓股票数量
    can_use_volume: int  # 可用股票数量
    frozen_volume: int  # 冻结股票数量
    open_price: float  # 成本价
    avg_price: float  # 平均成本价
    market_value: float  # 持仓市值

class InsertPositionRequest(BaseModel):
    """账户持仓插入请求"""
    account_id: str  # 账户ID
    positions: List[PositionDetail]  # 持仓列表
    timestamp: str  # 数据快照时间，格式为'YYYY-MM-DD HH:MM:SS'

class InsertStrategyRequest(BaseModel):
    """交易策略插入请求"""
    strategy_name: str  # 策略名称
    strategy_desc: Optional[str] = None  # 策略描述
    factor_names: Optional[List[str]] = None  # 因子名称列表
    factor_expressions: Optional[List[str]] = None  # 因子表达式列表
    extra_params: Optional[Dict] = None  # 额外参数


# === 查询相关请求模型 ===

class PaginationParams(BaseModel):
    """分页参数"""
    page: int = Field(default=1, ge=1, description="页码，从1开始")
    page_size: int = Field(default=20, ge=1, le=100, description="每页记录数，最大100")


class QueryStrategyByNameRequest(PaginationParams):
    """根据策略名称查询策略请求"""
    strategy_name: str  # 策略名称


class QueryStrategyByIdRequest(PaginationParams):
    """根据策略ID查询策略请求"""
    strategy_id: int = Field(ge=1, description="策略ID")


class QuerySignalsByStrategyRequest(PaginationParams):
    """根据策略名称查询信号请求"""
    strategy_name: str  # 策略名称


class QuerySignalsByStrategyAndDateRequest(PaginationParams):
    """根据策略名称和交易日期查询信号请求"""
    strategy_name: str  # 策略名称
    trade_date: str  # 交易日期，格式为'YYYY-MM-DD'


class QueryAccountSummaryRequest(PaginationParams):
    """查询账户概览请求"""
    account_id: str  # 账户ID
    date: Optional[str] = None  # 可选的日期过滤，格式为'YYYY-MM-DD'


class QueryAccountPositionsRequest(PaginationParams):
    """查询账户持仓请求"""
    account_id: str  # 账户ID
    date: Optional[str] = None  # 可选的日期过滤，格式为'YYYY-MM-DD'

