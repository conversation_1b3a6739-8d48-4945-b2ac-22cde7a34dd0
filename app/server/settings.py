"""application settings"""
from typing import Optional, List
import os
from pydantic_settings import BaseSettings
from pathlib import Path

BASE_DIR = Path(__file__).resolve().parent.parent.parent

class Settings(BaseSettings):
    # environment
    app_name: str = "SeekAlpha-Stock-Database"
    app_env: str = "dev"

    # basic
    app_port: int = 40023
    log_level: str = "INFO"
    log_dir: str = "./logs"

    # application
    version: str = "1.0.0"
    
    # CORS配置
    cors_origins: List[str] = ["*"]

    # ClickHouse配置
    clickhouse_host: str = ""
    clickhouse_port: int = 8123  # 使用HTTP端口，适配clickhouse-connect库
    clickhouse_user: str = ""
    clickhouse_password: str = ""
    clickhouse_database: str = ""

    # 缓存配置
    cache_enabled: bool = True
    cache_start_date: str = "2013-01-01"
    stock_codes_path: str = ""
    index_codes_path: str = ""
    sector_codes_path: str = ""

    # Tushare配置
    tushare_token: str = ""
    
    # MySQL配置
    mysql_host: str = "localhost"
    mysql_port: int = 3306
    mysql_user: str = ""
    mysql_password: str = ""
    mysql_database: str = "seekalpha"
    
    class Config:
        case_sensitive = False  # 允许用大写环境变量来覆盖小写字段名

def get_settings():
    app_env = os.getenv("APP_ENV", "dev")
    env_file = BASE_DIR / f".env.{app_env}"
    print(f"🔧 loading env file: {env_file}")
    return Settings(_env_file=env_file)


settings = get_settings()
