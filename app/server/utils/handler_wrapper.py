from typing import Text, List, Dict, Optional, Any, Union
from datetime import datetime
import traceback
import json
import asyncio
import time
from functools import wraps

from fastapi import Request, Response
from fastapi.responses import StreamingResponse

from app.server.core.errors import ErrorCode, failed_response
from app.server.core.log import logger
from app.server.settings import settings


def handler_exception_wrapper(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except BaseException as e:   
            response = failed_response(ErrorCode.SERVER_UNKNOWN_ERR)
            logger.error(f"error: {e}")
            logger.error(traceback.format_exc())
            logger.info(f"response: {response}")
            return response
    return wrapper

