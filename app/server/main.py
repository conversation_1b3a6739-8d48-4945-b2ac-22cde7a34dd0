"""main"""

import sys
import os
import json
import time
import threading
from datetime import datetime
import traceback

import pytz
import uvicorn
from fastapi import FastAPI

from app.server.api import router
from app.server.core.log import logger
from app.server.settings import settings
from app.src.manager.stock_data_manager import StockDataManager
from app.src.manager.daily_update_manager import DailyUpdateManager


from fastapi.middleware.cors import CORSMiddleware



app = FastAPI(title="SeekAlpha Center Control Serving API", version=settings.version)
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,  # 不再使用cookie，无需credentials
    allow_methods=["*"],  # 允许所有 HTTP 方法
    allow_headers=["*"],  # 允许所有 HTTP 头部
)

def daily_update_data():

    def _format_date(date_str: str) -> str:
        """格式化日期字符串"""
        if '-' in date_str:
            return datetime.strptime(date_str, '%Y-%m-%d').strftime('%Y%m%d')

        return datetime.strptime(date_str, '%Y%m%d').strftime('%Y%m%d')

    while True:    
        try:
            # 获取北京时间前一天
            yesterday = DailyUpdateManager.get_instance().get_beijing_yesterday()

            calendar_df = DailyUpdateManager.get_instance().get_or_update_trade_calendar(end_date=yesterday)

            trade_dates = calendar_df[calendar_df['is_open'] == 1]['cal_date'].astype(str).astype(int).astype(str).tolist()
            trade_dates.sort(reverse=True)
            latest_trade_date = _format_date(trade_dates[0])
            
            # 获取数据库最新日期
            latest_date_in_db = _format_date(StockDataManager.get_instance().get_latest_date_from_db('daily_quotes'))
 
            # 如果北京时间前一天和数据库最新日期不同，则更新数据
            if latest_trade_date != latest_date_in_db:
                # print(f"latest_trade_date: {latest_trade_date}, latest_date_in_db: {latest_date_in_db}")
            
                logger.info(f"开始更新数据，昨日前（包含昨日）的最新交易日: {latest_trade_date}, 数据库最新日期: {latest_date_in_db}")

                DailyUpdateManager.get_instance().daily_update_all_data(
                    stock_codes_path=settings.stock_codes_path,
                    index_codes_path=settings.index_codes_path,
                    sector_codes_path=settings.sector_codes_path,
                    latest_trade_date=latest_trade_date,
                    latest_date_in_db=latest_date_in_db, 
                    calendar_df=calendar_df
                )

                data_manager = StockDataManager.get_instance()

                # 更新数据库最新日期
                data_manager.update_latest_date_in_db()

                if settings.cache_enabled:
                    # 更新缓存
                    with open(settings.stock_codes_path, 'r', encoding='utf-8') as f:
                        stock_data = json.load(f)
                        stock_codes = stock_data.get('stock_codes')
                    data_manager.cache_combined_data(
                        start_date=settings.cache_start_date,
                        end_date=datetime.strptime(latest_trade_date, '%Y%m%d').strftime('%Y-%m-%d'),
                        stock_list=stock_codes
                    )
                    logger.info(f"股票缓存更新完成，共缓存 {len(stock_codes)} 只股票的数据")
                    
                    # 更新指数缓存
                    with open(settings.index_codes_path, 'r', encoding='utf-8') as f:
                        index_data = json.load(f)
                        index_codes = index_data.get('stock_codes')  # 复用字段名
                    data_manager.cache_index_data(
                        start_date=settings.cache_start_date,
                        end_date=datetime.strptime(latest_trade_date, '%Y%m%d').strftime('%Y-%m-%d'),
                        index_list=index_codes
                    )
                    logger.info(f"指数缓存更新完成，共缓存 {len(index_codes)} 个指数的数据")


            else:
                logger.info(f"无需更新数据，昨日前（包含昨日）的最新交易日: {latest_trade_date}, 数据库最新日期: {latest_date_in_db}")
        except Exception as e:
            logger.error(f"Daily update data failed: {e}")
            logger.error(traceback.format_exc())

        # 每20分钟检查一次
        time.sleep(1200)

@app.on_event("startup")
def start_daily_update_data():
    t = threading.Thread(target=daily_update_data, daemon=True)
    t.start()
    logger.info("Running daily update data in the background...")

class Server:
    def __init__(self):
        pass

    def init(self) -> bool:
        app.include_router(router)

        # 初始化StockDataManager
        logger.info("Initializing StockDataManager...")
        stock_data_manager = StockDataManager.get_instance()

        # 初始化DailyUpdateManager
        logger.info("Initializing DailyUpdateManager...")
        DailyUpdateManager.get_instance(stock_data_manager.clickhouse_client)

        # 预加载缓存
        if settings.cache_enabled:
            self._preload_cache()
        logger.info("Initializing SeekAlpha Stock Database...")
        return True
    
    def _preload_cache(self):
        """预加载缓存数据"""
        try:
            logger.info("开始预加载缓存数据...")
            data_manager = StockDataManager.get_instance()
            beijing_tz = pytz.timezone('Asia/Shanghai')
            beijing_now = datetime.now(beijing_tz)
            
            # 预加载股票数据缓存
            with open(settings.stock_codes_path, 'r', encoding='utf-8') as f:
                stock_data = json.load(f)
                stock_codes = stock_data.get('stock_codes')
            
            data_manager.cache_combined_data(
                start_date=settings.cache_start_date,
                end_date=beijing_now.strftime('%Y-%m-%d'),
                stock_list=stock_codes
            )
            
            logger.info(f"股票缓存预加载完成，共缓存 {len(stock_codes)} 只股票的数据")
            
            # 预加载指数数据缓存
            if settings.index_codes_path and os.path.exists(settings.index_codes_path):
                with open(settings.index_codes_path, 'r', encoding='utf-8') as f:
                    index_data = json.load(f)
                    index_codes = index_data.get('stock_codes')  # 复用字段名
                
                data_manager.cache_index_data(
                    start_date=settings.cache_start_date,
                    end_date=beijing_now.strftime('%Y-%m-%d'),
                    index_list=index_codes
                )
                
                logger.info(f"指数缓存预加载完成，共缓存 {len(index_codes)} 个指数的数据")
            else:
                logger.warning("未配置指数代码路径或文件不存在，跳过指数缓存预加载")
            
        except Exception as e:
            logger.error(f"缓存预加载失败: {e}")
            # 不影响服务启动

    def start(self):
        if self.init():
            uvicorn.run(app, host="0.0.0.0", port=settings.app_port)


if __name__ == "__main__":
    server = Server()
    server.start()


