"""api"""
import threading
import time
import traceback
from typing import Union, Optional
from datetime import datetime

import fastapi
from fastapi import APIRouter, File, UploadFile, Body, Query, Form, Request, Response
import numpy as np
import pandas as pd

from app.server.core.errors import ErrorCode, failed_response, success_data_response, success_response
from app.server.core.log import logger
from app.server.models.request import *
from app.server.models.response import *

from app.server.utils.handler_wrapper import handler_exception_wrapper
from app.src.manager.stock_data_manager import StockDataManager
from app.src.manager.daily_update_manager import DailyUpdateManager

router = APIRouter()




def format_date(date: str):
    if '-' in date:
        return datetime.strptime(date, '%Y-%m-%d').strftime('%Y-%m-%d')
    return datetime.strptime(date, '%Y%m%d').strftime('%Y-%m-%d')

# health
@router.get("/api/v1/data/health")
@handler_exception_wrapper
def health():
    """健康检查"""
    return {"status": "ok"}

@router.post("/api/v1/data/stock-industry-l1", response_model=Union[BaseResponse, ResponseData])
@handler_exception_wrapper
def get_stock_industry_l1(request: StockIndustryRequest):
    """根据股票列表获取一级行业信息（l1_code和l1_name）"""
    try:
        manager = StockDataManager.get_instance()
        
        # 获取股票一级行业信息
        result = manager.get_stock_industry_l1(request.stock_list)
        
        return success_data_response(data=result["data"])
        
    except Exception as e:
        logger.error(f"获取股票一级行业信息失败: {e}")
        return failed_response(
            code=1,
            message=f"获取数据失败: {str(e)}"
        )

@router.post("/api/v1/data/combined", response_model=Union[BaseResponse, ResponseData])
@handler_exception_wrapper
def get_combined_data(request: CombinedDataRequest):
    """获取股票综合数据(行情+筹码+资金流)"""
    try:
        manager = StockDataManager.get_instance()

        start_date = request.start_date
        end_date = request.end_date

        if start_date is not None:
            start_date = format_date(start_date)
            
        if end_date is not None:
            end_date = format_date(end_date)
            if end_date > manager.latest_date_in_db:
                end_date = manager.latest_date_in_db
    
        # 获取合并数据
        df = manager.get_combined_stock_data(
            start_date=start_date,
            end_date=end_date,
            stock_list=request.stock_list,
            batch_years=3,
            include_quotes=request.include_quotes,
            include_chips=request.include_chips,
            include_money_flow=request.include_money_flow
        )
        # 转换为字典列表
        df.replace({np.nan: None, pd.NaT: None}, inplace=True)
        df['trade_date'] = df['trade_date'].dt.strftime('%Y-%m-%d')
        # data = df.to_dict('records') if not df.empty else []
        # 改为数组返回，column名为key，列值为数组
        data = {}
        if not df.empty:
            for col in df.columns:
                data[col] = df[col].tolist()

        return success_data_response(data=data)
        
    except Exception as e:
        logger.error(f"获取合并数据失败: {e}")
        return failed_response(
            code=1,
            message=f"获取数据失败: {str(e)}"
        )

@router.post("/api/v1/data/index", response_model=Union[BaseResponse, ResponseData])
@handler_exception_wrapper
def get_index_data(request: IndexDataRequest):
    """获取指数数据"""
    try:
        manager = StockDataManager.get_instance()

        start_date = request.start_date
        end_date = request.end_date

        if start_date is not None:
            start_date = format_date(start_date)
            
        if end_date is not None:
            end_date = format_date(end_date)
            if end_date > manager.latest_date_in_db:
                end_date = manager.latest_date_in_db
    
        # 获取指数数据
        df = manager.get_daily_index(
            index_list=request.index_list,
            start_date=start_date,
            end_date=end_date,
            return_df=True
        )
        
        # 转换为字典列表
        df.replace({np.nan: None, pd.NaT: None}, inplace=True)
        df['trade_date'] = df['trade_date'].dt.strftime('%Y-%m-%d')
        
        # 按照现有模式返回数组格式，column名为key，列值为数组
        data = {}
        if not df.empty:
            for col in df.columns:
                data[col] = df[col].tolist()

        return success_data_response(data=data)
        
    except Exception as e:
        logger.error(f"获取指数数据失败: {e}")
        return failed_response(
            code=1,
            message=f"获取数据失败: {str(e)}"
        )

@router.post("/api/v1/data/stock-5min", response_model=Union[BaseResponse, ResponseData])
@handler_exception_wrapper
def get_stock_5min_data(request: Stock5MinRequest):
    """获取股票5分钟K线数据"""
    try:
        manager = StockDataManager.get_instance()

        start_date = request.start_date
        end_date = request.end_date

        if start_date is not None:
            start_date = format_date(start_date)
            
        if end_date is not None:
            end_date = format_date(end_date)
            # 检查结束日期不能超过数据库最新日期
            if end_date > manager.latest_date_in_db:
                end_date = manager.latest_date_in_db
    
        # 获取5分钟K线数据
        df = manager.get_stock_5min_data(
            stock_code=request.stock_code,
            start_date=start_date,
            end_date=end_date,
            return_df=True
        )
        
        # 转换为字典列表
        df.replace({np.nan: None, pd.NaT: None}, inplace=True)
        
        # 处理时间字段格式转换
        if 'trade_time' in df.columns:
            df['trade_time'] = df['trade_time'].dt.strftime('%Y-%m-%d %H:%M:%S')
        if 'trade_date' in df.columns:
            df['trade_date'] = df['trade_date'].dt.strftime('%Y-%m-%d')
        
        # 按照现有模式返回数组格式，column名为key，列值为数组
        data = {}
        if not df.empty:
            for col in df.columns:
                data[col] = df[col].tolist()

        return success_data_response(data=data)
        
    except Exception as e:
        logger.error(f"获取5分钟K线数据失败: {e}")
        return failed_response(
            code=1,
            message=f"获取数据失败: {str(e)}"
        )

@router.post("/api/v1/data/sector", response_model=Union[BaseResponse, ResponseData])
@handler_exception_wrapper
def get_sector_data(request: SectorDataRequest):
    """获取板块数据"""
    try:
        manager = StockDataManager.get_instance()

        start_date = request.start_date
        end_date = request.end_date

        if start_date is not None:
            start_date = format_date(start_date)
            
        if end_date is not None:
            end_date = format_date(end_date)
            if end_date > manager.latest_date_in_db:
                end_date = manager.latest_date_in_db
    
        # 获取板块数据
        df = manager.get_daily_sector_quotes(
            stock_list=request.stock_list,
            start_date=start_date,
            end_date=end_date,
            return_df=True
        )
        
        # 转换为字典列表
        df.replace({np.nan: None, pd.NaT: None}, inplace=True)
        df['trade_date'] = df['trade_date'].dt.strftime('%Y-%m-%d')
        
        # 按照现有模式返回数组格式，column名为key，列值为数组
        data = {}
        if not df.empty:
            for col in df.columns:
                data[col] = df[col].tolist()

        return success_data_response(
            data=data
        )
        
    except Exception as e:
        logger.error(f"获取板块数据失败: {e}")
        return failed_response(
            code=1,
            message=f"获取数据失败: {str(e)}"
        )