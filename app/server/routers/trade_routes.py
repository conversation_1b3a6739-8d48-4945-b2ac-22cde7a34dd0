

from fastapi import APIRouter, HTTPException
from typing import Union, Optional

from app.server.core.errors import ErrorCode, failed_response, success_response, success_data_response
from app.server.core.log import logger
from app.server.models.request import *
from app.server.models.response import BaseResponse, ResponseData

from app.server.utils.handler_wrapper import handler_exception_wrapper
from app.src.manager.trade_manager import TradeManager

router = APIRouter()

# 初始化交易管理器
trade_manager = TradeManager()

@router.post("/api/v1/trade/account-summary", response_model=Union[BaseResponse, ResponseData])
@handler_exception_wrapper
def insert_account_summary(request: InsertSummaryRequest):
    """插入账户概览数据"""
    try:
        # 转换请求数据为字典
        summary_data = request.dict()
        
        # 调用交易管理器插入数据
        result = trade_manager.insert_account_summary(summary_data)
        
        if result['success']:
            return success_response()
        else:
            return failed_response(
                code=ErrorCode.SERVER_INTERNAL_ERR,
                message=result['message']
            )
    except Exception as e:
        logger.error(f"Error in insert_account_summary: {e}")
        return failed_response(
            code=ErrorCode.SERVER_INTERNAL_ERR,
            message=f"插入账户概览数据失败: {str(e)}"
        )

@router.post("/api/v1/trade/account-positions", response_model=Union[BaseResponse, ResponseData])
@handler_exception_wrapper
def insert_account_positions(request: InsertPositionRequest):
    """批量插入账户持仓数据"""
    try:
        # 转换请求数据为字典
        position_data = request.dict()
        
        # 调用交易管理器插入数据
        result = trade_manager.insert_account_positions(position_data)
        
        if result['success']:
            return success_data_response(data={
                'total_provided': result.get('total_provided'),
                'successful_inserts': result.get('successful_inserts')
            })
        else:
            return failed_response(
                code=ErrorCode.SERVER_INTERNAL_ERR,
                message=result['message']
            )
    except Exception as e:
        logger.error(f"Error in insert_account_positions: {e}")
        return failed_response(
            code=ErrorCode.SERVER_INTERNAL_ERR,
            message=f"批量插入账户持仓数据失败: {str(e)}"
        )

@router.post("/api/v1/trade/strategy", response_model=Union[BaseResponse, ResponseData])
@handler_exception_wrapper
def insert_trade_strategy(request: InsertStrategyRequest):
    """插入交易策略"""
    try:
        # 转换请求数据为字典
        strategy_data = request.dict()
        
        # 调用交易管理器插入数据
        result = trade_manager.insert_trade_strategy(strategy_data)
        
        if result['success']:
            return success_response()
        else:
            return failed_response(
                code=ErrorCode.SERVER_INTERNAL_ERR,
                message=result['message']
            )
    except Exception as e:
        logger.error(f"Error in insert_trade_strategy: {e}")
        return failed_response(
            code=ErrorCode.SERVER_INTERNAL_ERR,
            message=f"插入交易策略失败: {str(e)}"
        )


@router.post("/api/v1/trade/signals", response_model=Union[BaseResponse, ResponseData])
@handler_exception_wrapper
def insert_trade_signals(request: BatchInsertSignalsRequest):
    """批量插入交易信号"""
    try:
        # 转换请求数据为字典列表
        signals_list = [signal.dict() for signal in request.signals]
        
        # 调用交易管理器批量插入数据
        result = trade_manager.insert_trade_signals(signals_list)
        
        if result['success']:
            return success_data_response(data={
                'total_provided': result['total_provided'],
                'successful_inserts': result['successful_inserts']
            })
        else:
            return failed_response(
                code=ErrorCode.SERVER_INTERNAL_ERR,
                message=result['message']
            )
    except Exception as e:
        logger.error(f"Error in batch_insert_trade_signals: {e}")
        return failed_response(
            code=ErrorCode.SERVER_INTERNAL_ERR,
            message=f"批量插入交易信号失败: {str(e)}"
        )


# ==================== 查询接口 ====================

@router.get("/api/v1/trade/strategy/by-name", response_model=Union[BaseResponse, ResponseData])
@handler_exception_wrapper
def get_strategy_by_name(strategy_name: str):
    """根据策略名称查询策略"""
    try:
        result = trade_manager.get_strategy_by_name(strategy_name)
        
        return success_data_response(data=result)

    except Exception as e:
        logger.error(f"Error in get_strategy_by_name: {e}")
        return failed_response(
            code=ErrorCode.SERVER_INTERNAL_ERR,
            message=f"查询策略失败: {str(e)}"
        )


@router.get("/api/v1/trade/strategy/by-id/{strategy_id}", response_model=Union[BaseResponse, ResponseData])
@handler_exception_wrapper
def get_strategy_by_id(strategy_id: int):
    """根据策略ID查询策略"""
    try:
        result = trade_manager.get_strategy_by_id(strategy_id)
        
        return success_data_response(data=result)
        
    except Exception as e:
        logger.error(f"Error in get_strategy_by_id: {e}")
        return failed_response(
            code=ErrorCode.SERVER_INTERNAL_ERR,
            message=f"查询策略失败: {str(e)}"
        )


@router.get("/api/v1/trade/signals/by-strategy", response_model=Union[BaseResponse, ResponseData])
@handler_exception_wrapper
def get_signals_by_strategy(strategy_name: str, page: Optional[int] = None, page_size: Optional[int] = None):
    """根据策略名称查询信号"""
    try:
        result = trade_manager.get_signals_by_strategy(strategy_name, page, page_size)
        
        if result['success']:
            return success_data_response(data={
                'items': result['data'],
                'pagination': result['pagination']
            })
        else:
            return failed_response(
                code=ErrorCode.SERVER_INTERNAL_ERR,
                message=result['message']
            )
    except Exception as e:
        logger.error(f"Error in get_signals_by_strategy: {e}")
        return failed_response(
            code=ErrorCode.SERVER_INTERNAL_ERR,
            message=f"查询交易信号失败: {str(e)}"
        )


@router.get("/api/v1/trade/signals/by-strategy-date", response_model=Union[BaseResponse, ResponseData])
@handler_exception_wrapper
def get_signals_by_strategy_and_date(strategy_name: str, trade_date: str, page: Optional[int] = None, page_size: Optional[int] = None):
    """根据策略名称和交易日期查询信号"""
    try:
        result = trade_manager.get_signals_by_strategy_and_date(strategy_name, trade_date, page, page_size)
        
        if result['success']:
            return success_data_response(data={
                'items': result['data'],
                'pagination': result['pagination']
            })
        else:
            return failed_response(
                code=ErrorCode.SERVER_INTERNAL_ERR,
                message=result['message']
            )
    except Exception as e:
        logger.error(f"Error in get_signals_by_strategy_and_date: {e}")
        return failed_response(
            code=ErrorCode.SERVER_INTERNAL_ERR,
            message=f"查询交易信号失败: {str(e)}"
        )


@router.get("/api/v1/trade/account-summary", response_model=Union[BaseResponse, ResponseData])
@handler_exception_wrapper
def get_account_summary(account_id: str, date: str = None, page: Optional[int] = None, page_size: Optional[int] = None):
    """查询账户概览"""
    try:
        result = trade_manager.get_account_summary(account_id, date, page, page_size)
        
        if result['success']:
            return success_data_response(data={
                'items': result['data'],
                'pagination': result['pagination']
            })
        else:
            return failed_response(
                code=ErrorCode.SERVER_INTERNAL_ERR,
                message=result['message']
            )
    except Exception as e:
        logger.error(f"Error in get_account_summary: {e}")
        return failed_response(
            code=ErrorCode.SERVER_INTERNAL_ERR,
            message=f"查询账户概览失败: {str(e)}"
        )


@router.get("/api/v1/trade/account-positions", response_model=Union[BaseResponse, ResponseData])
@handler_exception_wrapper
def get_account_positions(account_id: str, date: str = None, page: Optional[int] = None, page_size: Optional[int] = None):
    """查询账户持仓"""
    try:
        result = trade_manager.get_account_positions(account_id, date, page, page_size)
        
        if result['success']:
            return success_data_response(data={
                'items': result['data'],
                'pagination': result['pagination']
            })
        else:
            return failed_response(
                code=ErrorCode.SERVER_INTERNAL_ERR,
                message=result['message']
            )
    except Exception as e:
        logger.error(f"Error in get_account_positions: {e}")
        return failed_response(
            code=ErrorCode.SERVER_INTERNAL_ERR,
            message=f"查询账户持仓失败: {str(e)}"
        )