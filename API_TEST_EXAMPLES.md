# API 测试示例

## 股票一级行业信息接口

### 接口说明
- **URL**: `POST /api/v1/data/stock-industry-l1`
- **功能**: 根据股票列表获取一级行业代码（l1_code）和一级行业名称（l1_name）

### 请求参数
```json
{
    "stock_list": ["000001.SZ", "000002.SZ", "600000.SH"]
}
```

### 响应格式
```json
{
    "code": 0,
    "message": "Success",
    "data": [
        {
            "instrument_id": "000001.SZ",
            "l1_code": "801010",
            "l1_name": "银行"
        },
        {
            "instrument_id": "000002.SZ", 
            "l1_code": "801160",
            "l1_name": "房地产开发"
        }
    ]
}
```

### cURL 测试命令
```bash
curl -X POST "http://localhost:40023/api/v1/data/stock-industry-l1" \
     -H "Content-Type: application/json" \
     -d '{
         "stock_list": ["000001.SZ", "000002.SZ", "600000.SH", "600036.SH"]
     }'
```

### Python 测试代码
```python
import requests

url = "http://localhost:40023/api/v1/data/stock-industry-l1"
data = {
    "stock_list": ["000001.SZ", "000002.SZ", "600000.SH", "600036.SH"]
}

response = requests.post(url, json=data)
print(response.json())
```

## 错误处理

当股票列表为空时，接口会返回错误：
```json
{
    "code": 1,
    "message": "获取数据失败: 股票列表不能为空",
    "data": []
}
``` 