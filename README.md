# SeekAlpha-Stock-Database

> SeekAlpha股票数据服务

## ✨ 功能特性

- ✅ 支持下载中证500行情数据、筹码数据、资金流向数据、行业数据。
- ✅ 支持自定义日期范围
- ✅ 防重复下载机制

## 🚀 快速开始

### 📦 安装依赖

```bash
pip install -r requirements.txt
```

### 数据下载
### ⚙️ 配置设置

#### 1. 设置 Tushare Token

创建 `.env` 文件并添加您的 Tushare Pro Token：

```env
TUSHARE_TOKEN=YOUR_TUSHARE_TOKEN
```

> 💡 **提示**: 您可以在 [Tushare Pro官网](https://tushare.pro/) 申请免费的 API Token

#### 2. 配置数据库和存储参数

创建 `.env_sh` 文件并配置以下参数：

```bash
# 🗄️ ClickHouse 数据库配置
CLICKHOUSE_HOST=127.0.0.1
CLICKHOUSE_PORT=clickhouse端口
CLICKHOUSE_USER=clickhouse用户
CLICKHOUSE_PASSWORD=clickhouse密码
CLICKHOUSE_DATABASE=clickhouse数据库名称

# 📁 数据存储目录配置
## 股票数据CSV保存目录
STOCK_DATA_DIR=/path/to/stock_data

## 缓存目录
CACHE_DIR=/path/to/.cache

## 成分股列表缓存路径
STOCK_CODES_PATH=$CACHE_DIR/zz500_all_stocks_2013_2025.json
```

### 🎯 开始数据收集

运行以下命令开始下载数据（默认下载 2013-01-01 到 2025-06-16 的数据）：

```bash
bash scripts/create_zz500_database.sh
```

## 📊 数据说明

- **数据范围**: 默认为2013年1月1日 - 2025年6月16日，如有需要可自行在运行脚本进行调整
- **数据格式**: CSV 文件 + ClickHouse 数据库
- **覆盖范围**: 默认为中证500成分股

## 🔧 项目结构

```
SeekAlpha-Stock-Database/
├── 📁 docs/               # 文档目录
├── 📁 schema/             # 数据库表结构
├── 📁 scripts/            # 执行脚本
├── 📁 utils/              # 工具函数
├── 📄 stock_collector.py  # 主数据收集器
└── 📄 requirements.txt    # 依赖包列表
```

## ⚠️ 注意事项

- 确保 ClickHouse 数据库服务正常运行
- Tushare Pro API 有调用频率限制，请合理安排下载任务
- 建议在服务器环境下运行，以保证数据下载的稳定性


