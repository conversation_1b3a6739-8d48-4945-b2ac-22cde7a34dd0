#!/usr/bin/env python3
"""
测试分页功能修改的脚本
验证当page或page_size为None时，返回全部数据而不进行分页
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.src.manager.trade_manager import TradeManager

def test_pagination_changes():
    """测试分页功能的修改"""
    
    # 创建TradeManager实例
    trade_manager = TradeManager()
    
    print("=== 测试分页功能修改 ===\n")
    
    # 测试1: 正常分页 (page=1, page_size=5)
    print("1. 测试正常分页 (page=1, page_size=5):")
    result1 = trade_manager.get_signals_by_strategy("test_strategy", page=1, page_size=5)
    if result1['success']:
        pagination1 = result1.get('pagination', {})
        print(f"   - 分页信息: page={pagination1.get('page')}, page_size={pagination1.get('page_size')}")
        print(f"   - 是否分页: {pagination1.get('is_paginated', 'N/A')}")
        print(f"   - 总记录数: {pagination1.get('total')}")
        print(f"   - 返回记录数: {len(result1.get('data', []))}")
    else:
        print(f"   - 查询失败: {result1.get('message')}")
    
    print()
    
    # 测试2: page为None的情况
    print("2. 测试page为None的情况:")
    result2 = trade_manager.get_signals_by_strategy("test_strategy", page=None, page_size=5)
    if result2['success']:
        pagination2 = result2.get('pagination', {})
        print(f"   - 分页信息: page={pagination2.get('page')}, page_size={pagination2.get('page_size')}")
        print(f"   - 是否分页: {pagination2.get('is_paginated', 'N/A')}")
        print(f"   - 总记录数: {pagination2.get('total')}")
        print(f"   - 返回记录数: {len(result2.get('data', []))}")
    else:
        print(f"   - 查询失败: {result2.get('message')}")
    
    print()
    
    # 测试3: page_size为None的情况
    print("3. 测试page_size为None的情况:")
    result3 = trade_manager.get_signals_by_strategy("test_strategy", page=1, page_size=None)
    if result3['success']:
        pagination3 = result3.get('pagination', {})
        print(f"   - 分页信息: page={pagination3.get('page')}, page_size={pagination3.get('page_size')}")
        print(f"   - 是否分页: {pagination3.get('is_paginated', 'N/A')}")
        print(f"   - 总记录数: {pagination3.get('total')}")
        print(f"   - 返回记录数: {len(result3.get('data', []))}")
    else:
        print(f"   - 查询失败: {result3.get('message')}")
    
    print()
    
    # 测试4: 两个参数都为None的情况
    print("4. 测试两个参数都为None的情况:")
    result4 = trade_manager.get_signals_by_strategy("test_strategy", page=None, page_size=None)
    if result4['success']:
        pagination4 = result4.get('pagination', {})
        print(f"   - 分页信息: page={pagination4.get('page')}, page_size={pagination4.get('page_size')}")
        print(f"   - 是否分页: {pagination4.get('is_paginated', 'N/A')}")
        print(f"   - 总记录数: {pagination4.get('total')}")
        print(f"   - 返回记录数: {len(result4.get('data', []))}")
    else:
        print(f"   - 查询失败: {result4.get('message')}")
    
    print()
    
    # 测试5: 测试按策略和日期查询的方法
    print("5. 测试按策略和日期查询 (page=None, page_size=None):")
    result5 = trade_manager.get_signals_by_strategy_and_date("test_strategy", "2024-01-01", page=None, page_size=None)
    if result5['success']:
        pagination5 = result5.get('pagination', {})
        print(f"   - 分页信息: page={pagination5.get('page')}, page_size={pagination5.get('page_size')}")
        print(f"   - 是否分页: {pagination5.get('is_paginated', 'N/A')}")
        print(f"   - 总记录数: {pagination5.get('total')}")
        print(f"   - 返回记录数: {len(result5.get('data', []))}")
    else:
        print(f"   - 查询失败: {result5.get('message')}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_pagination_changes()
