import clickhouse_connect
from typing import Text, Dict, List, Optional, Any, Union, Tuple
import json
import traceback
import threading
import time
from contextlib import contextmanager
import logging
import pandas as pd

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ClickHouseClient:
    _instance = None
    _instance_lock = threading.Lock()
    _pool = []  # 存储可用连接
    _active_connections = 0  # 当前活跃连接数
    _pool_lock = threading.Lock()
    
    def __init__(
        self,
        host: Text = "localhost",
        port: int = 8123,  # clickhouse-connect默认使用HTTP端口8123
        user: Text = "default",
        password: Text = "",
        database: Text = "default",
        max_connections: int = 50,  # 最大连接数
        min_cached: int = 5,  # 最小空闲连接数
        max_cached: int = 20,  # 最大空闲连接数
        max_usage: Optional[int] = 100,  # 单个连接最大使用次数
        connection_timeout: int = 30,  # 连接超时时间(秒)
        idle_timeout: int = 600  # 空闲连接超时时间(秒)
    ):
        """
        初始化ClickHouse客户端
        
        Args:
            host: 主机名
            port: 端口号 (HTTP端口，默认8123)
            user: 用户名
            password: 密码
            database: 数据库名
            max_connections: 最大连接数
            min_cached: 最小空闲连接数
            max_cached: 最大空闲连接数
            max_usage: 单个连接最大使用次数，None表示无限制
            connection_timeout: 连接超时时间(秒)
            idle_timeout: 空闲连接超时时间(秒)
        """
        self.connection_params = {
            "host": host,
            "port": port,
            "username": user,
            "password": password,
            "database": database,
            "connect_timeout": connection_timeout
        }
        self.pool_params = {
            "max_connections": max_connections,
            "min_cached": min_cached,
            "max_cached": max_cached,
            "max_usage": max_usage,
            "idle_timeout": idle_timeout
        }
        self._init_pool()
    
    def _init_pool(self):
        """初始化连接池，创建最小数量的空闲连接"""
        with self._pool_lock:
            # 创建初始连接
            for _ in range(self.pool_params["min_cached"]):
                try:
                    client = self._create_connection()
                    if client:
                        self._pool.append({
                            "client": client,
                            "created_at": time.time(),
                            "last_used": time.time(),
                            "usage_count": 0
                        })
                except Exception as e:
                    logger.error(f"Error initializing connection pool: {e}")
                    logger.error(traceback.format_exc())
    
    def _create_connection(self):
        """创建新的连接"""
        try:
            client = clickhouse_connect.get_client(**self.connection_params)
            self._active_connections += 1
            logger.info(f"Created new ClickHouse connection, active connections: {self._active_connections}")
            return client
        except Exception as e:
            logger.error(f"Error creating ClickHouse client: {e}")
            logger.error(traceback.format_exc())
            return None
    
    @classmethod
    def get_instance(cls, **kwargs):
        """单例模式获取实例"""
        if cls._instance is None:
            with cls._instance_lock:
                if cls._instance is None:
                    cls._instance = cls(**kwargs)
        return cls._instance
    
    def _check_connection(self, client):
        """检查连接是否有效"""
        try:
            client.command("SELECT 1")
            return True
        except Exception:
            return False
    
    def _clean_idle_connections(self):
        """清理空闲超时的连接"""
        now = time.time()
        idle_timeout = self.pool_params["idle_timeout"]
        
        with self._pool_lock:
            active_connections = []
            for conn_info in self._pool:
                # 检查连接是否超时
                if now - conn_info["last_used"] > idle_timeout:
                    try:
                        conn_info["client"].close()
                        self._active_connections -= 1
                        logger.info(f"Closed idle connection, active connections: {self._active_connections}")
                    except Exception as e:
                        logger.error(f"Error closing idle connection: {e}")
                else:
                    active_connections.append(conn_info)
            
            self._pool = active_connections
    
    def _get_connection(self):
        """从连接池获取连接"""
        self._clean_idle_connections()
        
        with self._pool_lock:
            # 尝试从池中获取连接
            if self._pool:
                conn_info = self._pool.pop(0)
                client = conn_info["client"]
                
                # 验证连接是否有效
                if not self._check_connection(client):
                    try:
                        client.close()
                    except:
                        pass
                    self._active_connections -= 1
                    
                    # 创建新连接替代无效连接
                    client = self._create_connection()
                    if not client:
                        raise Exception("Failed to create replacement connection")
                
                # 更新连接信息
                conn_info = {
                    "client": client,
                    "created_at": conn_info["created_at"],
                    "last_used": time.time(),
                    "usage_count": conn_info["usage_count"] + 1
                }
                
                # 检查连接是否超过最大使用次数
                max_usage = self.pool_params["max_usage"]
                if max_usage and conn_info["usage_count"] >= max_usage:
                    logger.info(f"Connection reached max usage ({max_usage}), will create new one after use")
                    return conn_info, True  # 标记为使用后需要关闭
                
                return conn_info, False
            
            # 如果没有可用连接且未达到最大连接数，创建新连接
            if self._active_connections < self.pool_params["max_connections"]:
                client = self._create_connection()
                if client:
                    conn_info = {
                        "client": client,
                        "created_at": time.time(),
                        "last_used": time.time(),
                        "usage_count": 1
                    }
                    return conn_info, False
            
            # 如果达到最大连接数，抛出异常
            raise Exception(f"Connection pool exhausted, max_connections={self.pool_params['max_connections']}")
    
    def _return_connection(self, conn_info, force_close=False):
        """归还连接到连接池"""
        client = conn_info["client"]
        
        with self._pool_lock:
            # 决定是否关闭连接
            should_close = force_close
            
            # 检查是否超过最大使用次数
            max_usage = self.pool_params["max_usage"]
            if max_usage and conn_info["usage_count"] >= max_usage:
                should_close = True
            
            # 检查是否超过最大空闲连接数
            if len(self._pool) >= self.pool_params["max_cached"]:
                should_close = True
            
            if should_close:
                try:
                    client.close()
                    self._active_connections -= 1
                    logger.info(f"Closed connection, active connections: {self._active_connections}")
                except Exception as e:
                    logger.error(f"Error closing connection: {e}")
            else:
                # 更新最后使用时间并放回池中
                conn_info["last_used"] = time.time()
                self._pool.append(conn_info)
    
    @contextmanager
    def get_client(self):
        """上下文管理器，获取客户端并在完成后释放"""
        conn_info = None
        force_close = False
        
        try:
            conn_info, force_close = self._get_connection()
            yield conn_info["client"]
        except Exception as e:
            logger.error(f"Error using ClickHouse client: {e}")
            logger.error(traceback.format_exc())
            force_close = True  # 发生错误时强制关闭连接
            raise
        finally:
            if conn_info:
                self._return_connection(conn_info, force_close)
    
    def execute(self, query: Text, params: Dict = None, return_df: bool = False) -> Union[List, 'pd.DataFrame']:
        """
        执行SQL查询
        
        Args:
            query: SQL查询语句
            params: 查询参数
            return_df: 是否返回DataFrame，默认False返回字典列表
            
        Returns:
            查询结果（字典格式或DataFrame）
        """
        try:
            with self.get_client() as client:
                # 对于SELECT查询，根据return_df参数决定返回格式
                if query.strip().upper().startswith("SELECT"):
                    # start_time = time.time()
                    
                    if return_df:
                        # 直接返回DataFrame
                        result_df = client.query_df(
                            query, 
                            parameters=params or {}
                        )
                        # logger.info(f"query_df 执行时间: {time.time() - start_time} 秒")
                        return result_df
                    else:
                        # 原有逻辑：返回字典列表
                        result = client.query(
                            query, 
                            parameters=params or {}
                        )
                        # logger.info(f"query 执行时间: {time.time() - start_time} 秒")
                        # 将结果转换为字典列表
                        dict_results = []
                        for row in result.result_rows:
                            dict_results.append(dict(zip(result.column_names, row)))

                        return dict_results
                else:
                    # 对于非SELECT查询，直接执行
                    result = client.command(query, parameters=params or {})
                    return result
        except Exception as e:
            logger.error(f"Error executing query: {e}")
            logger.error(f"Query: {query}")
            logger.error(f"Params: {params}")
            raise
    
    def insert(self, table: Text, data: Dict) -> bool:
        """
        向表中插入数据
        
        Args:
            table: 表名
            data: 数据字典
            
        Returns:
            是否成功插入
        """
        try:
            with self.get_client() as client:
                # 使用clickhouse-connect的insert方法
                client.insert(
                    table=table,
                    data=[list(data.values())],
                    column_names=list(data.keys())
                )
                return True
        except Exception as e:
            logger.error(f"Error inserting data: {e}")
            return False
    
    def insert_many(self, table: Text, data_list: List[Dict]) -> bool:
        """
        批量插入数据
        
        Args:
            table: 表名
            data_list: 数据字典列表
            
        Returns:
            是否成功插入
        """
        if not data_list:
            return True
            
        try:
            # 确保所有字典具有相同的键
            keys = data_list[0].keys()
            for data in data_list:
                if set(data.keys()) != set(keys):
                    raise ValueError("All dictionaries must have the same keys")
            
            # 转换为行数据
            rows = []
            for data in data_list:
                rows.append([data[key] for key in keys])
            
            with self.get_client() as client:
                client.insert(
                    table=table,
                    data=rows,
                    column_names=list(keys)
                )
                return True
        except Exception as e:
            logger.error(f"Error inserting multiple data: {e}")
            return False

    @contextmanager
    def transaction(self):
        """事务上下文管理器"""
        with self.get_client() as client:
            try:
                client.command("BEGIN TRANSACTION")
                yield client
                client.command("COMMIT")
            except Exception as e:
                client.command("ROLLBACK")
                raise
            
    def _build_conditions(self, where: Dict = None, complex_conditions: List[Dict] = None) -> tuple:
        """
        构建查询条件的公共方法
        
        Args:
            where: 简单查询条件
            complex_conditions: 复杂查询条件
            
        Returns:
            (where_clause, params) 元组
        """
        where_clause = ""
        params = {}
        conditions = []
        
        # 处理简单条件
        if where:
            for i, (column, value) in enumerate(where.items()):
                param_name = f"where_{i}"
                conditions.append(f"{column} = %(where_{i})s")
                params[param_name] = value
        
        # 处理复杂条件
        if complex_conditions:
            for i, condition in enumerate(complex_conditions):
                column = condition["column"]
                operator = condition["operator"]
                value = condition["value"]
                param_name = f"complex_{i}"
                
                # 处理IN操作符
                if operator.upper() == "IN":
                    if not isinstance(value, list) or not value:
                        continue
                    
                    placeholders = []
                    for j, item in enumerate(value):
                        item_param = f"{param_name}_{j}"
                        placeholders.append(f"%(complex_{i}_{j})s")
                        params[item_param] = item
                    
                    conditions.append(f"{column} IN ({', '.join(placeholders)})")
                # 处理BETWEEN操作符
                elif operator.upper() == "BETWEEN" and isinstance(value, list) and len(value) == 2:
                    start_param = f"{param_name}_start"
                    end_param = f"{param_name}_end"
                    
                    conditions.append(f"{column} BETWEEN %(complex_{i}_start)s AND %(complex_{i}_end)s")
                    params[start_param] = value[0]
                    params[end_param] = value[1]
                # 处理其他操作符 (>, <, >=, <=, !=)
                else:
                    conditions.append(f"{column} {operator} %(complex_{i})s")
                    params[param_name] = value
        
        # 组合所有条件
        if conditions:
            where_clause = "WHERE " + " AND ".join(conditions)
            
        return where_clause, params

    def count(
        self, 
        table: Text, 
        where: Dict = None,
        complex_conditions: List[Dict] = None
    ) -> int:
        """
        高效计算表中符合条件的记录数
        
        Args:
            table: 表名
            where: 查询条件，格式为 {column: value}
            complex_conditions: 复杂查询条件，格式同select方法
            
        Returns:
            符合条件的记录数
        """
        try:
            where_clause, params = self._build_conditions(where, complex_conditions)
            
            # 构建计数查询
            query = f"""
            SELECT COUNT(*) as total_count
            FROM {table}
            {where_clause}
            """
            
            result = self.execute(query, params)
            return result[0]['total_count'] if result else 0
            
        except Exception as e:
            logger.error(f"Error counting records: {e}")
            return 0

    def select(
        self, 
        table: Text, 
        columns: List[Text] = None, 
        where: Dict = None, 
        order_by: List[Tuple[Text, Text]] = None,
        limit: int = None,
        offset: int = None,
        complex_conditions: List[Dict] = None,
        return_count: bool = False  # 新增参数，是否同时返回总数
    ) -> Union[List[Dict], tuple]:
        """
        从表中查询数据
        
        Args:
            table: 表名
            columns: 要查询的列，默认为所有列
            where: 查询条件，格式为 {column: value}
            order_by: 排序条件，格式为 [(column, 'ASC'), (column, 'DESC')]
            limit: 限制返回的记录数
            offset: 偏移量
            complex_conditions: 复杂查询条件
            return_count: 是否同时返回总数，用于分页
            
        Returns:
            查询结果（字典列表）或 (查询结果, 总数) 元组
        """
        try:
            where_clause, params = self._build_conditions(where, complex_conditions)
            
            # 构建SELECT部分
            select_clause = "*" if not columns else ", ".join(columns)
            
            # 构建ORDER BY部分
            order_clause = ""
            if order_by:
                orders = [f"{column} {direction}" for column, direction in order_by]
                order_clause = "ORDER BY " + ", ".join(orders)
            
            # 构建LIMIT和OFFSET部分
            limit_clause = f"LIMIT {limit}" if limit is not None else ""
            offset_clause = f"OFFSET {offset}" if offset is not None else ""
            
            # 如果需要返回总数，先执行计数查询
            total_count = None
            if return_count:
                total_count = self.count(table, where, complex_conditions)
            
            # 构建完整查询
            query = f"""
            SELECT {select_clause}
            FROM {table}
            {where_clause}
            {order_clause}
            {limit_clause}
            {offset_clause}
            """
            
            result = self.execute(query, params)
            
            if return_count:
                return result, total_count
            else:
                return result
                
        except Exception as e:
            logger.error(f"Error selecting data: {e}")
            if return_count:
                return [], 0
            else:
                return []
    
    def select_one(
        self, 
        table: Text, 
        columns: List[Text] = None, 
        where: Dict = None, 
        order_by: List[Tuple[Text, Text]] = None
    ) -> Optional[Dict]:
        """
        从表中查询单条数据
        
        Args:
            table: 表名
            columns: 要查询的列，默认为所有列
            where: 查询条件，格式为 {column: value}
            order_by: 排序条件，格式为 [(column, 'ASC'), (column, 'DESC')]
            
        Returns:
            查询结果的第一行，如果没有结果则返回None
        """
        try:
            results = self.select(table, columns, where, order_by, limit=1)
            return results[0] if results else None
        except Exception as e:
            logger.error(f"Error selecting one row: {e}")
            return None
    
    def update(self, table: Text, data: Dict, where: Dict) -> bool:
        """
        更新表中的数据
        
        Args:
            table: 表名
            data: 要更新的数据，格式为 {column: value}
            where: 更新条件，格式为 {column: value}
            
        Returns:
            是否成功更新
        """
        try:
            # 构建SET部分
            set_items = []
            params = {}
            
            for i, (column, value) in enumerate(data.items()):
                param_name = f"set_{i}"
                set_items.append(f"{column} = %(set_{i})s")
                params[param_name] = value
            
            set_clause = ", ".join(set_items)
            
            # 构建WHERE部分
            where_items = []
            for i, (column, value) in enumerate(where.items()):
                param_name = f"where_{i}"
                where_items.append(f"{column} = %(where_{i})s")
                params[param_name] = value
            
            where_clause = " AND ".join(where_items)
            
            # 构建完整查询
            query = f"""
            ALTER TABLE {table}
            UPDATE {set_clause}
            WHERE {where_clause}
            """
            
            self.execute(query, params)
            return True
        except Exception as e:
            logger.error(f"Error updating data: {e}")
            return False
    
    def delete(self, table: Text, where: Dict) -> bool:
        """
        删除表中的数据
        
        Args:
            table: 表名
            where: 删除条件，格式为 {column: value}
            
        Returns:
            是否成功删除
        """
        try:
            # 构建WHERE部分
            where_items = []
            params = {}
            
            for i, (column, value) in enumerate(where.items()):
                param_name = f"where_{i}"
                where_items.append(f"{column} = %(where_{i})s")
                params[param_name] = value
            
            where_clause = " AND ".join(where_items)
            
            # 构建完整查询
            query = f"""
            ALTER TABLE {table}
            DELETE WHERE {where_clause}
            """
            
            self.execute(query, params)
            return True
        except Exception as e:
            logger.error(f"Error deleting data: {e}")
            return False
    
    def create_table(self, query: Text) -> bool:
        """
        创建表
        
        Args:
            query: 创建表的SQL语句
            
        Returns:
            是否成功创建
        """
        try:
            self.execute(query)
            return True
        except Exception as e:
            logger.error(f"Error creating table: {e}")
            return False
    
    def drop_table(self, table: Text) -> bool:
        """
        删除表
        
        Args:
            table: 表名
            
        Returns:
            是否成功删除
        """
        try:
            self.execute(f"DROP TABLE IF EXISTS {table}")
            return True
        except Exception as e:
            logger.error(f"Error dropping table: {e}")
            return False

    def get_pool_status(self) -> Dict:
        """
        获取连接池状态信息
        
        Returns:
            连接池状态信息
        """
        with self._pool_lock:
            return {
                "active_connections": self._active_connections,
                "idle_connections": len(self._pool),
                "max_connections": self.pool_params["max_connections"],
                "min_cached": self.pool_params["min_cached"],
                "max_cached": self.pool_params["max_cached"]
            }

    def close(self):
        """关闭所有连接池中的连接"""
        with self._pool_lock:
            for conn_info in self._pool:
                try:
                    conn_info["client"].close()
                    logger.info(f"Closed ClickHouse client in pool")
                except Exception as e:
                    logger.error(f"Error closing ClickHouse client: {e}")
            
            self._pool.clear()
            self._active_connections = 0
            logger.info("All ClickHouse connections closed")
    
    def __del__(self):
        """析构函数，确保所有连接都被关闭"""
        self.close()