#!/usr/bin/env python3
import os
import clickhouse_connect


def execute_sql_script(
    sql_script_path, 
    host, 
    port, 
    user, 
    password, 
    database
):
    with open(sql_script_path, 'r') as file:
        sql_script = file.read()


    sql_statements = [sql_script,]
    # 连接到ClickHouse
    try:
        client = clickhouse_connect.get_client(
            host=host, 
            port=port, 
            username=user, 
            password=password, 
            database=database
        )
        print(f"已连接到ClickHouse: {host}:{port}, 数据库: {database}")
        
        # 逐条执行SQL语句
        for i, stmt in enumerate(sql_statements):
            try:
                print(f"执行语句 {i+1}/{len(sql_statements)}: {stmt[:50]}...")
                client.command(stmt)
                print(f"语句 {i+1} 执行成功")
            except Exception as e:
                print(f"执行语句 {i+1} 失败: {e}")
                if "already exists" in str(e):
                    print("对象已存在，继续执行...")
                    continue
                if input("是否继续执行其他语句? (y/n): ").lower() != 'y':
                    break
        
        print("所有SQL语句执行完成")
    except Exception as e:
        print(f"连接或执行过程中发生错误: {e}")


if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser()
    parser.add_argument("--host", type=str, required=True)
    parser.add_argument("--port", type=int, required=True)
    parser.add_argument("--user", type=str, required=True)
    parser.add_argument("--password", type=str, required=True)
    parser.add_argument("--database", type=str, required=True)
    parser.add_argument("--sql_script_path", type=str, required=True)
    args = parser.parse_args()
    execute_sql_script(args.sql_script_path, args.host, args.port, args.user, args.password, args.database)