from typing import List, Dict, Optional, Tuple
import logging

logger = logging.getLogger(__name__)

class SQLBuilder:
    """SQL 查询构建器，专门处理带计算字段的复杂查询"""
    
    def build_calculated_field_query(
        self,
        table: str,
        base_fields: List[str],
        calculated_field_mapping: Dict[str, str],
        columns: Optional[List[str]] = None,
        where_conditions: Optional[List[str]] = None,
        order_by: Optional[List[Tuple[str, str]]] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        sql_type_cast: Optional[Dict[str, str]] = None
    ) -> Tuple[str, str]:
        """
        构建带计算字段的查询SQL
        
        Args:
            table: 表名
            base_fields: 基础字段列表
            calculated_field_mapping: 计算字段映射，格式为 {field_name: "expression AS field_name"}
            columns: 需要查询的列，为None表示查询所有列
            where_conditions: WHERE 条件列表
            order_by: 排序条件，格式为 [(column, 'ASC'), (column, 'DESC')]
            limit: 限制返回的记录数
            offset: 偏移量
            sql_type_cast: SQL类型转换映射，格式为 {field_name: "CAST(...) AS field_name"}
            
        Returns:
            (data_query, count_query) 元组
        """
        # 构建 SELECT 字段
        select_fields = self._build_select_fields(
            base_fields, calculated_field_mapping, columns, sql_type_cast
        )
        
        # 构建其他 SQL 部分
        where_clause = self._build_where_clause(where_conditions) if where_conditions else ""
        order_clause = self._build_order_clause(order_by) if order_by else ""
        limit_clause = self._build_limit_clause(limit, offset)
        
        # 数据查询
        data_query = f"""
        SELECT {select_fields}
        FROM {table}
        {where_clause}
        {order_clause}
        {limit_clause}
        """.strip()
        
        # 计数查询
        count_query = f"""
        SELECT COUNT(*) as total_count
        FROM {table}
        {where_clause}
        """.strip()
        
        return data_query, count_query
    
    def _build_select_fields(
        self, 
        base_fields: List[str], 
        calculated_mapping: Dict[str, str], 
        columns: Optional[List[str]],
        sql_type_cast: Optional[Dict[str, str]] = None
    ) -> str:
        """构建 SELECT 字段列表"""
        type_cast_mapping = sql_type_cast or {}
        
        if columns is None:
            # 查询所有字段（基础字段 + 计算字段）
            all_base_fields = []
            for field in base_fields:
                # 优先使用类型转换版本，如果有的话
                all_base_fields.append(type_cast_mapping.get(field, field))
            
            # 对于计算字段，默认转换为Nullable(Float64)（如schema中找不到的字段）
            all_calculated_fields = []
            for calc_field_name, calc_expression in calculated_mapping.items():
                # 计算字段通常不在schema中，默认转换为Nullable(Float64)以处理可能的NULL值
                cast_expression = f"CAST(({calc_expression.split(' AS ')[0]}) AS Nullable(Float64)) AS {calc_field_name}"
                all_calculated_fields.append(cast_expression)
            
            return ", ".join(all_base_fields + all_calculated_fields)
        
        select_fields = []
        for col in columns:
            if col in calculated_mapping:
                # 这是一个计算字段，使用计算表达式并转换为Nullable(Float64)
                calc_expression = calculated_mapping[col]
                cast_expression = f"CAST(({calc_expression.split(' AS ')[0]}) AS Nullable(Float64)) AS {col}"
                select_fields.append(cast_expression)
            elif col in base_fields:
                # 这是一个基础字段，优先使用类型转换版本
                select_fields.append(type_cast_mapping.get(col, col))
            else:
                # 未知字段，直接添加（可能是用户自定义或其他有效字段）
                select_fields.append(col)
                
        return ", ".join(select_fields)
    
    def _build_where_clause(self, where_conditions: List[str]) -> str:
        """构建 WHERE 子句"""
        if not where_conditions:
            return ""
        return "WHERE " + " AND ".join(where_conditions)
    
    def _build_order_clause(self, order_by: List[Tuple[str, str]]) -> str:
        """构建 ORDER BY 子句"""
        if not order_by:
            return ""
        
        order_items = []
        for column, direction in order_by:
            order_items.append(f"{column} {direction}")
        
        return "ORDER BY " + ", ".join(order_items)
    
    def _build_limit_clause(self, limit: Optional[int], offset: Optional[int]) -> str:
        """构建 LIMIT 和 OFFSET 子句"""
        clauses = []
        
        if limit is not None:
            clauses.append(f"LIMIT {limit}")
            
        if offset is not None:
            clauses.append(f"OFFSET {offset}")
            
        return " ".join(clauses) 