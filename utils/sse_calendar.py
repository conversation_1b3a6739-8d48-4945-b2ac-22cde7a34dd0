import os
import tushare as ts
import pandas as pd



def get_trade_calendar(
    exchange='SSE', 
    start_date=None, 
    end_date=None, 
    output_file=None, 
    is_open='1',
    token=None
):
    """
    获取交易日历数据
    
    Args:
        exchange (str): 交易所代码，默认SSE（上交所）
        start_date (str): 开始日期，格式YYYYMMDD
        end_date (str): 结束日期，格式YYYYMMDD
        output_file (str): 输出CSV文件路径
    
    Returns:
        pd.DataFrame: 交易日历数据
    """
    # 从环境变量获取tushare token
    token = os.getenv('TUSHARE_TOKEN') if token is None else token
    if not token:
        raise ValueError("请设置TUSHARE_TOKEN环境变量")
    
    ts.set_token(token)
    pro = ts.pro_api(token)
    
    try:
        print(f"正在获取{exchange}交易日历数据...")
        print(f"时间范围: {start_date} - {end_date}")
        
        # 调用交易日历接口
        df = pro.trade_cal(
            exchange=exchange,
            start_date=start_date,
            end_date=end_date,
            is_open=is_open
        )
        
        if df.empty:
            print("未获取到数据，请检查参数设置")
            return None
            
        print(f"成功获取{len(df)}条记录")
        
        # 数据预览
        print("\n数据预览:")
        print(df.head(10))
        
        # 统计信息
        trading_days = len(df[df['is_open'] == 1])
        non_trading_days = len(df[df['is_open'] == 0])
        print(f"\n统计信息:")
        print(f"交易日: {trading_days}天")
        print(f"休市日: {non_trading_days}天")
        print(f"总计: {len(df)}天")
        
        # 保存为CSV文件
        if output_file:
            # 确保输出目录存在
            output_dir = os.path.dirname(output_file)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True)
            
            # 确保文件扩展名为.csv
            if not output_file.endswith('.csv'):
                output_file += '.csv'
                
            df.to_csv(output_file, index=False, encoding='utf-8')
            print(f"\n数据已保存至: {output_file}")
        
        return df
        
    except Exception as e:
        print(f"获取数据时发生错误: {str(e)}")
        return None
