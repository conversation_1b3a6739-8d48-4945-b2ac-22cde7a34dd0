import pymysql
from pymysql.cursors import DictCursor
from typing import Text, Dict, List, Optional, Any, Union, Tuple
import json
import traceback
from contextlib import contextmanager
from dbutils.pooled_db import PooledDB
from app.server.core.log import logger

class MySQLClient:
    _instance = None
    _pool = None
    
    def __init__(
        self,
        host: Text = "localhost",
        port: int = 3306,
        user: Text = "",
        password: Text = "",
        database: Text = ""
    ):
        """
        初始化MySQL客户端
        
        Args:
            host: 主机名
            port: 端口号
            user: 用户名
            password: 密码
            database: 数据库名
        """
        self.connection_params = {
            "host": host,
            "port": port,
            "user": user,
            "password": password,
            "database": database,
            "cursorclass": DictCursor  # 使用DictCursor替代普通游标
        }
        self._init_pool()
    
    def _init_pool(self):
        """初始化连接池"""
        if MySQLClient._pool is None:
            try:
                MySQLClient._pool = PooledDB(
                    creator=pymysql,  # 使用pymysql作为数据库连接库
                    maxconnections=50,  # 连接池允许的最大连接数
                    mincached=3,  # 初始化时，连接池中至少创建的空闲连接数
                    maxcached=5,  # 连接池中允许的最大空闲连接数
                    maxshared=5,  # 共享连接的最大数量
                    blocking=True,  # 连接池中如果没有可用连接后，是否阻塞等待
                    maxusage=None,  # 一个连接最多被重复使用的次数，None表示无限制
                    setsession=[],  # 开始会话前执行的命令列表
                    **self.connection_params
                )
                logger.info("MySQL connection pool initialized")
            except Exception as e:
                logger.error(f"Error initializing MySQL connection pool: {e}")
                logger.error(traceback.format_exc())
                raise
    
    @classmethod
    def get_instance(cls, **kwargs):
        """单例模式获取实例"""
        if cls._instance is None:
            cls._instance = cls(**kwargs)
        return cls._instance
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接"""
        conn = None
        try:
            conn = self._pool.connection()
            yield conn
        except Exception as e:
            logger.error(f"Error getting connection from pool: {e}")
            logger.error(traceback.format_exc())
            if conn:
                conn.rollback()
            raise
        finally:
            if conn:
                conn.close()
    
    @contextmanager
    def get_cursor(self):
        """获取数据库游标"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            try:
                yield cursor
            finally:
                cursor.close()
    
    def execute(self, query: Text, params: Union[Dict, List, Tuple] = None) -> Union[List[Dict], int]:
        """
        执行SQL查询
        
        Args:
            query: SQL查询语句
            params: 查询参数，可以是字典、列表或元组
            
        Returns:
            对于SELECT查询，返回结果列表；对于其他查询，返回影响的行数
        """
        try:
            with self.get_cursor() as cursor:
                affected_rows = cursor.execute(query, params or {})
                
                # 对于SELECT查询，返回结果
                if query.strip().upper().startswith("SELECT") or query.strip().upper().startswith("SHOW"):
                    return cursor.fetchall()  # 使用DictCursor时，这里返回的是字典列表
                
                # 对于非SELECT查询，返回影响的行数
                cursor.connection.commit()
                return affected_rows
        except Exception as e:
            logger.error(f"Error executing query: {e}")
            logger.error(f"Query: {query}")
            logger.error(f"Params: {params}")
            logger.error(traceback.format_exc())
            raise
    
    def insert(self, table: Text, data: Dict) -> int:
        """
        向表中插入数据
        
        Args:
            table: 表名
            data: 数据字典
            
        Returns:
            插入的行ID，如果失败则返回0
        """
        try:
            columns = ", ".join(f"`{k}`" for k in data.keys())
            placeholders = ", ".join(["%s"] * len(data))
            
            query = f"""
            INSERT INTO `{table}` ({columns})
            VALUES ({placeholders})
            """
            
            with self.get_cursor() as cursor:
                cursor.execute(query, list(data.values()))
                cursor.connection.commit()
                if cursor.rowcount == 1:
                    if 'id' in data:
                        return data['id']
                    else:
                        return cursor.lastrowid  # 返回数据库自动生成的主键ID
                else:
                    return 0
        except Exception as e:
            logger.error(f"Error inserting data: {e}")
            logger.error(traceback.format_exc())
            return 0
    
    def insert_many(self, table: Text, data_list: List[Dict]) -> int:
        """
        批量插入数据
        
        Args:
            table: 表名
            data_list: 数据字典列表
            
        Returns:
            成功插入的行数
        """
        if not data_list:
            return 0
            
        try:
            # 确保所有字典具有相同的键
            keys = list(data_list[0].keys())
            for data in data_list:
                if set(data.keys()) != set(keys):
                    raise ValueError("All dictionaries must have the same keys")
            
            columns = ", ".join(f"`{k}`" for k in keys)
            placeholders = ", ".join(["%s"] * len(keys))
            
            query = f"""
            INSERT INTO `{table}` ({columns})
            VALUES ({placeholders})
            """
            
            # 准备数据
            values = []
            for data in data_list:
                row = [data[key] for key in keys]
                values.append(row)
            
            with self.get_cursor() as cursor:
                affected_rows = cursor.executemany(query, values)
                cursor.connection.commit()
                return affected_rows
        except Exception as e:
            logger.error(f"Error inserting multiple data: {e}")
            logger.error(traceback.format_exc())
            return 0
    
    def select(
        self, 
        table: Text, 
        columns: List[Text] = None, 
        where: Dict = None, 
        order_by: List[Tuple[Text, Text]] = None,
        limit: int = None,
        offset: int = None,
        joins: List[Dict] = None,
        group_by: List[Text] = None,
        having: Dict = None
    ) -> List[Dict]:
        """
        从表中查询多条数据
        
        Args:
            table: 表名
            columns: 要查询的列，默认为所有列
            where: 查询条件，格式为 {column: value}
            order_by: 排序条件，格式为 [(column, 'ASC'), (column, 'DESC')]
            limit: 限制返回的记录数
            offset: 偏移量
            joins: 连接条件，格式为 [{'type': 'INNER', 'table': 't2', 'on': 't1.id = t2.id'}]
            group_by: 分组列
            having: 分组筛选条件，格式为 {column: value}
            
        Returns:
            查询结果列表
        """
        try:
            # 构建SELECT部分
            select_clause = "*" if not columns else ", ".join(f"`{col}`" for col in columns)
            
            # 构建JOIN部分
            join_clause = ""
            if joins:
                for join in joins:
                    join_type = join.get('type', 'INNER')
                    join_table = join.get('table')
                    join_on = join.get('on')
                    
                    if join_table and join_on:
                        join_clause += f" {join_type} JOIN `{join_table}` ON {join_on}"
            
            # 构建WHERE部分
            where_clause = ""
            params = []
            if where:
                conditions = []
                for column, value in where.items():
                    conditions.append(f"`{column}` = %s")
                    params.append(value)
                
                if conditions:
                    where_clause = "WHERE " + " AND ".join(conditions)
            
            # 构建GROUP BY部分
            group_clause = ""
            if group_by:
                group_columns = ", ".join(f"`{col}`" for col in group_by)
                group_clause = f"GROUP BY {group_columns}"
            
            # 构建HAVING部分
            having_clause = ""
            if having:
                having_conditions = []
                for column, value in having.items():
                    having_conditions.append(f"`{column}` = %s")
                    params.append(value)
                
                if having_conditions:
                    having_clause = "HAVING " + " AND ".join(having_conditions)
            
            # 构建ORDER BY部分
            order_clause = ""
            if order_by:
                orders = [f"`{column}` {direction}" for column, direction in order_by]
                order_clause = "ORDER BY " + ", ".join(orders)
            
            # 构建LIMIT和OFFSET部分
            limit_clause = f"LIMIT {limit}" if limit is not None else ""
            offset_clause = f"OFFSET {offset}" if offset is not None else ""
            
            # 构建完整查询
            query = f"""
            SELECT {select_clause}
            FROM `{table}`
            {join_clause}
            {where_clause}
            {group_clause}
            {having_clause}
            {order_clause}
            {limit_clause}
            {offset_clause}
            """
            
            return self.execute(query, params)
        except Exception as e:
            logger.error(f"Error selecting data: {e}")
            logger.error(traceback.format_exc())
            return []
    
    def select_one(
        self, 
        table: Text, 
        columns: List[Text] = None, 
        where: Dict = None, 
        order_by: List[Tuple[Text, Text]] = None,
        joins: List[Dict] = None
    ) -> Optional[Dict]:
        """
        从表中查询单条数据
        
        Args:
            table: 表名
            columns: 要查询的列，默认为所有列
            where: 查询条件，格式为 {column: value}
            order_by: 排序条件，格式为 [(column, 'ASC'), (column, 'DESC')]
            joins: 连接条件，格式为 [{'type': 'INNER', 'table': 't2', 'on': 't1.id = t2.id'}]
            
        Returns:
            单条记录，如果没有找到则返回None
        """
        try:
            # 构建SELECT部分
            select_clause = "*" if not columns else ", ".join(f"`{col}`" for col in columns)
            
            # 构建JOIN部分
            join_clause = ""
            if joins:
                for join in joins:
                    join_type = join.get('type', 'INNER')
                    join_table = join.get('table')
                    join_on = join.get('on')
                    
                    if join_table and join_on:
                        join_clause += f" {join_type} JOIN `{join_table}` ON {join_on}"
            
            # 构建WHERE部分
            where_clause = ""
            params = []
            if where:
                conditions = []
                for column, value in where.items():
                    conditions.append(f"`{column}` = %s")
                    params.append(value)
                
                if conditions:
                    where_clause = "WHERE " + " AND ".join(conditions)
            
            # 构建ORDER BY部分
            order_clause = ""
            if order_by:
                orders = [f"`{column}` {direction}" for column, direction in order_by]
                order_clause = "ORDER BY " + ", ".join(orders)
            
            # 构建完整查询，限制为1条记录
            query = f"""
            SELECT {select_clause}
            FROM `{table}`
            {join_clause}
            {where_clause}
            {order_clause}
            LIMIT 1
            """
            
            with self.get_cursor() as cursor:
                cursor.execute(query, params)
                return cursor.fetchone()  # 使用DictCursor时，这里返回的是字典
        except Exception as e:
            logger.error(f"Error selecting one record: {e}")
            logger.error(f"Query: {query if 'query' in locals() else 'Not constructed'}")
            logger.error(f"Params: {params if 'params' in locals() else []}")
            logger.error(traceback.format_exc())
            return None
    
    def select_in(
        self, 
        table: Text, 
        column: Text,
        values: List[Any],
        columns: List[Text] = None, 
        additional_where: Dict = None, 
        order_by: List[Tuple[Text, Text]] = None,
        limit: int = None,
        offset: int = None,
        joins: List[Dict] = None,
        group_by: List[Text] = None,
        having: Dict = None
    ) -> List[Dict]:
        """
        查询某个字段值在给定列表中的记录
        
        Args:
            table: 表名
            column: 要查询的字段名
            values: 字段可能的值列表
            columns: 要返回的列，默认为所有列
            additional_where: 附加的查询条件，格式为 {column: value}
            order_by: 排序条件，格式为 [(column, 'ASC'), (column, 'DESC')]
            limit: 限制返回的记录数
            offset: 偏移量
            joins: 连接条件，格式为 [{'type': 'INNER', 'table': 't2', 'on': 't1.id = t2.id'}]
            group_by: 分组列
            having: 分组筛选条件，格式为 {column: value}
            
        Returns:
            查询结果列表
        """
        if not values:
            return []  # 如果values为空，直接返回空列表
            
        try:
            # 构建SELECT部分
            select_clause = "*" if not columns else ", ".join(f"`{col}`" for col in columns)
            
            # 构建JOIN部分
            join_clause = ""
            if joins:
                for join in joins:
                    join_type = join.get('type', 'INNER')
                    join_table = join.get('table')
                    join_on = join.get('on')
                    
                    if join_table and join_on:
                        join_clause += f" {join_type} JOIN `{join_table}` ON {join_on}"
            
            # 构建IN条件部分
            params = []
            in_placeholders = ", ".join(["%s"] * len(values))
            in_clause = f"`{column}` IN ({in_placeholders})"
            params.extend(values)
            
            # 构建附加WHERE条件部分
            where_clause = f"WHERE {in_clause}"
            if additional_where:
                for where_col, where_val in additional_where.items():
                    where_clause += f" AND `{where_col}` = %s"
                    params.append(where_val)
            
            # 构建GROUP BY部分
            group_clause = ""
            if group_by:
                group_columns = ", ".join(f"`{col}`" for col in group_by)
                group_clause = f"GROUP BY {group_columns}"
            
            # 构建HAVING部分
            having_clause = ""
            if having:
                having_conditions = []
                for having_col, having_val in having.items():
                    having_conditions.append(f"`{having_col}` = %s")
                    params.append(having_val)
                
                if having_conditions:
                    having_clause = "HAVING " + " AND ".join(having_conditions)
            
            # 构建ORDER BY部分
            order_clause = ""
            if order_by:
                orders = [f"`{column}` {direction}" for column, direction in order_by]
                order_clause = "ORDER BY " + ", ".join(orders)
            
            # 构建LIMIT和OFFSET部分
            limit_clause = f"LIMIT {limit}" if limit is not None else ""
            offset_clause = f"OFFSET {offset}" if offset is not None else ""
            
            # 构建完整查询
            query = f"""
            SELECT {select_clause}
            FROM `{table}`
            {join_clause}
            {where_clause}
            {group_clause}
            {having_clause}
            {order_clause}
            {limit_clause}
            {offset_clause}
            """
            
            return self.execute(query, params)
        except Exception as e:
            logger.error(f"Error executing select_in query: {e}")
            logger.error(f"Column: {column}, Values: {values}")
            logger.error(traceback.format_exc())
            return []
    
    def update(self, table: Text, data: Dict, where: Dict) -> int:
        """
        更新表中的数据
        
        Args:
            table: 表名
            data: 要更新的数据，格式为 {column: value}
            where: 更新条件，格式为 {column: value}
            
        Returns:
            更新的行数
        """
        try:
            # 构建SET部分
            set_items = []
            params = []
            
            for column, value in data.items():
                set_items.append(f"`{column}` = %s")
                params.append(value)
            
            set_clause = ", ".join(set_items)
            
            # 构建WHERE部分
            where_items = []
            for column, value in where.items():
                where_items.append(f"`{column}` = %s")
                params.append(value)
            
            where_clause = " AND ".join(where_items)
            
            # 构建完整查询
            query = f"""
            UPDATE `{table}`
            SET {set_clause}
            WHERE {where_clause}
            """
            
            with self.get_cursor() as cursor:
                affected_rows = cursor.execute(query, params)
                cursor.connection.commit()
                return affected_rows
        except Exception as e:
            logger.error(f"Error updating data: {e}")
            logger.error(traceback.format_exc())
            return 0
    
    def delete(self, table: Text, where: Dict) -> int:
        """
        删除表中的数据
        
        Args:
            table: 表名
            where: 删除条件，格式为 {column: value}
            
        Returns:
            删除的行数
        """
        try:
            # 构建WHERE部分
            where_items = []
            params = []
            
            for column, value in where.items():
                where_items.append(f"`{column}` = %s")
                params.append(value)
            
            where_clause = " AND ".join(where_items)
            
            # 构建完整查询
            query = f"""
            DELETE FROM `{table}`
            WHERE {where_clause}
            """
            
            with self.get_cursor() as cursor:
                affected_rows = cursor.execute(query, params)
                cursor.connection.commit()
                return affected_rows
        except Exception as e:
            logger.error(f"Error deleting data: {e}")
            logger.error(traceback.format_exc())
            return 0
    
    def create_table(self, table: Text, columns: Dict[Text, Text], primary_key: Text = None, 
                     foreign_keys: Dict[Text, Dict] = None, engine: Text = "InnoDB") -> bool:
        """
        创建表
        
        Args:
            table: 表名
            columns: 列定义，格式为 {column_name: column_type}
            primary_key: 主键列名
            foreign_keys: 外键定义，格式为 {column_name: {'ref_table': table, 'ref_column': column}}
            engine: 存储引擎
            
        Returns:
            是否成功创建
        """
        try:
            # 构建列定义
            column_defs = []
            for col_name, col_type in columns.items():
                column_defs.append(f"`{col_name}` {col_type}")
            
            # 添加主键定义
            if primary_key:
                column_defs.append(f"PRIMARY KEY (`{primary_key}`)")
            
            # 添加外键定义
            if foreign_keys:
                for fk_col, fk_def in foreign_keys.items():
                    ref_table = fk_def.get('ref_table')
                    ref_column = fk_def.get('ref_column')
                    if ref_table and ref_column:
                        column_defs.append(
                            f"FOREIGN KEY (`{fk_col}`) REFERENCES `{ref_table}`(`{ref_column}`)"
                        )
            
            # 构建完整查询
            query = f"""
            CREATE TABLE IF NOT EXISTS `{table}` (
                {', '.join(column_defs)}
            ) ENGINE={engine}
            """
            
            self.execute(query)
            return True
        except Exception as e:
            logger.error(f"Error creating table: {e}")
            logger.error(traceback.format_exc())
            return False
    
    def drop_table(self, table: Text, if_exists: bool = True) -> bool:
        """
        删除表
        
        Args:
            table: 表名
            if_exists: 是否仅在表存在时删除
            
        Returns:
            是否成功删除
        """
        try:
            exists_clause = "IF EXISTS" if if_exists else ""
            query = f"DROP TABLE {exists_clause} `{table}`"
            self.execute(query)
            return True
        except Exception as e:
            logger.error(f"Error dropping table: {e}")
            logger.error(traceback.format_exc())
            return False
    
    def truncate_table(self, table: Text) -> bool:
        """
        清空表数据
        
        Args:
            table: 表名
            
        Returns:
            是否成功清空
        """
        try:
            query = f"TRUNCATE TABLE `{table}`"
            self.execute(query)
            return True
        except Exception as e:
            logger.error(f"Error truncating table: {e}")
            logger.error(traceback.format_exc())
            return False
    
    def execute_transaction(self, queries: List[Dict]) -> bool:
        """
        执行事务
        
        Args:
            queries: 查询列表，每个查询为字典 {'query': sql, 'params': params}
            
        Returns:
            事务是否成功
        """
        with self.get_connection() as conn:
            try:
                # 关闭自动提交
                conn.begin()
                
                with conn.cursor() as cursor:
                    for query_dict in queries:
                        sql = query_dict.get('query')
                        params = query_dict.get('params', None)
                        cursor.execute(sql, params)
                
                # 提交事务
                conn.commit()
                return True
            except Exception as e:
                # 回滚事务
                conn.rollback()
                logger.error(f"Error executing transaction: {e}")
                logger.error(traceback.format_exc())
                return False
    
    def show_tables(self) -> List[Dict]:
        """
        显示所有表
        
        Returns:
            表列表
        """
        try:
            return self.execute("SHOW TABLES")
        except Exception as e:
            logger.error(f"Error showing tables: {e}")
            logger.error(traceback.format_exc())
            return []
    
    def describe_table(self, table: Text) -> List[Dict]:
        """
        描述表结构
        
        Args:
            table: 表名
            
        Returns:
            表结构信息
        """
        try:
            return self.execute(f"DESCRIBE `{table}`")
        except Exception as e:
            logger.error(f"Error describing table: {e}")
            logger.error(traceback.format_exc())
            return []
    
    def select_columns(
        self, 
        table: Text, 
        columns: List[Text], 
        where: Dict = None, 
        order_by: List[Tuple[Text, Text]] = None,
        limit: int = None,
        offset: int = None
    ) -> List[Dict]:
        """
        从表中只查询指定的列
        
        Args:
            table: 表名
            columns: 要查询的列
            where: 查询条件，格式为 {column: value}
            order_by: 排序条件，格式为 [(column, 'ASC'), (column, 'DESC')]
            limit: 限制返回的记录数
            offset: 偏移量
            
        Returns:
            查询结果列表
        """
        try:
            if not columns or len(columns) == 0:
                raise ValueError("Columns list cannot be empty")
                
            # 构建SELECT部分
            select_clause = ", ".join(f"`{col}`" for col in columns)
            
            # 构建WHERE部分
            where_clause = ""
            params = []
            if where:
                conditions = []
                for column, value in where.items():
                    conditions.append(f"`{column}` = %s")
                    params.append(value)
                
                if conditions:
                    where_clause = "WHERE " + " AND ".join(conditions)
            
            # 构建ORDER BY部分
            order_clause = ""
            if order_by:
                orders = [f"`{column}` {direction}" for column, direction in order_by]
                order_clause = "ORDER BY " + ", ".join(orders)
            
            # 构建LIMIT和OFFSET部分
            limit_clause = f"LIMIT {limit}" if limit is not None else ""
            offset_clause = f"OFFSET {offset}" if offset is not None else ""
            
            # 构建完整查询
            query = f"""
            SELECT {select_clause}
            FROM `{table}`
            {where_clause}
            {order_clause}
            {limit_clause}
            {offset_clause}
            """
            
            return self.execute(query, params)
        except Exception as e:
            logger.error(f"Error selecting specific columns: {e}")
            logger.error(traceback.format_exc())
            return []
    
    def select_column_value(
        self, 
        table: Text, 
        column: Text, 
        where: Dict = None
    ) -> Any:
        """
        从表中查询单个字段的值
        
        Args:
            table: 表名
            column: 要查询的列
            where: 查询条件，格式为 {column: value}
            
        Returns:
            字段值，如果没有找到则返回None
        """
        try:
            # 构建WHERE部分
            where_clause = ""
            params = []
            if where:
                conditions = []
                for col, value in where.items():
                    conditions.append(f"`{col}` = %s")
                    params.append(value)
                
                if conditions:
                    where_clause = "WHERE " + " AND ".join(conditions)
            
            # 构建完整查询
            query = f"""
            SELECT `{column}`
            FROM `{table}`
            {where_clause}
            LIMIT 1
            """
            
            with self.get_cursor() as cursor:
                cursor.execute(query, params)
                result = cursor.fetchone()
                return result[column] if result else None
        except Exception as e:
            logger.error(f"Error selecting column value: {e}")
            logger.error(traceback.format_exc())
            return None
    
    def exists(self, table: Text, where: Dict) -> bool:
        """
        快速检查指定条件的记录是否存在
        
        Args:
            table: 表名
            where: 查询条件，格式为 {column: value}
            
        Returns:
            记录是否存在
        """
        try:
            # 构建WHERE部分
            where_clause = ""
            params = []
            if where:
                conditions = []
                for col, value in where.items():
                    conditions.append(f"`{col}` = %s")
                    params.append(value)
                
                if conditions:
                    where_clause = "WHERE " + " AND ".join(conditions)
            else:
                return False  # 如果没有条件，认为不存在
            
            # 构建高效的存在性检查查询
            query = f"""
            SELECT 1
            FROM `{table}`
            {where_clause}
            LIMIT 1
            """
            
            with self.get_cursor() as cursor:
                cursor.execute(query, params)
                return cursor.fetchone() is not None
        except Exception as e:
            logger.error(f"Error checking existence: {e}")
            logger.error(traceback.format_exc())
            return False
    
    def close(self):
        """关闭连接池"""
        if MySQLClient._pool:
            try:
                MySQLClient._pool.close()
                MySQLClient._pool = None
                logger.info("MySQL connection pool closed")
            except Exception as e:
                logger.error(f"Error closing MySQL connection pool: {e}")
                logger.error(traceback.format_exc())
    
    def __del__(self):
        """析构函数，确保连接池被关闭"""
        self.close()